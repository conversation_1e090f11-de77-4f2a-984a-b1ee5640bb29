import PeopleIcon from '@mui/icons-material/People';
import SettingsIcon from '@mui/icons-material/Settings';
import MenuIcon from '@mui/icons-material/Menu';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleOutlineIcon from '@mui/icons-material/PeopleOutline';
import BusinessIcon from '@mui/icons-material/Business';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import BuildIcon from '@mui/icons-material/Build';
import DevicesOtherIcon from '@mui/icons-material/DevicesOther';
import EngineeringIcon from '@mui/icons-material/Engineering';
import DescriptionIcon from '@mui/icons-material/Description';
import ReceiptIcon from '@mui/icons-material/Receipt';
import InventoryIcon from '@mui/icons-material/Inventory';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import BarChartIcon from '@mui/icons-material/BarChart';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import {
    AppBar,
    Box,
    ListItemIcon,
    ListItemText,
    MenuItem,
    Tab,
    Tabs,
    Toolbar,
    Typography,
    Drawer,
    IconButton,
    List,
    ListItem,
    ListItemButton,
    useMediaQuery,
    useTheme,
} from '@mui/material';
import {
    CanAccess,
    LoadingIndicator,
    Logout,
    UserMenu,
    useUserMenu,
} from 'react-admin';
import { Link, matchPath, useLocation } from 'react-router-dom';
import { useConfigurationContext } from '../root/ConfigurationContext';
import { useState } from 'react';

const Header = () => {
    const { logo, title } = useConfigurationContext();
    const location = useLocation();
    const theme = useTheme();
    const isLargeScreen = useMediaQuery(theme.breakpoints.up('md'));
    const [drawerOpen, setDrawerOpen] = useState(false);

    const handleDrawerToggle = () => {
        setDrawerOpen(!drawerOpen);
    };

    const navigationItems = [
        { label: 'Dashboard', path: '/', icon: <DashboardIcon /> },
        { label: 'Klienci', path: '/klienci', icon: <PeopleOutlineIcon /> },
        { label: 'Firmy', path: '/firmy', icon: <BusinessIcon /> },
        { label: 'Sprzedaż', path: '/sprzedaz', icon: <ShoppingCartIcon /> },
        { label: 'Serwis', path: '/serwis', icon: <BuildIcon /> },
        { label: 'Urządzenia', path: '/urzadzenia', icon: <DevicesOtherIcon /> },
        { label: 'Technicy', path: '/technicy', icon: <EngineeringIcon /> },
        { label: 'Oferty', path: '/oferty', icon: <DescriptionIcon /> },
        { label: 'Faktury', path: '/faktury', icon: <ReceiptIcon /> },
        { label: 'Części', path: '/czesci', icon: <InventoryIcon /> },
        { label: 'Konserwacja', path: '/konserwacja', icon: <BuildIcon /> },
        { label: 'Kalendarz', path: '/kalendarz', icon: <CalendarTodayIcon /> },
        { label: 'Finanse', path: '/finanse', icon: <AttachMoneyIcon /> },
        { label: 'Raporty', path: '/raporty', icon: <BarChartIcon /> },
        { label: 'AI Asystent', path: '/ai_asystent', icon: <SmartToyIcon /> },
        { label: 'Administracja', path: '/administracja', icon: <AdminPanelSettingsIcon /> },
    ];

    let currentPath: string | boolean = '/';
    if (!!matchPath('/', location.pathname)) {
        currentPath = '/';
    } else if (!!matchPath('/klienci/*', location.pathname)) {
        currentPath = '/klienci';
    } else if (!!matchPath('/firmy/*', location.pathname)) {
        currentPath = '/firmy';
    } else if (!!matchPath('/sprzedaz/*', location.pathname)) {
        currentPath = '/sprzedaz';
    } else if (!!matchPath('/serwis/*', location.pathname)) {
        currentPath = '/serwis';
    } else if (!!matchPath('/urzadzenia/*', location.pathname)) {
        currentPath = '/urzadzenia';
    } else if (!!matchPath('/technicy/*', location.pathname)) {
        currentPath = '/technicy';
    } else if (!!matchPath('/oferty/*', location.pathname)) {
        currentPath = '/oferty';
    } else if (!!matchPath('/faktury/*', location.pathname)) {
        currentPath = '/faktury';
    } else if (!!matchPath('/czesci/*', location.pathname)) {
        currentPath = '/czesci';
    } else if (!!matchPath('/konserwacja/*', location.pathname)) {
        currentPath = '/konserwacja';
    } else if (!!matchPath('/kalendarz/*', location.pathname)) {
        currentPath = '/kalendarz';
    } else if (!!matchPath('/finanse/*', location.pathname)) {
        currentPath = '/finanse';
    } else if (!!matchPath('/raporty/*', location.pathname)) {
        currentPath = '/raporty';
    } else if (!!matchPath('/ai_asystent/*', location.pathname)) {
        currentPath = '/ai_asystent';
    } else if (!!matchPath('/administracja/*', location.pathname)) {
        currentPath = '/administracja';
    }
    else {
        currentPath = false;
    }

    return (
        <Box component="nav" sx={{ flexGrow: 1 }}>
            <AppBar position="static" color="primary">
                <Toolbar variant="dense">
                    <Box flex={1} display="flex" justifyContent="space-between">
                        <Box
                            display="flex"
                            alignItems="center"
                            component={Link}
                            to="/"
                            sx={{
                                color: 'inherit',
                                textDecoration: 'inherit',
                            }}
                            gap={1.5}
                        >
                            <Box
                                component="img"
                                sx={{ height: 24 }}
                                src={logo}
                                alt={title}
                            />
                            <Typography component="span" variant="h5">
                                {title}
                            </Typography>
                        </Box>
                        {isLargeScreen ? (
                            <Box>
                                <Tabs
                                    value={currentPath}
                                    aria-label="Navigation Tabs"
                                    indicatorColor="secondary"
                                    textColor="inherit"
                                >
                                    {navigationItems.map((item) => (
                                        <Tab
                                            key={item.path}
                                            label={item.label}
                                            component={Link}
                                            to={item.path}
                                            value={item.path}
                                        />
                                    ))}
                                </Tabs>
                            </Box>
                        ) : (
                            <IconButton
                                color="inherit"
                                aria-label="open drawer"
                                edge="start"
                                onClick={handleDrawerToggle}
                                sx={{ mr: 2 }}
                            >
                                <MenuIcon />
                            </IconButton>
                        )}
                        <Box display="flex" alignItems="center">
                            <LoadingIndicator />
                            <UserMenu>
                                <ConfigurationMenu />
                                <CanAccess resource="sales" action="list">
                                    <UsersMenu />
                                </CanAccess>
                                <Logout />
                            </UserMenu>
                        </Box>
                    </Box>
                </Toolbar>
            </AppBar>
            <Drawer
                anchor="left"
                open={drawerOpen}
                onClose={handleDrawerToggle}
            >
                <Box
                    sx={{ width: 250 }}
                    role="presentation"
                    onClick={handleDrawerToggle}
                    onKeyDown={handleDrawerToggle}
                >
                    <List>
                        {navigationItems.map((item) => (
                            <ListItem key={item.path} disablePadding>
                                <ListItemButton component={Link} to={item.path}>
                                    <ListItemIcon>{item.icon}</ListItemIcon>
                                    <ListItemText primary={item.label} />
                                </ListItemButton>
                            </ListItem>
                        ))}
                    </List>
                </Box>
            </Drawer>
        </Box>
    );
};

const UsersMenu = () => {
    const { onClose } = useUserMenu() ?? {};
    return (
        <MenuItem component={Link} to="/sales" onClick={onClose}>
            <ListItemIcon>
                <PeopleIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Users</ListItemText>
        </MenuItem>
    );
};

const ConfigurationMenu = () => {
    const { onClose } = useUserMenu() ?? {};
    return (
        <MenuItem component={Link} to="/settings" onClick={onClose}>
            <ListItemIcon>
                <SettingsIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>My info</ListItemText>
        </MenuItem>
    );
};
export default Header;
