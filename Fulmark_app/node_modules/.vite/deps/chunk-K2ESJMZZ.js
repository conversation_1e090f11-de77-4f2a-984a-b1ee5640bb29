import {
  init_useForkRef,
  useForkRef
} from "./chunk-IFE6TQG2.js";
import {
  __esm
} from "./chunk-4B2QHNJT.js";

// node_modules/@mui/material/utils/useForkRef.js
var useForkRef_default;
var init_useForkRef2 = __esm({
  "node_modules/@mui/material/utils/useForkRef.js"() {
    "use client";
    init_useForkRef();
    useForkRef_default = useForkRef;
  }
});

export {
  useForkRef_default,
  init_useForkRef2 as init_useForkRef
};
//# sourceMappingURL=chunk-K2ESJMZZ.js.map
