{"version": 3, "sources": ["../../object-inspect/index.js", "../../side-channel-list/index.js", "../../side-channel-map/index.js", "../../side-channel-weakmap/index.js", "../../side-channel/index.js", "../../qs/lib/formats.js", "../../qs/lib/utils.js", "../../qs/lib/stringify.js", "../../qs/lib/parse.js", "../../qs/lib/index.js", "../../ra-supabase-core/src/getSearchString.ts", "../../ra-supabase-core/src/authProvider.ts", "../../@raphiniert/ra-data-postgrest/esm/urlBuilder.js", "../../@raphiniert/ra-data-postgrest/esm/index.js", "../../ra-supabase-core/src/dataProvider.ts", "../../ra-supabase-core/src/useAPISchema.ts", "../../ra-supabase-core/src/useRedirectIfAuthenticated.ts", "../../ra-supabase-core/src/useResetPassword.ts", "../../ra-supabase-core/src/useSetPassword.ts", "../../ra-supabase-core/src/useSupabaseAccessToken.ts", "../../ra-supabase-ui-materialui/src/AuthLayout.tsx", "../../ra-supabase-ui-materialui/src/guessers/CreateGuesser.tsx", "../../ra-supabase-ui-materialui/src/guessers/inferElementFromType.ts", "../../ra-supabase-ui-materialui/src/guessers/EditGuesser.tsx", "../../ra-supabase-ui-materialui/src/guessers/ListGuesser.tsx", "../../ra-supabase-ui-materialui/src/guessers/useCrudGuesser.tsx", "../../ra-supabase-ui-materialui/src/guessers/ShowGuesser.tsx", "../../ra-supabase-ui-materialui/src/ForgotPasswordForm.tsx", "../../ra-supabase-ui-materialui/src/ForgotPasswordPage.tsx", "../../ra-supabase-ui-materialui/src/LoginForm.tsx", "../../ra-supabase-ui-materialui/src/icons.tsx", "../../ra-supabase-ui-materialui/src/SocialAuthButton.tsx", "../../ra-supabase-ui-materialui/src/LoginPage.tsx", "../../ra-supabase-ui-materialui/src/SetPasswordForm.tsx", "../../ra-supabase-ui-materialui/src/SetPasswordPage.tsx", "../../ra-supabase-language-english/src/index.ts", "../../ra-supabase/src/defaultI18nProvider.ts", "../../ra-supabase/src/AdminGuesser.tsx"], "sourcesContent": ["var hasMap = typeof Map === 'function' && Map.prototype;\nvar mapSizeDescriptor = Object.getOwnPropertyDescriptor && hasMap ? Object.getOwnPropertyDescriptor(Map.prototype, 'size') : null;\nvar mapSize = hasMap && mapSizeDescriptor && typeof mapSizeDescriptor.get === 'function' ? mapSizeDescriptor.get : null;\nvar mapForEach = hasMap && Map.prototype.forEach;\nvar hasSet = typeof Set === 'function' && Set.prototype;\nvar setSizeDescriptor = Object.getOwnPropertyDescriptor && hasSet ? Object.getOwnPropertyDescriptor(Set.prototype, 'size') : null;\nvar setSize = hasSet && setSizeDescriptor && typeof setSizeDescriptor.get === 'function' ? setSizeDescriptor.get : null;\nvar setForEach = hasSet && Set.prototype.forEach;\nvar hasWeakMap = typeof WeakMap === 'function' && WeakMap.prototype;\nvar weakMapHas = hasWeakMap ? WeakMap.prototype.has : null;\nvar hasWeakSet = typeof WeakSet === 'function' && WeakSet.prototype;\nvar weakSetHas = hasWeakSet ? WeakSet.prototype.has : null;\nvar hasWeakRef = typeof WeakRef === 'function' && WeakRef.prototype;\nvar weakRefDeref = hasWeakRef ? WeakRef.prototype.deref : null;\nvar booleanValueOf = Boolean.prototype.valueOf;\nvar objectToString = Object.prototype.toString;\nvar functionToString = Function.prototype.toString;\nvar $match = String.prototype.match;\nvar $slice = String.prototype.slice;\nvar $replace = String.prototype.replace;\nvar $toUpperCase = String.prototype.toUpperCase;\nvar $toLowerCase = String.prototype.toLowerCase;\nvar $test = RegExp.prototype.test;\nvar $concat = Array.prototype.concat;\nvar $join = Array.prototype.join;\nvar $arrSlice = Array.prototype.slice;\nvar $floor = Math.floor;\nvar bigIntValueOf = typeof BigInt === 'function' ? BigInt.prototype.valueOf : null;\nvar gOPS = Object.getOwnPropertySymbols;\nvar symToString = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ? Symbol.prototype.toString : null;\nvar hasShammedSymbols = typeof Symbol === 'function' && typeof Symbol.iterator === 'object';\n// ie, `has-tostringtag/shams\nvar toStringTag = typeof Symbol === 'function' && Symbol.toStringTag && (typeof Symbol.toStringTag === hasShammedSymbols ? 'object' : 'symbol')\n    ? Symbol.toStringTag\n    : null;\nvar isEnumerable = Object.prototype.propertyIsEnumerable;\n\nvar gPO = (typeof Reflect === 'function' ? Reflect.getPrototypeOf : Object.getPrototypeOf) || (\n    [].__proto__ === Array.prototype // eslint-disable-line no-proto\n        ? function (O) {\n            return O.__proto__; // eslint-disable-line no-proto\n        }\n        : null\n);\n\nfunction addNumericSeparator(num, str) {\n    if (\n        num === Infinity\n        || num === -Infinity\n        || num !== num\n        || (num && num > -1000 && num < 1000)\n        || $test.call(/e/, str)\n    ) {\n        return str;\n    }\n    var sepRegex = /[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;\n    if (typeof num === 'number') {\n        var int = num < 0 ? -$floor(-num) : $floor(num); // trunc(num)\n        if (int !== num) {\n            var intStr = String(int);\n            var dec = $slice.call(str, intStr.length + 1);\n            return $replace.call(intStr, sepRegex, '$&_') + '.' + $replace.call($replace.call(dec, /([0-9]{3})/g, '$&_'), /_$/, '');\n        }\n    }\n    return $replace.call(str, sepRegex, '$&_');\n}\n\nvar utilInspect = require('./util.inspect');\nvar inspectCustom = utilInspect.custom;\nvar inspectSymbol = isSymbol(inspectCustom) ? inspectCustom : null;\n\nvar quotes = {\n    __proto__: null,\n    'double': '\"',\n    single: \"'\"\n};\nvar quoteREs = {\n    __proto__: null,\n    'double': /([\"\\\\])/g,\n    single: /(['\\\\])/g\n};\n\nmodule.exports = function inspect_(obj, options, depth, seen) {\n    var opts = options || {};\n\n    if (has(opts, 'quoteStyle') && !has(quotes, opts.quoteStyle)) {\n        throw new TypeError('option \"quoteStyle\" must be \"single\" or \"double\"');\n    }\n    if (\n        has(opts, 'maxStringLength') && (typeof opts.maxStringLength === 'number'\n            ? opts.maxStringLength < 0 && opts.maxStringLength !== Infinity\n            : opts.maxStringLength !== null\n        )\n    ) {\n        throw new TypeError('option \"maxStringLength\", if provided, must be a positive integer, Infinity, or `null`');\n    }\n    var customInspect = has(opts, 'customInspect') ? opts.customInspect : true;\n    if (typeof customInspect !== 'boolean' && customInspect !== 'symbol') {\n        throw new TypeError('option \"customInspect\", if provided, must be `true`, `false`, or `\\'symbol\\'`');\n    }\n\n    if (\n        has(opts, 'indent')\n        && opts.indent !== null\n        && opts.indent !== '\\t'\n        && !(parseInt(opts.indent, 10) === opts.indent && opts.indent > 0)\n    ) {\n        throw new TypeError('option \"indent\" must be \"\\\\t\", an integer > 0, or `null`');\n    }\n    if (has(opts, 'numericSeparator') && typeof opts.numericSeparator !== 'boolean') {\n        throw new TypeError('option \"numericSeparator\", if provided, must be `true` or `false`');\n    }\n    var numericSeparator = opts.numericSeparator;\n\n    if (typeof obj === 'undefined') {\n        return 'undefined';\n    }\n    if (obj === null) {\n        return 'null';\n    }\n    if (typeof obj === 'boolean') {\n        return obj ? 'true' : 'false';\n    }\n\n    if (typeof obj === 'string') {\n        return inspectString(obj, opts);\n    }\n    if (typeof obj === 'number') {\n        if (obj === 0) {\n            return Infinity / obj > 0 ? '0' : '-0';\n        }\n        var str = String(obj);\n        return numericSeparator ? addNumericSeparator(obj, str) : str;\n    }\n    if (typeof obj === 'bigint') {\n        var bigIntStr = String(obj) + 'n';\n        return numericSeparator ? addNumericSeparator(obj, bigIntStr) : bigIntStr;\n    }\n\n    var maxDepth = typeof opts.depth === 'undefined' ? 5 : opts.depth;\n    if (typeof depth === 'undefined') { depth = 0; }\n    if (depth >= maxDepth && maxDepth > 0 && typeof obj === 'object') {\n        return isArray(obj) ? '[Array]' : '[Object]';\n    }\n\n    var indent = getIndent(opts, depth);\n\n    if (typeof seen === 'undefined') {\n        seen = [];\n    } else if (indexOf(seen, obj) >= 0) {\n        return '[Circular]';\n    }\n\n    function inspect(value, from, noIndent) {\n        if (from) {\n            seen = $arrSlice.call(seen);\n            seen.push(from);\n        }\n        if (noIndent) {\n            var newOpts = {\n                depth: opts.depth\n            };\n            if (has(opts, 'quoteStyle')) {\n                newOpts.quoteStyle = opts.quoteStyle;\n            }\n            return inspect_(value, newOpts, depth + 1, seen);\n        }\n        return inspect_(value, opts, depth + 1, seen);\n    }\n\n    if (typeof obj === 'function' && !isRegExp(obj)) { // in older engines, regexes are callable\n        var name = nameOf(obj);\n        var keys = arrObjKeys(obj, inspect);\n        return '[Function' + (name ? ': ' + name : ' (anonymous)') + ']' + (keys.length > 0 ? ' { ' + $join.call(keys, ', ') + ' }' : '');\n    }\n    if (isSymbol(obj)) {\n        var symString = hasShammedSymbols ? $replace.call(String(obj), /^(Symbol\\(.*\\))_[^)]*$/, '$1') : symToString.call(obj);\n        return typeof obj === 'object' && !hasShammedSymbols ? markBoxed(symString) : symString;\n    }\n    if (isElement(obj)) {\n        var s = '<' + $toLowerCase.call(String(obj.nodeName));\n        var attrs = obj.attributes || [];\n        for (var i = 0; i < attrs.length; i++) {\n            s += ' ' + attrs[i].name + '=' + wrapQuotes(quote(attrs[i].value), 'double', opts);\n        }\n        s += '>';\n        if (obj.childNodes && obj.childNodes.length) { s += '...'; }\n        s += '</' + $toLowerCase.call(String(obj.nodeName)) + '>';\n        return s;\n    }\n    if (isArray(obj)) {\n        if (obj.length === 0) { return '[]'; }\n        var xs = arrObjKeys(obj, inspect);\n        if (indent && !singleLineValues(xs)) {\n            return '[' + indentedJoin(xs, indent) + ']';\n        }\n        return '[ ' + $join.call(xs, ', ') + ' ]';\n    }\n    if (isError(obj)) {\n        var parts = arrObjKeys(obj, inspect);\n        if (!('cause' in Error.prototype) && 'cause' in obj && !isEnumerable.call(obj, 'cause')) {\n            return '{ [' + String(obj) + '] ' + $join.call($concat.call('[cause]: ' + inspect(obj.cause), parts), ', ') + ' }';\n        }\n        if (parts.length === 0) { return '[' + String(obj) + ']'; }\n        return '{ [' + String(obj) + '] ' + $join.call(parts, ', ') + ' }';\n    }\n    if (typeof obj === 'object' && customInspect) {\n        if (inspectSymbol && typeof obj[inspectSymbol] === 'function' && utilInspect) {\n            return utilInspect(obj, { depth: maxDepth - depth });\n        } else if (customInspect !== 'symbol' && typeof obj.inspect === 'function') {\n            return obj.inspect();\n        }\n    }\n    if (isMap(obj)) {\n        var mapParts = [];\n        if (mapForEach) {\n            mapForEach.call(obj, function (value, key) {\n                mapParts.push(inspect(key, obj, true) + ' => ' + inspect(value, obj));\n            });\n        }\n        return collectionOf('Map', mapSize.call(obj), mapParts, indent);\n    }\n    if (isSet(obj)) {\n        var setParts = [];\n        if (setForEach) {\n            setForEach.call(obj, function (value) {\n                setParts.push(inspect(value, obj));\n            });\n        }\n        return collectionOf('Set', setSize.call(obj), setParts, indent);\n    }\n    if (isWeakMap(obj)) {\n        return weakCollectionOf('WeakMap');\n    }\n    if (isWeakSet(obj)) {\n        return weakCollectionOf('WeakSet');\n    }\n    if (isWeakRef(obj)) {\n        return weakCollectionOf('WeakRef');\n    }\n    if (isNumber(obj)) {\n        return markBoxed(inspect(Number(obj)));\n    }\n    if (isBigInt(obj)) {\n        return markBoxed(inspect(bigIntValueOf.call(obj)));\n    }\n    if (isBoolean(obj)) {\n        return markBoxed(booleanValueOf.call(obj));\n    }\n    if (isString(obj)) {\n        return markBoxed(inspect(String(obj)));\n    }\n    // note: in IE 8, sometimes `global !== window` but both are the prototypes of each other\n    /* eslint-env browser */\n    if (typeof window !== 'undefined' && obj === window) {\n        return '{ [object Window] }';\n    }\n    if (\n        (typeof globalThis !== 'undefined' && obj === globalThis)\n        || (typeof global !== 'undefined' && obj === global)\n    ) {\n        return '{ [object globalThis] }';\n    }\n    if (!isDate(obj) && !isRegExp(obj)) {\n        var ys = arrObjKeys(obj, inspect);\n        var isPlainObject = gPO ? gPO(obj) === Object.prototype : obj instanceof Object || obj.constructor === Object;\n        var protoTag = obj instanceof Object ? '' : 'null prototype';\n        var stringTag = !isPlainObject && toStringTag && Object(obj) === obj && toStringTag in obj ? $slice.call(toStr(obj), 8, -1) : protoTag ? 'Object' : '';\n        var constructorTag = isPlainObject || typeof obj.constructor !== 'function' ? '' : obj.constructor.name ? obj.constructor.name + ' ' : '';\n        var tag = constructorTag + (stringTag || protoTag ? '[' + $join.call($concat.call([], stringTag || [], protoTag || []), ': ') + '] ' : '');\n        if (ys.length === 0) { return tag + '{}'; }\n        if (indent) {\n            return tag + '{' + indentedJoin(ys, indent) + '}';\n        }\n        return tag + '{ ' + $join.call(ys, ', ') + ' }';\n    }\n    return String(obj);\n};\n\nfunction wrapQuotes(s, defaultStyle, opts) {\n    var style = opts.quoteStyle || defaultStyle;\n    var quoteChar = quotes[style];\n    return quoteChar + s + quoteChar;\n}\n\nfunction quote(s) {\n    return $replace.call(String(s), /\"/g, '&quot;');\n}\n\nfunction canTrustToString(obj) {\n    return !toStringTag || !(typeof obj === 'object' && (toStringTag in obj || typeof obj[toStringTag] !== 'undefined'));\n}\nfunction isArray(obj) { return toStr(obj) === '[object Array]' && canTrustToString(obj); }\nfunction isDate(obj) { return toStr(obj) === '[object Date]' && canTrustToString(obj); }\nfunction isRegExp(obj) { return toStr(obj) === '[object RegExp]' && canTrustToString(obj); }\nfunction isError(obj) { return toStr(obj) === '[object Error]' && canTrustToString(obj); }\nfunction isString(obj) { return toStr(obj) === '[object String]' && canTrustToString(obj); }\nfunction isNumber(obj) { return toStr(obj) === '[object Number]' && canTrustToString(obj); }\nfunction isBoolean(obj) { return toStr(obj) === '[object Boolean]' && canTrustToString(obj); }\n\n// Symbol and BigInt do have Symbol.toStringTag by spec, so that can't be used to eliminate false positives\nfunction isSymbol(obj) {\n    if (hasShammedSymbols) {\n        return obj && typeof obj === 'object' && obj instanceof Symbol;\n    }\n    if (typeof obj === 'symbol') {\n        return true;\n    }\n    if (!obj || typeof obj !== 'object' || !symToString) {\n        return false;\n    }\n    try {\n        symToString.call(obj);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nfunction isBigInt(obj) {\n    if (!obj || typeof obj !== 'object' || !bigIntValueOf) {\n        return false;\n    }\n    try {\n        bigIntValueOf.call(obj);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nvar hasOwn = Object.prototype.hasOwnProperty || function (key) { return key in this; };\nfunction has(obj, key) {\n    return hasOwn.call(obj, key);\n}\n\nfunction toStr(obj) {\n    return objectToString.call(obj);\n}\n\nfunction nameOf(f) {\n    if (f.name) { return f.name; }\n    var m = $match.call(functionToString.call(f), /^function\\s*([\\w$]+)/);\n    if (m) { return m[1]; }\n    return null;\n}\n\nfunction indexOf(xs, x) {\n    if (xs.indexOf) { return xs.indexOf(x); }\n    for (var i = 0, l = xs.length; i < l; i++) {\n        if (xs[i] === x) { return i; }\n    }\n    return -1;\n}\n\nfunction isMap(x) {\n    if (!mapSize || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        mapSize.call(x);\n        try {\n            setSize.call(x);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof Map; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakMap(x) {\n    if (!weakMapHas || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakMapHas.call(x, weakMapHas);\n        try {\n            weakSetHas.call(x, weakSetHas);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof WeakMap; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakRef(x) {\n    if (!weakRefDeref || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakRefDeref.call(x);\n        return true;\n    } catch (e) {}\n    return false;\n}\n\nfunction isSet(x) {\n    if (!setSize || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        setSize.call(x);\n        try {\n            mapSize.call(x);\n        } catch (m) {\n            return true;\n        }\n        return x instanceof Set; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isWeakSet(x) {\n    if (!weakSetHas || !x || typeof x !== 'object') {\n        return false;\n    }\n    try {\n        weakSetHas.call(x, weakSetHas);\n        try {\n            weakMapHas.call(x, weakMapHas);\n        } catch (s) {\n            return true;\n        }\n        return x instanceof WeakSet; // core-js workaround, pre-v2.5.0\n    } catch (e) {}\n    return false;\n}\n\nfunction isElement(x) {\n    if (!x || typeof x !== 'object') { return false; }\n    if (typeof HTMLElement !== 'undefined' && x instanceof HTMLElement) {\n        return true;\n    }\n    return typeof x.nodeName === 'string' && typeof x.getAttribute === 'function';\n}\n\nfunction inspectString(str, opts) {\n    if (str.length > opts.maxStringLength) {\n        var remaining = str.length - opts.maxStringLength;\n        var trailer = '... ' + remaining + ' more character' + (remaining > 1 ? 's' : '');\n        return inspectString($slice.call(str, 0, opts.maxStringLength), opts) + trailer;\n    }\n    var quoteRE = quoteREs[opts.quoteStyle || 'single'];\n    quoteRE.lastIndex = 0;\n    // eslint-disable-next-line no-control-regex\n    var s = $replace.call($replace.call(str, quoteRE, '\\\\$1'), /[\\x00-\\x1f]/g, lowbyte);\n    return wrapQuotes(s, 'single', opts);\n}\n\nfunction lowbyte(c) {\n    var n = c.charCodeAt(0);\n    var x = {\n        8: 'b',\n        9: 't',\n        10: 'n',\n        12: 'f',\n        13: 'r'\n    }[n];\n    if (x) { return '\\\\' + x; }\n    return '\\\\x' + (n < 0x10 ? '0' : '') + $toUpperCase.call(n.toString(16));\n}\n\nfunction markBoxed(str) {\n    return 'Object(' + str + ')';\n}\n\nfunction weakCollectionOf(type) {\n    return type + ' { ? }';\n}\n\nfunction collectionOf(type, size, entries, indent) {\n    var joinedEntries = indent ? indentedJoin(entries, indent) : $join.call(entries, ', ');\n    return type + ' (' + size + ') {' + joinedEntries + '}';\n}\n\nfunction singleLineValues(xs) {\n    for (var i = 0; i < xs.length; i++) {\n        if (indexOf(xs[i], '\\n') >= 0) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getIndent(opts, depth) {\n    var baseIndent;\n    if (opts.indent === '\\t') {\n        baseIndent = '\\t';\n    } else if (typeof opts.indent === 'number' && opts.indent > 0) {\n        baseIndent = $join.call(Array(opts.indent + 1), ' ');\n    } else {\n        return null;\n    }\n    return {\n        base: baseIndent,\n        prev: $join.call(Array(depth + 1), baseIndent)\n    };\n}\n\nfunction indentedJoin(xs, indent) {\n    if (xs.length === 0) { return ''; }\n    var lineJoiner = '\\n' + indent.prev + indent.base;\n    return lineJoiner + $join.call(xs, ',' + lineJoiner) + '\\n' + indent.prev;\n}\n\nfunction arrObjKeys(obj, inspect) {\n    var isArr = isArray(obj);\n    var xs = [];\n    if (isArr) {\n        xs.length = obj.length;\n        for (var i = 0; i < obj.length; i++) {\n            xs[i] = has(obj, i) ? inspect(obj[i], obj) : '';\n        }\n    }\n    var syms = typeof gOPS === 'function' ? gOPS(obj) : [];\n    var symMap;\n    if (hasShammedSymbols) {\n        symMap = {};\n        for (var k = 0; k < syms.length; k++) {\n            symMap['$' + syms[k]] = syms[k];\n        }\n    }\n\n    for (var key in obj) { // eslint-disable-line no-restricted-syntax\n        if (!has(obj, key)) { continue; } // eslint-disable-line no-restricted-syntax, no-continue\n        if (isArr && String(Number(key)) === key && key < obj.length) { continue; } // eslint-disable-line no-restricted-syntax, no-continue\n        if (hasShammedSymbols && symMap['$' + key] instanceof Symbol) {\n            // this is to prevent shammed Symbols, which are stored as strings, from being included in the string key section\n            continue; // eslint-disable-line no-restricted-syntax, no-continue\n        } else if ($test.call(/[^\\w$]/, key)) {\n            xs.push(inspect(key, obj) + ': ' + inspect(obj[key], obj));\n        } else {\n            xs.push(key + ': ' + inspect(obj[key], obj));\n        }\n    }\n    if (typeof gOPS === 'function') {\n        for (var j = 0; j < syms.length; j++) {\n            if (isEnumerable.call(obj, syms[j])) {\n                xs.push('[' + inspect(syms[j]) + ']: ' + inspect(obj[syms[j]], obj));\n            }\n        }\n    }\n    return xs;\n}\n", "'use strict';\n\nvar inspect = require('object-inspect');\n\nvar $TypeError = require('es-errors/type');\n\n/*\n* This function traverses the list returning the node corresponding to the given key.\n*\n* That node is also moved to the head of the list, so that if it's accessed again we don't need to traverse the whole list.\n* By doing so, all the recently used nodes can be accessed relatively quickly.\n*/\n/** @type {import('./list.d.ts').listGetNode} */\n// eslint-disable-next-line consistent-return\nvar listGetNode = function (list, key, isDelete) {\n\t/** @type {typeof list | NonNullable<(typeof list)['next']>} */\n\tvar prev = list;\n\t/** @type {(typeof list)['next']} */\n\tvar curr;\n\t// eslint-disable-next-line eqeqeq\n\tfor (; (curr = prev.next) != null; prev = curr) {\n\t\tif (curr.key === key) {\n\t\t\tprev.next = curr.next;\n\t\t\tif (!isDelete) {\n\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\tcurr.next = /** @type {NonNullable<typeof list.next>} */ (list.next);\n\t\t\t\tlist.next = curr; // eslint-disable-line no-param-reassign\n\t\t\t}\n\t\t\treturn curr;\n\t\t}\n\t}\n};\n\n/** @type {import('./list.d.ts').listGet} */\nvar listGet = function (objects, key) {\n\tif (!objects) {\n\t\treturn void undefined;\n\t}\n\tvar node = listGetNode(objects, key);\n\treturn node && node.value;\n};\n/** @type {import('./list.d.ts').listSet} */\nvar listSet = function (objects, key, value) {\n\tvar node = listGetNode(objects, key);\n\tif (node) {\n\t\tnode.value = value;\n\t} else {\n\t\t// Prepend the new node to the beginning of the list\n\t\tobjects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */ ({ // eslint-disable-line no-param-reassign, no-extra-parens\n\t\t\tkey: key,\n\t\t\tnext: objects.next,\n\t\t\tvalue: value\n\t\t});\n\t}\n};\n/** @type {import('./list.d.ts').listHas} */\nvar listHas = function (objects, key) {\n\tif (!objects) {\n\t\treturn false;\n\t}\n\treturn !!listGetNode(objects, key);\n};\n/** @type {import('./list.d.ts').listDelete} */\n// eslint-disable-next-line consistent-return\nvar listDelete = function (objects, key) {\n\tif (objects) {\n\t\treturn listGetNode(objects, key, true);\n\t}\n};\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannelList() {\n\t/** @typedef {ReturnType<typeof getSideChannelList>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {import('./list.d.ts').RootNode<V, K> | undefined} */ var $o;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tvar root = $o && $o.next;\n\t\t\tvar deletedNode = listDelete($o, key);\n\t\t\tif (deletedNode && root && root === deletedNode) {\n\t\t\t\t$o = void undefined;\n\t\t\t}\n\t\t\treturn !!deletedNode;\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn listGet($o, key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn listHas($o, key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$o) {\n\t\t\t\t// Initialize the linked list as an empty node, so that we don't have to special-case handling of the first node: we can always refer to it as (previous node).next, instead of something like (list).head\n\t\t\t\t$o = {\n\t\t\t\t\tnext: void undefined\n\t\t\t\t};\n\t\t\t}\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\tlistSet(/** @type {NonNullable<typeof $o>} */ ($o), key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\n\nvar $TypeError = require('es-errors/type');\nvar $Map = GetIntrinsic('%Map%', true);\n\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => V} */\nvar $mapGet = callBound('Map.prototype.get', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K, value: V) => void} */\nvar $mapSet = callBound('Map.prototype.set', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapHas = callBound('Map.prototype.has', true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */\nvar $mapDelete = callBound('Map.prototype.delete', true);\n/** @type {<K, V>(thisArg: Map<K, V>) => number} */\nvar $mapSize = callBound('Map.prototype.size', true);\n\n/** @type {import('.')} */\nmodule.exports = !!$Map && /** @type {Exclude<import('.'), false>} */ function getSideChannelMap() {\n\t/** @typedef {ReturnType<typeof getSideChannelMap>} Channel */\n\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t/** @type {Map<K, V> | undefined} */ var $m;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\tif ($m) {\n\t\t\t\tvar result = $mapDelete($m, key);\n\t\t\t\tif ($mapSize($m) === 0) {\n\t\t\t\t\t$m = void undefined;\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tget: function (key) { // eslint-disable-line consistent-return\n\t\t\tif ($m) {\n\t\t\t\treturn $mapGet($m, key);\n\t\t\t}\n\t\t},\n\t\thas: function (key) {\n\t\t\tif ($m) {\n\t\t\t\treturn $mapHas($m, key);\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$m) {\n\t\t\t\t// @ts-expect-error TS can't handle narrowing a variable inside a closure\n\t\t\t\t$m = new $Map();\n\t\t\t}\n\t\t\t$mapSet($m, key, value);\n\t\t}\n\t};\n\n\t// @ts-expect-error TODO: figure out why TS is erroring here\n\treturn channel;\n};\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBound = require('call-bound');\nvar inspect = require('object-inspect');\nvar getSideChannelMap = require('side-channel-map');\n\nvar $TypeError = require('es-errors/type');\nvar $WeakMap = GetIntrinsic('%WeakMap%', true);\n\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => V} */\nvar $weakMapGet = callBound('WeakMap.prototype.get', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K, value: V) => void} */\nvar $weakMapSet = callBound('WeakMap.prototype.set', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapHas = callBound('WeakMap.prototype.has', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapDelete = callBound('WeakMap.prototype.delete', true);\n\n/** @type {import('.')} */\nmodule.exports = $WeakMap\n\t? /** @type {Exclude<import('.'), false>} */ function getSideChannelWeakMap() {\n\t\t/** @typedef {ReturnType<typeof getSideChannelWeakMap>} Channel */\n\t\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t\t/** @type {WeakMap<K & object, V> | undefined} */ var $wm;\n\t\t/** @type {Channel | undefined} */ var $m;\n\n\t\t/** @type {Channel} */\n\t\tvar channel = {\n\t\t\tassert: function (key) {\n\t\t\t\tif (!channel.has(key)) {\n\t\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t\t}\n\t\t\t},\n\t\t\t'delete': function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapDelete($wm, key);\n\t\t\t\t\t}\n\t\t\t\t} else if (getSideChannelMap) {\n\t\t\t\t\tif ($m) {\n\t\t\t\t\t\treturn $m['delete'](key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tget: function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapGet($wm, key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn $m && $m.get(key);\n\t\t\t},\n\t\t\thas: function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapHas($wm, key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn !!$m && $m.has(key);\n\t\t\t},\n\t\t\tset: function (key, value) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif (!$wm) {\n\t\t\t\t\t\t$wm = new $WeakMap();\n\t\t\t\t\t}\n\t\t\t\t\t$weakMapSet($wm, key, value);\n\t\t\t\t} else if (getSideChannelMap) {\n\t\t\t\t\tif (!$m) {\n\t\t\t\t\t\t$m = getSideChannelMap();\n\t\t\t\t\t}\n\t\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\t\t/** @type {NonNullable<typeof $m>} */ ($m).set(key, value);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\t// @ts-expect-error TODO: figure out why this is erroring\n\t\treturn channel;\n\t}\n\t: getSideChannelMap;\n", "'use strict';\n\nvar $TypeError = require('es-errors/type');\nvar inspect = require('object-inspect');\nvar getSideChannelList = require('side-channel-list');\nvar getSideChannelMap = require('side-channel-map');\nvar getSideChannelWeakMap = require('side-channel-weakmap');\n\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannel() {\n\t/** @typedef {ReturnType<typeof getSideChannel>} Channel */\n\n\t/** @type {Channel | undefined} */ var $channelData;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\treturn !!$channelData && $channelData['delete'](key);\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn $channelData && $channelData.get(key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn !!$channelData && $channelData.has(key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$channelData) {\n\t\t\t\t$channelData = makeChannel();\n\t\t\t}\n\n\t\t\t$channelData.set(key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n", "'use strict';\n\nvar replace = String.prototype.replace;\nvar percentTwenties = /%20/g;\n\nvar Format = {\n    RFC1738: 'RFC1738',\n    RFC3986: 'RFC3986'\n};\n\nmodule.exports = {\n    'default': Format.RFC3986,\n    formatters: {\n        RFC1738: function (value) {\n            return replace.call(value, percentTwenties, '+');\n        },\n        RFC3986: function (value) {\n            return String(value);\n        }\n    },\n    RFC1738: Format.RFC1738,\n    RFC3986: Format.RFC3986\n};\n", "'use strict';\n\nvar formats = require('./formats');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar hexTable = (function () {\n    var array = [];\n    for (var i = 0; i < 256; ++i) {\n        array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());\n    }\n\n    return array;\n}());\n\nvar compactQueue = function compactQueue(queue) {\n    while (queue.length > 1) {\n        var item = queue.pop();\n        var obj = item.obj[item.prop];\n\n        if (isArray(obj)) {\n            var compacted = [];\n\n            for (var j = 0; j < obj.length; ++j) {\n                if (typeof obj[j] !== 'undefined') {\n                    compacted.push(obj[j]);\n                }\n            }\n\n            item.obj[item.prop] = compacted;\n        }\n    }\n};\n\nvar arrayToObject = function arrayToObject(source, options) {\n    var obj = options && options.plainObjects ? { __proto__: null } : {};\n    for (var i = 0; i < source.length; ++i) {\n        if (typeof source[i] !== 'undefined') {\n            obj[i] = source[i];\n        }\n    }\n\n    return obj;\n};\n\nvar merge = function merge(target, source, options) {\n    /* eslint no-param-reassign: 0 */\n    if (!source) {\n        return target;\n    }\n\n    if (typeof source !== 'object' && typeof source !== 'function') {\n        if (isArray(target)) {\n            target.push(source);\n        } else if (target && typeof target === 'object') {\n            if (\n                (options && (options.plainObjects || options.allowPrototypes))\n                || !has.call(Object.prototype, source)\n            ) {\n                target[source] = true;\n            }\n        } else {\n            return [target, source];\n        }\n\n        return target;\n    }\n\n    if (!target || typeof target !== 'object') {\n        return [target].concat(source);\n    }\n\n    var mergeTarget = target;\n    if (isArray(target) && !isArray(source)) {\n        mergeTarget = arrayToObject(target, options);\n    }\n\n    if (isArray(target) && isArray(source)) {\n        source.forEach(function (item, i) {\n            if (has.call(target, i)) {\n                var targetItem = target[i];\n                if (targetItem && typeof targetItem === 'object' && item && typeof item === 'object') {\n                    target[i] = merge(targetItem, item, options);\n                } else {\n                    target.push(item);\n                }\n            } else {\n                target[i] = item;\n            }\n        });\n        return target;\n    }\n\n    return Object.keys(source).reduce(function (acc, key) {\n        var value = source[key];\n\n        if (has.call(acc, key)) {\n            acc[key] = merge(acc[key], value, options);\n        } else {\n            acc[key] = value;\n        }\n        return acc;\n    }, mergeTarget);\n};\n\nvar assign = function assignSingleSource(target, source) {\n    return Object.keys(source).reduce(function (acc, key) {\n        acc[key] = source[key];\n        return acc;\n    }, target);\n};\n\nvar decode = function (str, defaultDecoder, charset) {\n    var strWithoutPlus = str.replace(/\\+/g, ' ');\n    if (charset === 'iso-8859-1') {\n        // unescape never throws, no try...catch needed:\n        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);\n    }\n    // utf-8\n    try {\n        return decodeURIComponent(strWithoutPlus);\n    } catch (e) {\n        return strWithoutPlus;\n    }\n};\n\nvar limit = 1024;\n\n/* eslint operator-linebreak: [2, \"before\"] */\n\nvar encode = function encode(str, defaultEncoder, charset, kind, format) {\n    // This code was originally written by Brian White (mscdex) for the io.js core querystring library.\n    // It has been adapted here for stricter adherence to RFC 3986\n    if (str.length === 0) {\n        return str;\n    }\n\n    var string = str;\n    if (typeof str === 'symbol') {\n        string = Symbol.prototype.toString.call(str);\n    } else if (typeof str !== 'string') {\n        string = String(str);\n    }\n\n    if (charset === 'iso-8859-1') {\n        return escape(string).replace(/%u[0-9a-f]{4}/gi, function ($0) {\n            return '%26%23' + parseInt($0.slice(2), 16) + '%3B';\n        });\n    }\n\n    var out = '';\n    for (var j = 0; j < string.length; j += limit) {\n        var segment = string.length >= limit ? string.slice(j, j + limit) : string;\n        var arr = [];\n\n        for (var i = 0; i < segment.length; ++i) {\n            var c = segment.charCodeAt(i);\n            if (\n                c === 0x2D // -\n                || c === 0x2E // .\n                || c === 0x5F // _\n                || c === 0x7E // ~\n                || (c >= 0x30 && c <= 0x39) // 0-9\n                || (c >= 0x41 && c <= 0x5A) // a-z\n                || (c >= 0x61 && c <= 0x7A) // A-Z\n                || (format === formats.RFC1738 && (c === 0x28 || c === 0x29)) // ( )\n            ) {\n                arr[arr.length] = segment.charAt(i);\n                continue;\n            }\n\n            if (c < 0x80) {\n                arr[arr.length] = hexTable[c];\n                continue;\n            }\n\n            if (c < 0x800) {\n                arr[arr.length] = hexTable[0xC0 | (c >> 6)]\n                    + hexTable[0x80 | (c & 0x3F)];\n                continue;\n            }\n\n            if (c < 0xD800 || c >= 0xE000) {\n                arr[arr.length] = hexTable[0xE0 | (c >> 12)]\n                    + hexTable[0x80 | ((c >> 6) & 0x3F)]\n                    + hexTable[0x80 | (c & 0x3F)];\n                continue;\n            }\n\n            i += 1;\n            c = 0x10000 + (((c & 0x3FF) << 10) | (segment.charCodeAt(i) & 0x3FF));\n\n            arr[arr.length] = hexTable[0xF0 | (c >> 18)]\n                + hexTable[0x80 | ((c >> 12) & 0x3F)]\n                + hexTable[0x80 | ((c >> 6) & 0x3F)]\n                + hexTable[0x80 | (c & 0x3F)];\n        }\n\n        out += arr.join('');\n    }\n\n    return out;\n};\n\nvar compact = function compact(value) {\n    var queue = [{ obj: { o: value }, prop: 'o' }];\n    var refs = [];\n\n    for (var i = 0; i < queue.length; ++i) {\n        var item = queue[i];\n        var obj = item.obj[item.prop];\n\n        var keys = Object.keys(obj);\n        for (var j = 0; j < keys.length; ++j) {\n            var key = keys[j];\n            var val = obj[key];\n            if (typeof val === 'object' && val !== null && refs.indexOf(val) === -1) {\n                queue.push({ obj: obj, prop: key });\n                refs.push(val);\n            }\n        }\n    }\n\n    compactQueue(queue);\n\n    return value;\n};\n\nvar isRegExp = function isRegExp(obj) {\n    return Object.prototype.toString.call(obj) === '[object RegExp]';\n};\n\nvar isBuffer = function isBuffer(obj) {\n    if (!obj || typeof obj !== 'object') {\n        return false;\n    }\n\n    return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));\n};\n\nvar combine = function combine(a, b) {\n    return [].concat(a, b);\n};\n\nvar maybeMap = function maybeMap(val, fn) {\n    if (isArray(val)) {\n        var mapped = [];\n        for (var i = 0; i < val.length; i += 1) {\n            mapped.push(fn(val[i]));\n        }\n        return mapped;\n    }\n    return fn(val);\n};\n\nmodule.exports = {\n    arrayToObject: arrayToObject,\n    assign: assign,\n    combine: combine,\n    compact: compact,\n    decode: decode,\n    encode: encode,\n    isBuffer: isBuffer,\n    isRegExp: isRegExp,\n    maybeMap: maybeMap,\n    merge: merge\n};\n", "'use strict';\n\nvar getSideChannel = require('side-channel');\nvar utils = require('./utils');\nvar formats = require('./formats');\nvar has = Object.prototype.hasOwnProperty;\n\nvar arrayPrefixGenerators = {\n    brackets: function brackets(prefix) {\n        return prefix + '[]';\n    },\n    comma: 'comma',\n    indices: function indices(prefix, key) {\n        return prefix + '[' + key + ']';\n    },\n    repeat: function repeat(prefix) {\n        return prefix;\n    }\n};\n\nvar isArray = Array.isArray;\nvar push = Array.prototype.push;\nvar pushToArray = function (arr, valueOrArray) {\n    push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);\n};\n\nvar toISO = Date.prototype.toISOString;\n\nvar defaultFormat = formats['default'];\nvar defaults = {\n    addQueryPrefix: false,\n    allowDots: false,\n    allowEmptyArrays: false,\n    arrayFormat: 'indices',\n    charset: 'utf-8',\n    charsetSentinel: false,\n    commaRoundTrip: false,\n    delimiter: '&',\n    encode: true,\n    encodeDotInKeys: false,\n    encoder: utils.encode,\n    encodeValuesOnly: false,\n    filter: void undefined,\n    format: defaultFormat,\n    formatter: formats.formatters[defaultFormat],\n    // deprecated\n    indices: false,\n    serializeDate: function serializeDate(date) {\n        return toISO.call(date);\n    },\n    skipNulls: false,\n    strictNullHandling: false\n};\n\nvar isNonNullishPrimitive = function isNonNullishPrimitive(v) {\n    return typeof v === 'string'\n        || typeof v === 'number'\n        || typeof v === 'boolean'\n        || typeof v === 'symbol'\n        || typeof v === 'bigint';\n};\n\nvar sentinel = {};\n\nvar stringify = function stringify(\n    object,\n    prefix,\n    generateArrayPrefix,\n    commaRoundTrip,\n    allowEmptyArrays,\n    strictNullHandling,\n    skipNulls,\n    encodeDotInKeys,\n    encoder,\n    filter,\n    sort,\n    allowDots,\n    serializeDate,\n    format,\n    formatter,\n    encodeValuesOnly,\n    charset,\n    sideChannel\n) {\n    var obj = object;\n\n    var tmpSc = sideChannel;\n    var step = 0;\n    var findFlag = false;\n    while ((tmpSc = tmpSc.get(sentinel)) !== void undefined && !findFlag) {\n        // Where object last appeared in the ref tree\n        var pos = tmpSc.get(object);\n        step += 1;\n        if (typeof pos !== 'undefined') {\n            if (pos === step) {\n                throw new RangeError('Cyclic object value');\n            } else {\n                findFlag = true; // Break while\n            }\n        }\n        if (typeof tmpSc.get(sentinel) === 'undefined') {\n            step = 0;\n        }\n    }\n\n    if (typeof filter === 'function') {\n        obj = filter(prefix, obj);\n    } else if (obj instanceof Date) {\n        obj = serializeDate(obj);\n    } else if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        obj = utils.maybeMap(obj, function (value) {\n            if (value instanceof Date) {\n                return serializeDate(value);\n            }\n            return value;\n        });\n    }\n\n    if (obj === null) {\n        if (strictNullHandling) {\n            return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, 'key', format) : prefix;\n        }\n\n        obj = '';\n    }\n\n    if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {\n        if (encoder) {\n            var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, 'key', format);\n            return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults.encoder, charset, 'value', format))];\n        }\n        return [formatter(prefix) + '=' + formatter(String(obj))];\n    }\n\n    var values = [];\n\n    if (typeof obj === 'undefined') {\n        return values;\n    }\n\n    var objKeys;\n    if (generateArrayPrefix === 'comma' && isArray(obj)) {\n        // we need to join elements in\n        if (encodeValuesOnly && encoder) {\n            obj = utils.maybeMap(obj, encoder);\n        }\n        objKeys = [{ value: obj.length > 0 ? obj.join(',') || null : void undefined }];\n    } else if (isArray(filter)) {\n        objKeys = filter;\n    } else {\n        var keys = Object.keys(obj);\n        objKeys = sort ? keys.sort(sort) : keys;\n    }\n\n    var encodedPrefix = encodeDotInKeys ? String(prefix).replace(/\\./g, '%2E') : String(prefix);\n\n    var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? encodedPrefix + '[]' : encodedPrefix;\n\n    if (allowEmptyArrays && isArray(obj) && obj.length === 0) {\n        return adjustedPrefix + '[]';\n    }\n\n    for (var j = 0; j < objKeys.length; ++j) {\n        var key = objKeys[j];\n        var value = typeof key === 'object' && key && typeof key.value !== 'undefined'\n            ? key.value\n            : obj[key];\n\n        if (skipNulls && value === null) {\n            continue;\n        }\n\n        var encodedKey = allowDots && encodeDotInKeys ? String(key).replace(/\\./g, '%2E') : String(key);\n        var keyPrefix = isArray(obj)\n            ? typeof generateArrayPrefix === 'function' ? generateArrayPrefix(adjustedPrefix, encodedKey) : adjustedPrefix\n            : adjustedPrefix + (allowDots ? '.' + encodedKey : '[' + encodedKey + ']');\n\n        sideChannel.set(object, step);\n        var valueSideChannel = getSideChannel();\n        valueSideChannel.set(sentinel, sideChannel);\n        pushToArray(values, stringify(\n            value,\n            keyPrefix,\n            generateArrayPrefix,\n            commaRoundTrip,\n            allowEmptyArrays,\n            strictNullHandling,\n            skipNulls,\n            encodeDotInKeys,\n            generateArrayPrefix === 'comma' && encodeValuesOnly && isArray(obj) ? null : encoder,\n            filter,\n            sort,\n            allowDots,\n            serializeDate,\n            format,\n            formatter,\n            encodeValuesOnly,\n            charset,\n            valueSideChannel\n        ));\n    }\n\n    return values;\n};\n\nvar normalizeStringifyOptions = function normalizeStringifyOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (typeof opts.allowEmptyArrays !== 'undefined' && typeof opts.allowEmptyArrays !== 'boolean') {\n        throw new TypeError('`allowEmptyArrays` option can only be `true` or `false`, when provided');\n    }\n\n    if (typeof opts.encodeDotInKeys !== 'undefined' && typeof opts.encodeDotInKeys !== 'boolean') {\n        throw new TypeError('`encodeDotInKeys` option can only be `true` or `false`, when provided');\n    }\n\n    if (opts.encoder !== null && typeof opts.encoder !== 'undefined' && typeof opts.encoder !== 'function') {\n        throw new TypeError('Encoder has to be a function.');\n    }\n\n    var charset = opts.charset || defaults.charset;\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n\n    var format = formats['default'];\n    if (typeof opts.format !== 'undefined') {\n        if (!has.call(formats.formatters, opts.format)) {\n            throw new TypeError('Unknown format option provided.');\n        }\n        format = opts.format;\n    }\n    var formatter = formats.formatters[format];\n\n    var filter = defaults.filter;\n    if (typeof opts.filter === 'function' || isArray(opts.filter)) {\n        filter = opts.filter;\n    }\n\n    var arrayFormat;\n    if (opts.arrayFormat in arrayPrefixGenerators) {\n        arrayFormat = opts.arrayFormat;\n    } else if ('indices' in opts) {\n        arrayFormat = opts.indices ? 'indices' : 'repeat';\n    } else {\n        arrayFormat = defaults.arrayFormat;\n    }\n\n    if ('commaRoundTrip' in opts && typeof opts.commaRoundTrip !== 'boolean') {\n        throw new TypeError('`commaRoundTrip` must be a boolean, or absent');\n    }\n\n    var allowDots = typeof opts.allowDots === 'undefined' ? opts.encodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;\n\n    return {\n        addQueryPrefix: typeof opts.addQueryPrefix === 'boolean' ? opts.addQueryPrefix : defaults.addQueryPrefix,\n        allowDots: allowDots,\n        allowEmptyArrays: typeof opts.allowEmptyArrays === 'boolean' ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,\n        arrayFormat: arrayFormat,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        commaRoundTrip: !!opts.commaRoundTrip,\n        delimiter: typeof opts.delimiter === 'undefined' ? defaults.delimiter : opts.delimiter,\n        encode: typeof opts.encode === 'boolean' ? opts.encode : defaults.encode,\n        encodeDotInKeys: typeof opts.encodeDotInKeys === 'boolean' ? opts.encodeDotInKeys : defaults.encodeDotInKeys,\n        encoder: typeof opts.encoder === 'function' ? opts.encoder : defaults.encoder,\n        encodeValuesOnly: typeof opts.encodeValuesOnly === 'boolean' ? opts.encodeValuesOnly : defaults.encodeValuesOnly,\n        filter: filter,\n        format: format,\n        formatter: formatter,\n        serializeDate: typeof opts.serializeDate === 'function' ? opts.serializeDate : defaults.serializeDate,\n        skipNulls: typeof opts.skipNulls === 'boolean' ? opts.skipNulls : defaults.skipNulls,\n        sort: typeof opts.sort === 'function' ? opts.sort : null,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling\n    };\n};\n\nmodule.exports = function (object, opts) {\n    var obj = object;\n    var options = normalizeStringifyOptions(opts);\n\n    var objKeys;\n    var filter;\n\n    if (typeof options.filter === 'function') {\n        filter = options.filter;\n        obj = filter('', obj);\n    } else if (isArray(options.filter)) {\n        filter = options.filter;\n        objKeys = filter;\n    }\n\n    var keys = [];\n\n    if (typeof obj !== 'object' || obj === null) {\n        return '';\n    }\n\n    var generateArrayPrefix = arrayPrefixGenerators[options.arrayFormat];\n    var commaRoundTrip = generateArrayPrefix === 'comma' && options.commaRoundTrip;\n\n    if (!objKeys) {\n        objKeys = Object.keys(obj);\n    }\n\n    if (options.sort) {\n        objKeys.sort(options.sort);\n    }\n\n    var sideChannel = getSideChannel();\n    for (var i = 0; i < objKeys.length; ++i) {\n        var key = objKeys[i];\n        var value = obj[key];\n\n        if (options.skipNulls && value === null) {\n            continue;\n        }\n        pushToArray(keys, stringify(\n            value,\n            key,\n            generateArrayPrefix,\n            commaRoundTrip,\n            options.allowEmptyArrays,\n            options.strictNullHandling,\n            options.skipNulls,\n            options.encodeDotInKeys,\n            options.encode ? options.encoder : null,\n            options.filter,\n            options.sort,\n            options.allowDots,\n            options.serializeDate,\n            options.format,\n            options.formatter,\n            options.encodeValuesOnly,\n            options.charset,\n            sideChannel\n        ));\n    }\n\n    var joined = keys.join(options.delimiter);\n    var prefix = options.addQueryPrefix === true ? '?' : '';\n\n    if (options.charsetSentinel) {\n        if (options.charset === 'iso-8859-1') {\n            // encodeURIComponent('&#10003;'), the \"numeric entity\" representation of a checkmark\n            prefix += 'utf8=%26%2310003%3B&';\n        } else {\n            // encodeURIComponent('✓')\n            prefix += 'utf8=%E2%9C%93&';\n        }\n    }\n\n    return joined.length > 0 ? prefix + joined : '';\n};\n", "'use strict';\n\nvar utils = require('./utils');\n\nvar has = Object.prototype.hasOwnProperty;\nvar isArray = Array.isArray;\n\nvar defaults = {\n    allowDots: false,\n    allowEmptyArrays: false,\n    allowPrototypes: false,\n    allowSparse: false,\n    arrayLimit: 20,\n    charset: 'utf-8',\n    charsetSentinel: false,\n    comma: false,\n    decodeDotInKeys: false,\n    decoder: utils.decode,\n    delimiter: '&',\n    depth: 5,\n    duplicates: 'combine',\n    ignoreQueryPrefix: false,\n    interpretNumericEntities: false,\n    parameterLimit: 1000,\n    parseArrays: true,\n    plainObjects: false,\n    strictDepth: false,\n    strictNullHandling: false,\n    throwOnLimitExceeded: false\n};\n\nvar interpretNumericEntities = function (str) {\n    return str.replace(/&#(\\d+);/g, function ($0, numberStr) {\n        return String.fromCharCode(parseInt(numberStr, 10));\n    });\n};\n\nvar parseArrayValue = function (val, options, currentArrayLength) {\n    if (val && typeof val === 'string' && options.comma && val.indexOf(',') > -1) {\n        return val.split(',');\n    }\n\n    if (options.throwOnLimitExceeded && currentArrayLength >= options.arrayLimit) {\n        throw new RangeError('Array limit exceeded. Only ' + options.arrayLimit + ' element' + (options.arrayLimit === 1 ? '' : 's') + ' allowed in an array.');\n    }\n\n    return val;\n};\n\n// This is what browsers will submit when the ✓ character occurs in an\n// application/x-www-form-urlencoded body and the encoding of the page containing\n// the form is iso-8859-1, or when the submitted form has an accept-charset\n// attribute of iso-8859-1. Presumably also with other charsets that do not contain\n// the ✓ character, such as us-ascii.\nvar isoSentinel = 'utf8=%26%2310003%3B'; // encodeURIComponent('&#10003;')\n\n// These are the percent-encoded utf-8 octets representing a checkmark, indicating that the request actually is utf-8 encoded.\nvar charsetSentinel = 'utf8=%E2%9C%93'; // encodeURIComponent('✓')\n\nvar parseValues = function parseQueryStringValues(str, options) {\n    var obj = { __proto__: null };\n\n    var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\\?/, '') : str;\n    cleanStr = cleanStr.replace(/%5B/gi, '[').replace(/%5D/gi, ']');\n\n    var limit = options.parameterLimit === Infinity ? undefined : options.parameterLimit;\n    var parts = cleanStr.split(\n        options.delimiter,\n        options.throwOnLimitExceeded ? limit + 1 : limit\n    );\n\n    if (options.throwOnLimitExceeded && parts.length > limit) {\n        throw new RangeError('Parameter limit exceeded. Only ' + limit + ' parameter' + (limit === 1 ? '' : 's') + ' allowed.');\n    }\n\n    var skipIndex = -1; // Keep track of where the utf8 sentinel was found\n    var i;\n\n    var charset = options.charset;\n    if (options.charsetSentinel) {\n        for (i = 0; i < parts.length; ++i) {\n            if (parts[i].indexOf('utf8=') === 0) {\n                if (parts[i] === charsetSentinel) {\n                    charset = 'utf-8';\n                } else if (parts[i] === isoSentinel) {\n                    charset = 'iso-8859-1';\n                }\n                skipIndex = i;\n                i = parts.length; // The eslint settings do not allow break;\n            }\n        }\n    }\n\n    for (i = 0; i < parts.length; ++i) {\n        if (i === skipIndex) {\n            continue;\n        }\n        var part = parts[i];\n\n        var bracketEqualsPos = part.indexOf(']=');\n        var pos = bracketEqualsPos === -1 ? part.indexOf('=') : bracketEqualsPos + 1;\n\n        var key;\n        var val;\n        if (pos === -1) {\n            key = options.decoder(part, defaults.decoder, charset, 'key');\n            val = options.strictNullHandling ? null : '';\n        } else {\n            key = options.decoder(part.slice(0, pos), defaults.decoder, charset, 'key');\n\n            val = utils.maybeMap(\n                parseArrayValue(\n                    part.slice(pos + 1),\n                    options,\n                    isArray(obj[key]) ? obj[key].length : 0\n                ),\n                function (encodedVal) {\n                    return options.decoder(encodedVal, defaults.decoder, charset, 'value');\n                }\n            );\n        }\n\n        if (val && options.interpretNumericEntities && charset === 'iso-8859-1') {\n            val = interpretNumericEntities(String(val));\n        }\n\n        if (part.indexOf('[]=') > -1) {\n            val = isArray(val) ? [val] : val;\n        }\n\n        var existing = has.call(obj, key);\n        if (existing && options.duplicates === 'combine') {\n            obj[key] = utils.combine(obj[key], val);\n        } else if (!existing || options.duplicates === 'last') {\n            obj[key] = val;\n        }\n    }\n\n    return obj;\n};\n\nvar parseObject = function (chain, val, options, valuesParsed) {\n    var currentArrayLength = 0;\n    if (chain.length > 0 && chain[chain.length - 1] === '[]') {\n        var parentKey = chain.slice(0, -1).join('');\n        currentArrayLength = Array.isArray(val) && val[parentKey] ? val[parentKey].length : 0;\n    }\n\n    var leaf = valuesParsed ? val : parseArrayValue(val, options, currentArrayLength);\n\n    for (var i = chain.length - 1; i >= 0; --i) {\n        var obj;\n        var root = chain[i];\n\n        if (root === '[]' && options.parseArrays) {\n            obj = options.allowEmptyArrays && (leaf === '' || (options.strictNullHandling && leaf === null))\n                ? []\n                : utils.combine([], leaf);\n        } else {\n            obj = options.plainObjects ? { __proto__: null } : {};\n            var cleanRoot = root.charAt(0) === '[' && root.charAt(root.length - 1) === ']' ? root.slice(1, -1) : root;\n            var decodedRoot = options.decodeDotInKeys ? cleanRoot.replace(/%2E/g, '.') : cleanRoot;\n            var index = parseInt(decodedRoot, 10);\n            if (!options.parseArrays && decodedRoot === '') {\n                obj = { 0: leaf };\n            } else if (\n                !isNaN(index)\n                && root !== decodedRoot\n                && String(index) === decodedRoot\n                && index >= 0\n                && (options.parseArrays && index <= options.arrayLimit)\n            ) {\n                obj = [];\n                obj[index] = leaf;\n            } else if (decodedRoot !== '__proto__') {\n                obj[decodedRoot] = leaf;\n            }\n        }\n\n        leaf = obj;\n    }\n\n    return leaf;\n};\n\nvar parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {\n    if (!givenKey) {\n        return;\n    }\n\n    // Transform dot notation to bracket notation\n    var key = options.allowDots ? givenKey.replace(/\\.([^.[]+)/g, '[$1]') : givenKey;\n\n    // The regex chunks\n\n    var brackets = /(\\[[^[\\]]*])/;\n    var child = /(\\[[^[\\]]*])/g;\n\n    // Get the parent\n\n    var segment = options.depth > 0 && brackets.exec(key);\n    var parent = segment ? key.slice(0, segment.index) : key;\n\n    // Stash the parent if it exists\n\n    var keys = [];\n    if (parent) {\n        // If we aren't using plain objects, optionally prefix keys that would overwrite object prototype properties\n        if (!options.plainObjects && has.call(Object.prototype, parent)) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n\n        keys.push(parent);\n    }\n\n    // Loop through children appending to the array until we hit depth\n\n    var i = 0;\n    while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {\n        i += 1;\n        if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {\n            if (!options.allowPrototypes) {\n                return;\n            }\n        }\n        keys.push(segment[1]);\n    }\n\n    // If there's a remainder, check strictDepth option for throw, else just add whatever is left\n\n    if (segment) {\n        if (options.strictDepth === true) {\n            throw new RangeError('Input depth exceeded depth option of ' + options.depth + ' and strictDepth is true');\n        }\n        keys.push('[' + key.slice(segment.index) + ']');\n    }\n\n    return parseObject(keys, val, options, valuesParsed);\n};\n\nvar normalizeParseOptions = function normalizeParseOptions(opts) {\n    if (!opts) {\n        return defaults;\n    }\n\n    if (typeof opts.allowEmptyArrays !== 'undefined' && typeof opts.allowEmptyArrays !== 'boolean') {\n        throw new TypeError('`allowEmptyArrays` option can only be `true` or `false`, when provided');\n    }\n\n    if (typeof opts.decodeDotInKeys !== 'undefined' && typeof opts.decodeDotInKeys !== 'boolean') {\n        throw new TypeError('`decodeDotInKeys` option can only be `true` or `false`, when provided');\n    }\n\n    if (opts.decoder !== null && typeof opts.decoder !== 'undefined' && typeof opts.decoder !== 'function') {\n        throw new TypeError('Decoder has to be a function.');\n    }\n\n    if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {\n        throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');\n    }\n\n    if (typeof opts.throwOnLimitExceeded !== 'undefined' && typeof opts.throwOnLimitExceeded !== 'boolean') {\n        throw new TypeError('`throwOnLimitExceeded` option must be a boolean');\n    }\n\n    var charset = typeof opts.charset === 'undefined' ? defaults.charset : opts.charset;\n\n    var duplicates = typeof opts.duplicates === 'undefined' ? defaults.duplicates : opts.duplicates;\n\n    if (duplicates !== 'combine' && duplicates !== 'first' && duplicates !== 'last') {\n        throw new TypeError('The duplicates option must be either combine, first, or last');\n    }\n\n    var allowDots = typeof opts.allowDots === 'undefined' ? opts.decodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;\n\n    return {\n        allowDots: allowDots,\n        allowEmptyArrays: typeof opts.allowEmptyArrays === 'boolean' ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,\n        allowPrototypes: typeof opts.allowPrototypes === 'boolean' ? opts.allowPrototypes : defaults.allowPrototypes,\n        allowSparse: typeof opts.allowSparse === 'boolean' ? opts.allowSparse : defaults.allowSparse,\n        arrayLimit: typeof opts.arrayLimit === 'number' ? opts.arrayLimit : defaults.arrayLimit,\n        charset: charset,\n        charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults.charsetSentinel,\n        comma: typeof opts.comma === 'boolean' ? opts.comma : defaults.comma,\n        decodeDotInKeys: typeof opts.decodeDotInKeys === 'boolean' ? opts.decodeDotInKeys : defaults.decodeDotInKeys,\n        decoder: typeof opts.decoder === 'function' ? opts.decoder : defaults.decoder,\n        delimiter: typeof opts.delimiter === 'string' || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,\n        // eslint-disable-next-line no-implicit-coercion, no-extra-parens\n        depth: (typeof opts.depth === 'number' || opts.depth === false) ? +opts.depth : defaults.depth,\n        duplicates: duplicates,\n        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,\n        interpretNumericEntities: typeof opts.interpretNumericEntities === 'boolean' ? opts.interpretNumericEntities : defaults.interpretNumericEntities,\n        parameterLimit: typeof opts.parameterLimit === 'number' ? opts.parameterLimit : defaults.parameterLimit,\n        parseArrays: opts.parseArrays !== false,\n        plainObjects: typeof opts.plainObjects === 'boolean' ? opts.plainObjects : defaults.plainObjects,\n        strictDepth: typeof opts.strictDepth === 'boolean' ? !!opts.strictDepth : defaults.strictDepth,\n        strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults.strictNullHandling,\n        throwOnLimitExceeded: typeof opts.throwOnLimitExceeded === 'boolean' ? opts.throwOnLimitExceeded : false\n    };\n};\n\nmodule.exports = function (str, opts) {\n    var options = normalizeParseOptions(opts);\n\n    if (str === '' || str === null || typeof str === 'undefined') {\n        return options.plainObjects ? { __proto__: null } : {};\n    }\n\n    var tempObj = typeof str === 'string' ? parseValues(str, options) : str;\n    var obj = options.plainObjects ? { __proto__: null } : {};\n\n    // Iterate over the keys and setup the new object\n\n    var keys = Object.keys(tempObj);\n    for (var i = 0; i < keys.length; ++i) {\n        var key = keys[i];\n        var newObj = parseKeys(key, tempObj[key], options, typeof str === 'string');\n        obj = utils.merge(obj, newObj, options);\n    }\n\n    if (options.allowSparse === true) {\n        return obj;\n    }\n\n    return utils.compact(obj);\n};\n", "'use strict';\n\nvar stringify = require('./stringify');\nvar parse = require('./parse');\nvar formats = require('./formats');\n\nmodule.exports = {\n    formats: formats,\n    parse: parse,\n    stringify: stringify\n};\n", "export function getSearchString() {\n    const search = window.location.search;\n    const hash = window.location.hash.substring(1);\n\n    return search && search !== ''\n        ? search\n        : hash.includes('?')\n        ? hash.split('?')[1]\n        : hash;\n}\n", "import { Provider, SupabaseClient, User } from '@supabase/supabase-js';\nimport { AuthProvider, UserIdentity } from 'ra-core';\nimport { getSearchString } from './getSearchString';\n\nexport const supabaseAuthProvider = (\n    client: SupabaseClient,\n    { getIdentity, getPermissions, redirectTo }: SupabaseAuthProviderOptions\n): SupabaseAuthProvider => {\n    const authProvider: SupabaseAuthProvider = {\n        async login(params) {\n            const emailPasswordParams = params as LoginWithEmailPasswordParams;\n            if (emailPasswordParams.email && emailPasswordParams.password) {\n                const { error } = await client.auth.signInWithPassword(\n                    emailPasswordParams\n                );\n\n                if (error) {\n                    throw error;\n                }\n\n                return;\n            }\n\n            const oauthParams = params as LoginWithOAuthParams;\n            if (oauthParams.provider) {\n                client.auth.signInWithOAuth({\n                    ...oauthParams,\n                    options: { redirectTo },\n                });\n                // To avoid react-admin to consider this as an immediate success,\n                // we return a rejected promise that is handled by the default OAuth login buttons\n                return Promise.reject();\n            }\n            return Promise.reject(new Error('Invalid login parameters'));\n        },\n        async setPassword({\n            access_token,\n            refresh_token,\n            password,\n        }: SetPasswordParams) {\n            const { error: sessionError } = await client.auth.setSession({\n                access_token,\n                refresh_token,\n            });\n\n            if (sessionError) {\n                throw sessionError;\n            }\n            const { error } = await client.auth.updateUser({\n                password,\n            });\n\n            if (error) {\n                throw error;\n            }\n            return undefined;\n        },\n        async resetPassword(params: ResetPasswordParams) {\n            const { email, ...options } = params;\n\n            const { error } = await client.auth.resetPasswordForEmail(\n                email,\n                options\n            );\n\n            if (error) {\n                throw error;\n            }\n            return undefined;\n        },\n        async logout() {\n            const { error } = await client.auth.signOut();\n            if (error) {\n                throw error;\n            }\n        },\n        async checkError(error) {\n            if (\n                error.status === 401 ||\n                error.status === 403 ||\n                // Supabase returns 400 when the session is missing, we need to check this case too.\n                (error.status === 400 &&\n                    error.name === 'AuthSessionMissingError')\n            ) {\n                return Promise.reject();\n            }\n\n            return Promise.resolve();\n        },\n        async handleCallback() {\n            const { access_token, refresh_token, type } = getUrlParams();\n\n            // Users have reset their password or have just been invited and must set a new password\n            if (type === 'recovery' || type === 'invite') {\n                if (access_token && refresh_token) {\n                    return {\n                        redirectTo: () => ({\n                            pathname: redirectTo\n                                ? `${redirectTo}/set-password`\n                                : '/set-password',\n                            search: `access_token=${access_token}&refresh_token=${refresh_token}&type=${type}`,\n                        }),\n                    };\n                }\n\n                if (process.env.NODE_ENV === 'development') {\n                    console.error(\n                        'Missing access_token or refresh_token for an invite or recovery'\n                    );\n                }\n            }\n        },\n        async checkAuth() {\n            // Users are on the set-password page, nothing to do\n            if (\n                window.location.pathname === '/set-password' ||\n                window.location.hash.includes('#/set-password')\n            ) {\n                return;\n            }\n            // Users are on the forgot-password page, nothing to do\n            if (\n                window.location.pathname === '/forgot-password' ||\n                window.location.hash.includes('#/forgot-password')\n            ) {\n                return;\n            }\n\n            const { access_token, refresh_token, type } = getUrlParams();\n            // Users have reset their password or have just been invited and must set a new password\n            if (type === 'recovery' || type === 'invite') {\n                if (access_token && refresh_token) {\n                    // eslint-disable-next-line no-throw-literal\n                    throw {\n                        redirectTo: () => ({\n                            pathname: redirectTo\n                                ? `${redirectTo}/set-password`\n                                : '/set-password',\n                            search: `access_token=${access_token}&refresh_token=${refresh_token}&type=${type}`,\n                        }),\n                        message: false,\n                    };\n                }\n\n                if (process.env.NODE_ENV === 'development') {\n                    console.error(\n                        'Missing access_token or refresh_token for an invite or recovery'\n                    );\n                }\n            }\n\n            const { data } = await client.auth.getSession();\n            if (data.session == null) {\n                return Promise.reject();\n            }\n\n            return Promise.resolve();\n        },\n        async getPermissions() {\n            if (typeof getPermissions !== 'function') {\n                return;\n            }\n            // No permissions when users are on the set-password page\n            // or on the forgot-password page.\n            if (\n                window.location.pathname === '/set-password' ||\n                window.location.hash.includes('#/set-password') ||\n                window.location.pathname === '/forgot-password' ||\n                window.location.hash.includes('#/forgot-password')\n            ) {\n                return;\n            }\n\n            const { data, error } = await client.auth.getUser();\n            if (error || data.user == null) {\n                return;\n            }\n\n            const permissions = await getPermissions(data.user);\n            return permissions;\n        },\n    };\n\n    if (typeof getIdentity === 'function') {\n        authProvider.getIdentity = async () => {\n            const { data } = await client.auth.getUser();\n            if (data.user == null) {\n                throw new Error();\n            }\n\n            const identity = await getIdentity(data.user);\n            return identity;\n        };\n    }\n\n    return authProvider;\n};\n\nexport type GetIdentity = (user: User) => Promise<UserIdentity>;\nexport type GetPermissions = (user: User) => Promise<any>;\nexport type SupabaseAuthProviderOptions = {\n    getIdentity?: GetIdentity;\n    getPermissions?: GetPermissions;\n    redirectTo?: string;\n};\n\ntype LoginWithEmailPasswordParams = {\n    email: string;\n    password: string;\n};\n\ntype LoginWithOAuthParams = {\n    provider: Provider;\n};\n\ntype LoginWithMagicLink = {\n    email: string;\n};\n\nexport interface SupabaseAuthProvider extends AuthProvider {\n    login: (\n        params:\n            | LoginWithEmailPasswordParams\n            | LoginWithMagicLink\n            | LoginWithOAuthParams\n    ) => ReturnType<AuthProvider['login']>;\n    setPassword: (params: SetPasswordParams) => Promise<void>;\n    resetPassword: (params: ResetPasswordParams) => Promise<void>;\n}\n\nexport type SetPasswordParams = {\n    access_token: string;\n    refresh_token: string;\n    password: string;\n};\n\nexport type ResetPasswordParams = {\n    email: string;\n    redirectTo?: string;\n    captchaToken?: string;\n};\n\nconst getUrlParams = () => {\n    const searchStr = getSearchString();\n    const urlSearchParams = new URLSearchParams(searchStr);\n    const access_token = urlSearchParams.get('access_token');\n    const refresh_token = urlSearchParams.get('refresh_token');\n    const type = urlSearchParams.get('type');\n\n    return { access_token, refresh_token, type };\n};\n", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n/**\n * List of all possible operators in PostgRest\n * (https://postgrest.org/en/stable/api.html#operators)\n */\nvar postgrestOperators = [\n    'eq',\n    'gt',\n    'gte',\n    'lt',\n    'lte',\n    'neq',\n    'like',\n    'ilike',\n    'match',\n    'imatch',\n    'in',\n    'is',\n    'fts',\n    'plfts',\n    'phfts',\n    'wfts',\n    'cs',\n    'cd',\n    'ov',\n    'sl',\n    'sr',\n    'nxr',\n    'nxl',\n    'adj',\n    'not',\n    'or',\n    'and',\n];\nvar isObject = function (obj) {\n    return typeof obj === 'object' && !Array.isArray(obj) && obj !== null;\n};\nvar resolveKeys = function (filter, keys) {\n    var result = filter[keys[0]];\n    for (var i = 1; i < keys.length; ++i) {\n        result = result[keys[i]];\n    }\n    return result;\n};\nexport var parseFilters = function (params, defaultListOp) {\n    var filter = params.filter, _a = params.meta, meta = _a === void 0 ? {} : _a;\n    var result = {};\n    result.filter = {};\n    Object.keys(filter).forEach(function (key) {\n        // key: the name of the object key\n        var keyArray = [key];\n        var values;\n        // in case of a nested object selection in a text field\n        // RA returns object{ object } structure which is resolved here\n        // see issue https://github.com/raphiniert-com/ra-data-postgrest/issues/58\n        if (key.split('@')[0] !== '' && isObject(filter[key])) {\n            var innerVal = filter[key];\n            do {\n                var inner = resolveKeys(filter, keyArray);\n                var innerKey = Object.keys(inner)[0];\n                keyArray.push(innerKey);\n                innerVal = inner[innerKey];\n            } while (isObject(innerVal));\n            key = keyArray.join('.');\n            values = [innerVal];\n        }\n        else {\n            values = [filter[key]];\n        }\n        var splitKey = key.split('@');\n        var operation = splitKey.length == 2\n            ? splitKey[1]\n            : defaultListOp;\n        if (['like', 'ilike'].includes(operation)) {\n            // we split the search term in words\n            values = resolveKeys(filter, keyArray).trim().split(/\\s+/);\n        }\n        // CASE: Logical operator\n        else if (['or', 'and'].includes(operation)) {\n            // we extract each value entries and make it dot separated string.\n            var subFilter = parseFilters({ filter: resolveKeys(filter, keyArray) }, defaultListOp).filter;\n            // something like { \"age@lt\": 18 } and { \"age@gt\": 21 }\n            var filterExpressions_1 = [];\n            Object.entries(subFilter).forEach(function (_a) {\n                var op = _a[0], val = _a[1];\n                if (Array.isArray(val))\n                    filterExpressions_1.push.apply(filterExpressions_1, val.map(function (v) { return [op, v].join('.'); }));\n                else\n                    filterExpressions_1.push([op, val].join('.'));\n            });\n            // finally we flatten all as single string and enclose with bracket.\n            values = [\"(\".concat(filterExpressions_1.join(','), \")\")];\n        }\n        values.forEach(function (value) {\n            var op = (function () {\n                // if operator is intentionally blank, rpc syntax\n                if (operation.length === 0)\n                    return \"\".concat(value);\n                if (operation.includes('like'))\n                    return \"\".concat(operation, \".*\").concat(value, \"*\");\n                if (['and', 'or'].includes(operation))\n                    return \"\".concat(value);\n                return \"\".concat(operation, \".\").concat(value);\n            })();\n            // If resulting filter doesn't contain the fieldname, then add it.\n            if (result.filter[splitKey[0]] === undefined) {\n                // first operator for the key,\n                if (['and', 'or'].includes(operation)) {\n                    result.filter[operation] = op;\n                }\n                else {\n                    // we add it to the dict\n                    result.filter[splitKey[0]] = op;\n                }\n            }\n            else {\n                if (!Array.isArray(result[splitKey[0]])) {\n                    // second operator, we transform to an array\n                    result.filter[splitKey[0]] = [\n                        result.filter[splitKey[0]],\n                        op,\n                    ];\n                }\n                else {\n                    // third and subsequent, we add to array\n                    result.filter[splitKey[0]].push(op);\n                }\n            }\n        });\n    });\n    if (meta === null || meta === void 0 ? void 0 : meta.columns) {\n        result.select = Array.isArray(meta.columns)\n            ? meta.columns.join(',')\n            : meta.columns;\n    }\n    return result;\n};\nexport var getPrimaryKey = function (resource, primaryKeys) {\n    return (primaryKeys && primaryKeys.get(resource)) || ['id'];\n};\nexport var decodeId = function (id, primaryKey) {\n    if (isCompoundKey(primaryKey)) {\n        return JSON.parse(id.toString());\n    }\n    else {\n        return [id.toString()];\n    }\n};\nexport var encodeId = function (data, primaryKey) {\n    if (isCompoundKey(primaryKey)) {\n        return JSON.stringify(primaryKey.map(function (key) { return data[key]; }));\n    }\n    else {\n        return data[primaryKey[0]];\n    }\n};\nexport var removePrimaryKey = function (data, primaryKey) {\n    var newData = __assign({}, data);\n    primaryKey.forEach(function (key) {\n        delete newData[key];\n    });\n    return newData;\n};\nexport var dataWithVirtualId = function (data, primaryKey) {\n    if (primaryKey.length === 1 && primaryKey[0] === 'id') {\n        return data;\n    }\n    return Object.assign(data, {\n        id: encodeId(data, primaryKey),\n    });\n};\nexport var dataWithoutVirtualId = function (data, primaryKey) {\n    if (primaryKey.length === 1 && primaryKey[0] === 'id') {\n        return data;\n    }\n    var id = data.id, dataWithoutId = __rest(data, [\"id\"]);\n    return dataWithoutId;\n};\nvar isCompoundKey = function (primaryKey) {\n    return primaryKey.length > 1;\n};\nexport var getQuery = function (primaryKey, ids, resource, meta) {\n    var _a, _b;\n    if (meta === void 0) { meta = null; }\n    var result = {};\n    if (Array.isArray(ids) && ids.length > 1) {\n        // no standardized query with multiple ids possible for rpc endpoints which are api-exposed database functions\n        if (resource.startsWith('rpc/')) {\n            console.error(\"PostgREST's rpc endpoints are not intended to be handled as views. Therefore, no query generation for multiple key values implemented!\");\n            return;\n        }\n        if (isCompoundKey(primaryKey)) {\n            result = {\n                or: \"(\".concat(ids.map(function (id) {\n                    var primaryKeyParams = decodeId(id, primaryKey);\n                    return \"and(\".concat(primaryKey\n                        .map(function (key, i) { return \"\".concat(key, \".eq.\").concat(primaryKeyParams[i]); })\n                        .join(','), \")\");\n                }), \")\"),\n            };\n        }\n        else {\n            result = (_a = {},\n                _a[primaryKey[0]] = \"in.(\".concat(ids.join(','), \")\"),\n                _a);\n        }\n    }\n    else if (ids) {\n        // if ids is one Identifier\n        var id = ids.toString();\n        var primaryKeyParams_1 = decodeId(id, primaryKey);\n        if (isCompoundKey(primaryKey)) {\n            if (resource.startsWith('rpc/')) {\n                result = {};\n                primaryKey.map(function (key, i) {\n                    return (result[key] = \"\".concat(primaryKeyParams_1[i]));\n                });\n            }\n            else {\n                result = {\n                    and: \"(\".concat(primaryKey.map(function (key, i) {\n                        return \"\".concat(key, \".eq.\").concat(primaryKeyParams_1[i]);\n                    }), \")\"),\n                };\n            }\n        }\n        else {\n            result = (_b = {},\n                _b[primaryKey[0]] = \"eq.\".concat(id),\n                _b);\n        }\n    }\n    if (meta && meta.columns) {\n        result.select = Array.isArray(meta.columns)\n            ? meta.columns.join(',')\n            : meta.columns;\n    }\n    return result;\n};\nexport var getOrderBy = function (field, order, primaryKey, sortOrder) {\n    if (sortOrder === void 0) { sortOrder = \"asc,desc\" /* PostgRestSortOrder.AscendingNullsLastDescendingNullsFirst */; }\n    var postgRestOrder = sortOrder.split(',')[order === 'ASC' ? 0 : 1];\n    if (field == 'id') {\n        return primaryKey.map(function (key) { return \"\".concat(key, \".\").concat(postgRestOrder); }).join(',');\n    }\n    else {\n        return \"\".concat(field, \".\").concat(postgRestOrder);\n    }\n};\n", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { getPrimaryKey, parseFilters, getOrderBy, dataWithVirtualId, dataWithoutVirtualId, removePrimaryKey, getQuery, encodeId, } from './urlBuilder';\nimport qs from 'qs';\nimport isEqual from 'lodash/isEqual';\nexport var defaultPrimaryKeys = new Map();\nexport var defaultSchema = function () { return ''; };\nvar useCustomSchema = function (schema, metaSchema, method) {\n    var _a;\n    var funcHeaderSchema = schema;\n    if (metaSchema !== undefined) {\n        funcHeaderSchema = function () { return metaSchema; };\n    }\n    if (funcHeaderSchema().length > 0) {\n        var schemaHeader = '';\n        if (['GET', 'HEAD'].includes(method)) {\n            schemaHeader = 'Accept-Profile';\n        }\n        else if (['POST', 'PATCH', 'PUT', 'DELETE'].includes(method)) {\n            schemaHeader = 'Content-Profile';\n        }\n        else\n            return {};\n        return _a = {}, _a[schemaHeader] = funcHeaderSchema(), _a;\n    }\n    else\n        return {};\n};\nexport default (function (config) { return ({\n    getList: function (resource, params) {\n        var _a, _b, _c, _d, _e, _f;\n        if (params === void 0) { params = {}; }\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var _g = params.pagination, page = _g.page, perPage = _g.perPage;\n        var _h = params.sort || {}, field = _h.field, order = _h.order;\n        var _j = parseFilters(params, config.defaultListOp), filter = _j.filter, select = _j.select;\n        var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;\n        var query = __assign({ offset: String((page - 1) * perPage), limit: String(perPage) }, filter);\n        if (field) {\n            query.order = getOrderBy(field, order, primaryKey, config.sortOrder);\n            if (((_b = params.meta) === null || _b === void 0 ? void 0 : _b.nullsfirst) &&\n                !query.order.includes('nullsfirst')) {\n                query.order = query.order.includes('nullslast')\n                    ? query.order.replace('nullslast', 'nullsfirst')\n                    : \"\".concat(query.order, \".nullsfirst\");\n            }\n            if (((_c = params.meta) === null || _c === void 0 ? void 0 : _c.nullsfirst) === false &&\n                query.order.includes('nullsfirst')) {\n                query.order = query.order.replace('.nullsfirst', '');\n            }\n            if (((_d = params.meta) === null || _d === void 0 ? void 0 : _d.nullslast) && !query.order.includes('nullslast')) {\n                query.order = query.order.includes('nullsfirst')\n                    ? query.order.replace('nullsfirst', 'nullslast')\n                    : \"\".concat(query.order, \".nullslast\");\n            }\n            if (((_e = params.meta) === null || _e === void 0 ? void 0 : _e.nullslast) === false &&\n                query.order.includes('nullslast')) {\n                query.order = query.order.replace('.nullslast', '');\n            }\n        }\n        if (select) {\n            query.select = select;\n        }\n        // add header that Content-Range is in returned header\n        var options = {\n            headers: new Headers(__assign(__assign({ Accept: 'application/json', Prefer: 'count=exact' }, (((_f = params.meta) === null || _f === void 0 ? void 0 : _f.headers) || {})), useCustomSchema(config.schema, metaSchema, 'GET'))),\n        };\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource, \"?\").concat(qs.stringify(query));\n        return config.httpClient(url, options).then(function (_a) {\n            var headers = _a.headers, json = _a.json;\n            if (!headers.has('content-range')) {\n                throw new Error(\"The Content-Range header is missing in the HTTP Response. The postgREST data provider expects\\n          responses for lists of resources to contain this header with the total number of results to build\\n          the pagination. If you are using CORS, did you declare Content-Range in the Access-Control-Expose-Headers header?\");\n            }\n            return {\n                data: json.map(function (obj) { return dataWithVirtualId(obj, primaryKey); }),\n                total: parseInt(headers.get('content-range').split('/').pop(), 10),\n            };\n        });\n    },\n    getOne: function (resource, params) {\n        var _a, _b;\n        if (params === void 0) { params = {}; }\n        var id = params.id, meta = params.meta;\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var query = getQuery(primaryKey, id, resource, meta);\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource, \"?\").concat(qs.stringify(query));\n        var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;\n        return config\n            .httpClient(url, {\n            headers: new Headers(__assign(__assign({ accept: 'application/vnd.pgrst.object+json' }, (((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {})), useCustomSchema(config.schema, metaSchema, 'GET'))),\n        })\n            .then(function (_a) {\n            var json = _a.json;\n            return ({\n                data: dataWithVirtualId(json, primaryKey),\n            });\n        });\n    },\n    getMany: function (resource, params) {\n        var _a;\n        if (params === void 0) { params = {}; }\n        var ids = params.ids;\n        if (ids.length === 0) {\n            return Promise.resolve({ data: [] });\n        }\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var query = getQuery(primaryKey, ids, resource, params.meta);\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource, \"?\").concat(qs.stringify(query));\n        var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;\n        return config\n            .httpClient(url, {\n            headers: new Headers(__assign({}, useCustomSchema(config.schema, metaSchema, 'GET'))),\n        })\n            .then(function (_a) {\n            var json = _a.json;\n            return ({\n                data: json.map(function (data) { return dataWithVirtualId(data, primaryKey); }),\n            });\n        });\n    },\n    getManyReference: function (resource, params) {\n        var _a;\n        var _b, _c;\n        if (params === void 0) { params = {}; }\n        var _d = params.pagination, page = _d.page, perPage = _d.perPage;\n        var _e = params.sort, field = _e.field, order = _e.order;\n        var _f = parseFilters(params, config.defaultListOp), filter = _f.filter, select = _f.select;\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var metaSchema = (_b = params.meta) === null || _b === void 0 ? void 0 : _b.schema;\n        var query = params.target\n            ? __assign((_a = {}, _a[params.target] = \"eq.\".concat(params.id), _a.order = getOrderBy(field, order, primaryKey, config.sortOrder), _a.offset = String((page - 1) * perPage), _a.limit = String(perPage), _a), filter) : __assign({ order: getOrderBy(field, order, primaryKey), offset: String((page - 1) * perPage), limit: String(perPage) }, filter);\n        if (select) {\n            query.select = select;\n        }\n        // add header that Content-Range is in returned header\n        var options = {\n            headers: new Headers(__assign(__assign({ Accept: 'application/json', Prefer: 'count=exact' }, (((_c = params.meta) === null || _c === void 0 ? void 0 : _c.headers) || {})), useCustomSchema(config.schema, metaSchema, 'GET'))),\n        };\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource, \"?\").concat(qs.stringify(query));\n        return config.httpClient(url, options).then(function (_a) {\n            var headers = _a.headers, json = _a.json;\n            if (!headers.has('content-range')) {\n                throw new Error(\"The Content-Range header is missing in the HTTP Response. The postgREST data provider expects\\n          responses for lists of resources to contain this header with the total number of results to build\\n          the pagination. If you are using CORS, did you declare Content-Range in the Access-Control-Expose-Headers header?\");\n            }\n            return {\n                data: json.map(function (data) { return dataWithVirtualId(data, primaryKey); }),\n                total: parseInt(headers.get('content-range').split('/').pop(), 10),\n            };\n        });\n    },\n    update: function (resource, params) {\n        var _a, _b;\n        if (params === void 0) { params = {}; }\n        var id = params.id, data = params.data, meta = params.meta, previousData = params.previousData;\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var query = getQuery(primaryKey, id, resource, meta);\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource, \"?\").concat(qs.stringify(query));\n        var changedData = getChanges(data, previousData);\n        if (Object.keys(changedData).length === 0) {\n            return Promise.resolve({ data: __assign({}, previousData) });\n        }\n        var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;\n        var body = JSON.stringify(__assign({}, dataWithoutVirtualId(removePrimaryKey(changedData, primaryKey), primaryKey)));\n        return config\n            .httpClient(url, {\n            method: 'PATCH',\n            headers: new Headers(__assign(__assign({ Accept: 'application/vnd.pgrst.object+json', Prefer: 'return=representation', 'Content-Type': 'application/json' }, (((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {})), useCustomSchema(config.schema, metaSchema, 'PATCH'))),\n            body: body,\n        })\n            .then(function (_a) {\n            var json = _a.json;\n            return ({\n                data: dataWithVirtualId(json, primaryKey),\n            });\n        });\n    },\n    updateMany: function (resource, params) {\n        var _a, _b;\n        if (params === void 0) { params = {}; }\n        var ids = params.ids, meta = params.meta, data = params.data;\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var query = getQuery(primaryKey, ids, resource, meta);\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource, \"?\").concat(qs.stringify(query));\n        var body = JSON.stringify(__assign({}, dataWithoutVirtualId(removePrimaryKey(data, primaryKey), primaryKey)));\n        var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;\n        return config\n            .httpClient(url, {\n            method: 'PATCH',\n            headers: new Headers(__assign(__assign({ Prefer: 'return=representation', 'Content-Type': 'application/json' }, (((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {})), useCustomSchema(config.schema, metaSchema, 'PATCH'))),\n            body: body,\n        })\n            .then(function (_a) {\n            var json = _a.json;\n            return ({\n                data: json.map(function (data) { return encodeId(data, primaryKey); }),\n            });\n        });\n    },\n    create: function (resource, params) {\n        var _a, _b;\n        if (params === void 0) { params = {}; }\n        var meta = params.meta;\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var query = getQuery(primaryKey, undefined, resource, meta);\n        var queryStr = qs.stringify(query);\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource).concat(queryStr.length > 0 ? '?' : '').concat(queryStr);\n        var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;\n        return config\n            .httpClient(url, {\n            method: 'POST',\n            headers: new Headers(__assign(__assign({ Accept: 'application/vnd.pgrst.object+json', Prefer: 'return=representation', 'Content-Type': 'application/json' }, (((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {})), useCustomSchema(config.schema, metaSchema, 'POST'))),\n            body: JSON.stringify(dataWithoutVirtualId(params.data, primaryKey)),\n        })\n            .then(function (_a) {\n            var json = _a.json;\n            return ({\n                data: __assign(__assign({}, json), { id: encodeId(json, primaryKey) }),\n            });\n        });\n    },\n    delete: function (resource, params) {\n        var _a, _b;\n        if (params === void 0) { params = {}; }\n        var id = params.id, meta = params.meta;\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var query = getQuery(primaryKey, id, resource, meta);\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource, \"?\").concat(qs.stringify(query));\n        var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;\n        return config\n            .httpClient(url, {\n            method: 'DELETE',\n            headers: new Headers(__assign(__assign({ Accept: 'application/vnd.pgrst.object+json', Prefer: 'return=representation', 'Content-Type': 'application/json' }, (((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {})), useCustomSchema(config.schema, metaSchema, 'DELETE'))),\n        })\n            .then(function (_a) {\n            var json = _a.json;\n            return ({\n                data: dataWithVirtualId(json, primaryKey),\n            });\n        });\n    },\n    deleteMany: function (resource, params) {\n        var _a, _b;\n        if (params === void 0) { params = {}; }\n        var ids = params.ids, meta = params.meta;\n        var primaryKey = getPrimaryKey(resource, config.primaryKeys);\n        var query = getQuery(primaryKey, ids, resource, meta);\n        var url = \"\".concat(config.apiUrl, \"/\").concat(resource, \"?\").concat(qs.stringify(query));\n        var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;\n        return config\n            .httpClient(url, {\n            method: 'DELETE',\n            headers: new Headers(__assign(__assign({ Prefer: 'return=representation', 'Content-Type': 'application/json' }, (((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {})), useCustomSchema(config.schema, metaSchema, 'DELETE'))),\n        })\n            .then(function (_a) {\n            var json = _a.json;\n            return ({\n                data: json.map(function (data) { return encodeId(data, primaryKey); }),\n            });\n        });\n    },\n}); });\nvar getChanges = function (data, previousData) {\n    var changes = Object.keys(data).reduce(function (changes, key) {\n        if (!isEqual(data[key], previousData[key])) {\n            changes[key] = data[key];\n        }\n        return changes;\n    }, {});\n    return changes;\n};\n", "import { DataProvider, fetchUtils } from 'ra-core';\nimport postgrestRestProvider, {\n    IDataProviderConfig,\n    defaultPrimaryKeys,\n    defaultSchema,\n} from '@raphiniert/ra-data-postgrest';\nimport { createClient } from '@supabase/supabase-js';\nimport type { SupabaseClient } from '@supabase/supabase-js';\nimport type { OpenAPIV2 } from 'openapi-types';\n\n/**\n * A function that returns a dataProvider for Supabase.\n * @param instanceUrl The URL of the Supabase instance\n * @param apiKey The API key of the Supabase instance. Prefer the anonymous key.\n * @param supabaseClient The Supabase client\n * @param httpClient Optional - The httpClient to use. Defaults to a httpClient that handles the authentication.\n * @param defaultListOp Optional - The default list filter operator. Defaults to 'eq'.\n * @param primaryKeys Optional - The primary keys of the tables. Defaults to 'id'.\n * @param schema Optional - The custom schema to use. Defaults to none.\n * @returns A dataProvider for Supabase\n */\nexport const supabaseDataProvider = ({\n    instanceUrl,\n    apiKey,\n    supabaseClient = createClient(instanceUrl, apiKey),\n    httpClient = supabaseHttpClient({ apiKey, supabaseClient }),\n    defaultListOp = 'eq',\n    primaryKeys = defaultPrimaryKeys,\n    schema = defaultSchema,\n    ...rest\n}: {\n    instanceUrl: string;\n    apiKey: string;\n    supabaseClient?: SupabaseClient;\n} & Partial<Omit<IDataProviderConfig, 'apiUrl'>>): DataProvider => {\n    const config: IDataProviderConfig = {\n        apiUrl: `${instanceUrl}/rest/v1`,\n        httpClient,\n        defaultListOp,\n        primaryKeys,\n        schema,\n        ...rest,\n    };\n    return {\n        supabaseClient: (url: string, options?: any) =>\n            httpClient(`${config.apiUrl}/${url}`, options),\n        getSchema: async (): Promise<OpenAPIV2.Document> => {\n            const { json } = await httpClient(`${config.apiUrl}/`, {});\n            if (!json || !json.swagger) {\n                throw new Error('The Open API schema is not readable');\n            }\n            return json;\n        },\n        ...postgrestRestProvider(config),\n    };\n};\n\n/**\n * A function that returns a httpClient for Supabase. It handles the authentication.\n * @param apiKey The API key of the Supabase instance. Prefer the anonymous key.\n * @param supabaseClient The Supabase client\n * @returns A httpClient for Supabase\n */\nexport const supabaseHttpClient =\n    ({\n        apiKey,\n        supabaseClient,\n    }: {\n        apiKey: string;\n        supabaseClient: SupabaseClient;\n    }) =>\n    async (url: string, options: any = {}) => {\n        const { data } = await supabaseClient.auth.getSession();\n        if (!options.headers) options.headers = new Headers({});\n\n        if (supabaseClient['headers']) {\n            Object.entries(supabaseClient['headers']).forEach(([name, value]) =>\n                options.headers.set(name, value)\n            );\n        }\n        if (data.session) {\n            options.user = {\n                authenticated: true,\n                // This ensures that users are identified correctly and that RLS can be applied\n                token: `Bearer ${data.session.access_token}`,\n            };\n        }\n        // Always send the apiKey even if there isn't a session\n        options.headers.set('apiKey', apiKey);\n\n        return fetchUtils.fetchJson(url, options);\n    };\n", "import { useDataProvider } from 'react-admin';\nimport { useQuery } from '@tanstack/react-query';\nimport type { UseQueryOptions } from '@tanstack/react-query';\nimport type { OpenAPIV2 } from 'openapi-types';\n\nexport const useAPISchema = ({\n    options,\n}: {\n    options?: Partial<\n        Omit<UseQueryOptions<OpenAPIV2.Document>, 'queryKey' | 'queryFn'>\n    >;\n} = {}) => {\n    const dataProvider = useDataProvider();\n    if (!dataProvider.getSchema) {\n        throw new Error(\n            \"The data provider doesn't have access to the database schema\"\n        );\n    }\n    return useQuery<OpenAPIV2.Document>({\n        queryKey: ['getSchema'],\n        queryFn: () => dataProvider.getSchema() as Promise<OpenAPIV2.Document>,\n        staleTime: 1000 * 60, // 1 minute\n        ...options,\n    });\n};\n", "import { useCheckAuth } from 'ra-core';\nimport { useEffect } from 'react';\nimport { To, useNavigate } from 'react-router';\n\n/**\n * This hook redirect the user to the provided path (/ by default) if they are authenticated.\n *\n * @example\n * import { useRedirectIfAuthenticated } from 'react-admin';\n * const MyLoginPage = () => {\n *     useRedirectIfAuthenticated();\n *     // UI and logic for authentication\n * }\n **/\nexport const useRedirectIfAuthenticated = (\n    redirectTo: UseRedirectIfAuthenticatedOptions = '/'\n) => {\n    const navigate = useNavigate();\n    const checkAuth = useCheckAuth();\n\n    useEffect(() => {\n        checkAuth({}, false, undefined)\n            .then(() => {\n                // already authenticated, redirect to the home page\n                navigate(redirectTo);\n            })\n            .catch(() => {\n                // not authenticated, stay on the login page\n            });\n    }, [checkAuth, navigate, redirectTo]);\n};\n\nexport type UseRedirectIfAuthenticatedOptions = To;\n", "import {\n    OnError,\n    OnSuc<PERSON>,\n    useAuthProvider,\n    useNotify,\n    useRedirect,\n} from 'ra-core';\nimport { useMutation, UseMutationResult } from '@tanstack/react-query';\nimport { ResetPasswordParams, SupabaseAuthProvider } from './authProvider';\n\n/**\n * This hook returns a function to call in order to reset a user password on Supabase.\n *\n * @example\n * import { useSupabaseAccessToken } from 'ra-supabase-core';\n *\n * const ResetPasswordPage = () => {\n *     const [resetPassword] = useResetPassword();\n *\n *     const handleSubmit = event => {\n *         resetPassword({\n *             email: event.currentTarget.elements.email.value,\n *         });\n *     };\n *\n *     return (\n *         <form onSubmit={handleSubmit}>\n *             <label for=\"email\">Email:</label>\n *             <input id=\"email\" name=\"email\" type=\"email\" />\n *             <button type=\"submit\">Reset password</button>\n *         </form>\n *     );\n * };\n **/\nexport const useResetPassword = (\n    options?: UseResetPasswordOptions\n): [\n    UseMutationResult<unknown, Error, ResetPasswordParams>['mutate'],\n    UseMutationResult<unknown, Error, ResetPasswordParams>\n] => {\n    const notify = useNotify();\n    const redirect = useRedirect();\n    const authProvider = useAuthProvider<SupabaseAuthProvider>();\n\n    if (authProvider == null) {\n        throw new Error(\n            'No authProvider found. Did you forget to set up an AuthProvider on the <Admin> component?'\n        );\n    }\n\n    if (authProvider.resetPassword == null) {\n        throw new Error(\n            'The setPassword() method is missing from the AuthProvider although it is required. You may consider adding it'\n        );\n    }\n\n    const {\n        onSuccess = () => {\n            redirect('/login');\n            notify('ra-supabase.auth.password_reset', { type: 'info' });\n        },\n        onError = error => notify(error.message, { type: 'error' }),\n    } = options || {};\n\n    const mutation = useMutation<unknown, Error, ResetPasswordParams>({\n        mutationFn: params => {\n            return authProvider.resetPassword(params);\n        },\n        onSuccess,\n        onError,\n        retry: false,\n    });\n\n    return [mutation.mutate, mutation];\n};\n\nexport type UseResetPasswordOptions = {\n    onSuccess?: OnSuccess;\n    onError?: OnError;\n};\n", "import {\n    OnError,\n    OnSuc<PERSON>,\n    useAuth<PERSON>rovider,\n    useNotify,\n    useRedirect,\n} from 'ra-core';\nimport { useMutation, UseMutationResult } from '@tanstack/react-query';\nimport { SetPasswordParams, SupabaseAuthProvider } from './authProvider';\n\n/**\n * This hook returns a function to call in order to set a user password on Supabase.\n *\n * @example\n * import { useSupabaseAccessToken } from 'ra-supabase-core';\n *\n * const SetPasswordPage = () => {\n *     const access_token = useSupabaseAccessToken();\n *     const setPassword = useSetPassword();\n *\n *     const handleSubmit = event => {\n *         setPassword({\n *             access_token,\n *             password: event.currentTarget.elements.password.value,\n *         });\n *     };\n *\n *     return (\n *         <form onSubmit={handleSubmit}>\n *             <label for=\"password\">Choose a password:</label>\n *             <input id=\"password\" name=\"password\" type=\"password\" />\n *             <button type=\"submit\">Save</button>\n *         </form>\n *     );\n * };\n **/\nexport const useSetPassword = (\n    options?: UseSetPasswordOptions\n): [\n    UseMutationResult<unknown, Error, SetPasswordParams>['mutate'],\n    UseMutationResult<unknown, Error, SetPasswordParams>\n] => {\n    const notify = useNotify();\n    const redirect = useRedirect();\n    const authProvider = useAuthProvider<SupabaseAuthProvider>();\n\n    if (authProvider == null) {\n        throw new Error(\n            'No authProvider found. Did you forget to set up an AuthProvider on the <Admin> component?'\n        );\n    }\n\n    if (authProvider.setPassword == null) {\n        throw new Error(\n            'The setPassword() method is missing from the AuthProvider although it is required. You may consider adding it'\n        );\n    }\n\n    const {\n        onSuccess = () => redirect('/'),\n        onError = error => notify(error.message, { type: 'error' }),\n    } = options || {};\n\n    const mutation = useMutation<unknown, Error, SetPasswordParams>({\n        mutationFn: params => {\n            return authProvider.setPassword(params);\n        },\n        onSuccess,\n        onError,\n        retry: false,\n    });\n\n    return [mutation.mutate, mutation];\n};\n\nexport type UseSetPasswordOptions = {\n    onSuccess?: OnSuccess;\n    onError?: OnError;\n};\n", "import { useRedirect } from 'ra-core';\nimport { useEffect } from 'react';\nimport { getSearchString } from './getSearchString';\n\n/**\n * This hook gets the access_token from supabase in the current browser URL and redirects to the specified page (/ by default) if there is none.\n * To be used in pages such as those which set the password after a reset or an invitation.\n *\n * @example\n * import { useSupabaseAccessToken } from 'ra-supabase-core';\n *\n * const SetPasswordPage = () => {\n *     const access_token = useSupabaseAccessToken();\n *     const setPassword = useSetPassword();\n *\n *     const handleSubmit = event => {\n *         setPassword({\n *             access_token,\n *             password: event.currentTarget.elements.password.value,\n *         });\n *     };\n *\n *     return (\n *         <form onSubmit={handleSubmit}>\n *             <label for=\"password\">Choose a password:</label>\n *             <input id=\"password\" name=\"password\" type=\"password\" />\n *             <button type=\"submit\">Save</button>\n *         </form>\n *     );\n * };\n **/\nexport const useSupabaseAccessToken = ({\n    redirectTo = '/',\n    parameterName = 'access_token',\n}: UseSupabaseAccessTokenOptions = {}) => {\n    const redirect = useRedirect();\n\n    const searchStr = getSearchString();\n    const urlSearchParams = new URLSearchParams(searchStr);\n    const access_token = urlSearchParams.get(parameterName);\n    useEffect(() => {\n        if (access_token == null) {\n            if (redirectTo !== false) {\n                redirect(redirectTo);\n            }\n        }\n    });\n\n    return access_token;\n};\n\nexport type UseSupabaseAccessTokenOptions = {\n    redirectTo?: string | false;\n    parameterName?: string;\n};\n", "import React, {\n    HtmlHTMLAttributes,\n    ComponentType,\n    createElement,\n    ReactNode,\n    useRef,\n    useEffect,\n    useMemo,\n} from 'react';\nimport {\n    Card,\n    Avatar,\n    ThemeProvider,\n    createTheme,\n    styled,\n} from '@mui/material';\nimport LockIcon from '@mui/icons-material/Lock';\nimport { TitleComponent } from 'ra-core';\nimport { defaultTheme, Notification } from 'ra-ui-materialui';\n\n/**\n * A standalone login page, to serve as authentication gate to the admin\n *\n * Expects the user to enter a login and a password, which will be checked\n * by the `authProvider.login()` method. Redirects to the root page (/)\n * upon success, otherwise displays an authentication error message.\n *\n * Copy and adapt this component to implement your own login logic\n * (e.g. to authenticate via email or facebook or anything else).\n *\n * @example\n *     import MyLoginPage from './MyLoginPage';\n *     const App = () => (\n *         <Admin loginPage={MyLoginPage} authProvider={authProvider}>\n *             ...\n *        </Admin>\n *     );\n */\nexport const AuthLayout: React.FunctionComponent<LoginProps> = props => {\n    const {\n        theme = defaultTheme,\n        title,\n        classes: classesOverride,\n        className,\n        children,\n        notification = Notification,\n        backgroundImage,\n        ...rest\n    } = props;\n    const containerRef = useRef<HTMLDivElement>(null);\n    const muiTheme = useMemo(() => createTheme(theme), [theme]);\n    let backgroundImageLoaded = false;\n\n    const updateBackgroundImage = () => {\n        if (!backgroundImageLoaded && containerRef.current) {\n            containerRef.current.style.backgroundImage = `url(${backgroundImage})`;\n            backgroundImageLoaded = true;\n        }\n    };\n\n    // Load background image asynchronously to speed up time to interactive\n    const lazyLoadBackgroundImage = () => {\n        if (backgroundImage) {\n            const img = new Image();\n            img.onload = updateBackgroundImage;\n            img.src = backgroundImage;\n        }\n    };\n\n    useEffect(() => {\n        if (!backgroundImageLoaded) {\n            lazyLoadBackgroundImage();\n        }\n    });\n\n    return (\n        <ThemeProvider theme={muiTheme}>\n            <Root {...rest} ref={containerRef}>\n                <Card className={AuthLayoutClasses.card}>\n                    <div className={AuthLayoutClasses.avatar}>\n                        <Avatar className={AuthLayoutClasses.icon}>\n                            <LockIcon />\n                        </Avatar>\n                    </div>\n                    {children}\n                </Card>\n                {notification ? createElement(notification) : null}\n            </Root>\n        </ThemeProvider>\n    );\n};\n\nexport interface LoginProps\n    extends Omit<HtmlHTMLAttributes<HTMLDivElement>, 'title'> {\n    backgroundImage?: string;\n    children?: ReactNode;\n    classes?: object;\n    className?: string;\n    notification?: ComponentType;\n    theme?: object;\n    title?: TitleComponent;\n}\n\nconst PREFIX = 'RaAuthLayout';\n\nexport const AuthLayoutClasses = {\n    card: `${PREFIX}-card`,\n    avatar: `${PREFIX}-avatar`,\n    icon: `${PREFIX}-icon`,\n};\n\nconst Root = styled('div', {\n    name: PREFIX,\n    overridesResolver: (props, styles) => styles.root,\n})(({ theme }) => ({\n    display: 'flex',\n    flexDirection: 'column',\n    minHeight: '100vh',\n    height: '1px',\n    alignItems: 'center',\n    justifyContent: 'flex-start',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: 'cover',\n    backgroundImage:\n        'radial-gradient(circle at 50% 14em, #313264 0%, #00023b 60%, #00023b 100%)',\n    [`& .${AuthLayoutClasses.card}`]: {\n        minWidth: 300,\n        marginTop: '6em',\n    },\n    [`& .${AuthLayoutClasses.avatar}`]: {\n        margin: '1em',\n        display: 'flex',\n        justifyContent: 'center',\n    },\n    [`& .${AuthLayoutClasses.icon}`]: {\n        backgroundColor: theme.palette.grey[500],\n    },\n}));\n", "import * as React from 'react';\nimport type { ReactNode } from 'react';\nimport { useAPISchema } from 'ra-supabase-core';\nimport { CreateBase, useResourceContext } from 'ra-core';\nimport {\n    CreateView,\n    editFieldTypes,\n    InferredElement,\n    Loading,\n} from 'react-admin';\nimport type { CreateProps, CreateViewProps } from 'ra-ui-materialui';\nimport { capitalize, singularize } from 'inflection';\n\nimport { inferElementFromType } from './inferElementFromType';\n\nexport const CreateGuesser = (props: CreateProps & { enableLog?: boolean }) => {\n    const {\n        mutationOptions,\n        resource,\n        record,\n        transform,\n        redirect,\n        disableAuthentication,\n        ...rest\n    } = props;\n    return (\n        <CreateBase\n            resource={resource}\n            record={record}\n            redirect={redirect}\n            transform={transform}\n            mutationOptions={mutationOptions}\n            disableAuthentication={disableAuthentication}\n        >\n            <CreateGuesserView {...rest} />\n        </CreateBase>\n    );\n};\n\nexport const CreateGuesserView = (\n    props: CreateViewProps & {\n        enableLog?: boolean;\n    }\n) => {\n    const { data: schema, error, isPending } = useAPISchema();\n    const resource = useResourceContext();\n    const [child, setChild] = React.useState<ReactNode>(null);\n    if (!resource) {\n        throw new Error('CreateGuesser must be used withing a ResourceContext');\n    }\n    const { enableLog = process.env.NODE_ENV === 'development', ...rest } =\n        props;\n\n    React.useEffect(() => {\n        if (isPending || error) {\n            return;\n        }\n        const resourceDefinition = schema.definitions?.[resource];\n        const requiredFields = resourceDefinition?.required || [];\n        if (!resourceDefinition || !resourceDefinition.properties) {\n            throw new Error(\n                `The resource ${resource} is not defined in the API schema`\n            );\n        }\n        const inferredInputs = Object.keys(resourceDefinition.properties)\n            .filter((source: string) => source !== 'id')\n            .filter(\n                source =>\n                    resourceDefinition.properties![source].format !== 'tsvector'\n            )\n            .map((source: string) =>\n                inferElementFromType({\n                    name: source,\n                    types: editFieldTypes,\n                    description:\n                        resourceDefinition.properties![source].description,\n                    format: resourceDefinition.properties![source].format,\n                    type: (resourceDefinition.properties &&\n                    resourceDefinition.properties[source] &&\n                    typeof resourceDefinition.properties[source].type ===\n                        'string'\n                        ? resourceDefinition.properties![source].type\n                        : 'string') as string,\n                    requiredFields,\n                })\n            );\n        const inferredForm = new InferredElement(\n            editFieldTypes.form,\n            null,\n            inferredInputs\n        );\n        setChild(inferredForm.getElement());\n        if (!enableLog) return;\n\n        const representation = inferredForm.getRepresentation();\n\n        const components = ['Create']\n            .concat(\n                Array.from(\n                    new Set(\n                        Array.from(representation.matchAll(/<([^/\\s>]+)/g))\n                            .map(match => match[1])\n                            .filter(component => component !== 'span')\n                    )\n                )\n            )\n            .sort();\n\n        // eslint-disable-next-line no-console\n        console.log(\n            `Guessed Create:\n            \nimport { ${components.join(', ')} } from 'react-admin';\n            \nexport const ${capitalize(singularize(resource))}Create = () => (\n    <Create>\n${representation}\n    </Create>\n);`\n        );\n    }, [resource, isPending, error, schema, enableLog]);\n\n    if (isPending) return <Loading />;\n    if (error) return <p>Error: {error.message}</p>;\n\n    return <CreateView {...rest}>{child}</CreateView>;\n};\n", "import { InferredElement, required, type InferredTypeMap } from 'ra-core';\nimport { pluralize } from 'inflection';\n\nconst hasType = (type, types) => typeof types[type] !== 'undefined';\n\nexport const inferElementFromType = ({\n    name,\n    description,\n    format,\n    type,\n    requiredFields,\n    types,\n    props,\n}: {\n    name: string;\n    types: InferredTypeMap;\n    description?: string;\n    format?: string;\n    type?: string;\n    requiredFields?: string[];\n    props?: any;\n}) => {\n    if (name === 'id' && hasType('id', types)) {\n        return new InferredElement(types.id, { source: 'id' });\n    }\n    const validate = requiredFields?.includes(name) ? [required()] : undefined;\n    if (\n        description?.startsWith('Note:\\nThis is a Foreign Key to') &&\n        hasType('reference', types)\n    ) {\n        const reference = description.split('`')[1].split('.')[0];\n        return new InferredElement(types.reference, {\n            source: name,\n            reference,\n            ...props,\n        });\n    }\n    if (\n        name.substring(name.length - 4) === '_ids' &&\n        hasType('reference', types)\n    ) {\n        const reference = pluralize(name.substr(0, name.length - 4));\n        return new InferredElement(types.referenceArray, {\n            source: name,\n            reference,\n            ...props,\n        });\n    }\n    if (type === 'array') {\n        // FIXME instrospect further\n        return new InferredElement(types.string, {\n            source: name,\n            validate,\n        });\n    }\n    if (type === 'string') {\n        if (name === 'email' && hasType('email', types)) {\n            return new InferredElement(types.email, {\n                source: name,\n                validate,\n                ...props,\n            });\n        }\n        if (['url', 'website'].includes(name) && hasType('url', types)) {\n            return new InferredElement(types.url, {\n                source: name,\n                validate,\n                ...props,\n            });\n        }\n        if (\n            format &&\n            [\n                'timestamp with time zone',\n                'timestamp without time zone',\n            ].includes(format) &&\n            hasType('date', types)\n        ) {\n            return new InferredElement(types.date, {\n                source: name,\n                validate,\n                ...props,\n            });\n        }\n    }\n    if (type === 'integer' && hasType('number', types)) {\n        return new InferredElement(types.number, {\n            source: name,\n            validate,\n            ...props,\n        });\n    }\n    if (type && hasType(type, types)) {\n        return new InferredElement(types[type], {\n            source: name,\n            validate,\n            ...props,\n        });\n    }\n    return new InferredElement(types.string, {\n        source: name,\n        validate,\n        ...props,\n    });\n};\n", "import * as React from 'react';\nimport type { ReactNode } from 'react';\nimport { useAPISchema } from 'ra-supabase-core';\nimport { EditBase, InferredElement, useResourceContext } from 'ra-core';\nimport {\n    editFieldTypes,\n    type EditProps,\n    EditView,\n    type EditViewProps,\n    Loading,\n} from 'ra-ui-materialui';\nimport { capitalize, singularize } from 'inflection';\n\nimport { inferElementFromType } from './inferElementFromType';\n\nexport const EditGuesser = (props: EditProps & { enableLogs?: boolean }) => {\n    const {\n        resource,\n        id,\n        mutationMode,\n        mutationOptions,\n        queryOptions,\n        redirect,\n        transform,\n        disableAuthentication,\n        ...rest\n    } = props;\n    return (\n        <EditBase\n            resource={resource}\n            id={id}\n            mutationMode={mutationMode}\n            mutationOptions={mutationOptions}\n            queryOptions={queryOptions}\n            redirect={redirect}\n            transform={transform}\n            disableAuthentication={disableAuthentication}\n        >\n            <EditGuesserView {...rest} />\n        </EditBase>\n    );\n};\n\nexport const EditGuesserView = (\n    props: EditViewProps & {\n        enableLog?: boolean;\n    }\n) => {\n    const { data: schema, error, isPending } = useAPISchema();\n    const resource = useResourceContext();\n    const [child, setChild] = React.useState<ReactNode>(null);\n    if (!resource) {\n        throw new Error('EditGuesser must be used withing a ResourceContext');\n    }\n    const { enableLog = process.env.NODE_ENV === 'development', ...rest } =\n        props;\n\n    React.useEffect(() => {\n        if (isPending || error) {\n            return;\n        }\n        const resourceDefinition = schema.definitions?.[resource];\n        const requiredFields = resourceDefinition?.required || [];\n        if (!resourceDefinition || !resourceDefinition.properties) {\n            throw new Error(\n                `The resource ${resource} is not defined in the API schema`\n            );\n        }\n        const inferredInputs = Object.keys(resourceDefinition.properties)\n            .filter((source: string) => source !== 'id')\n            .filter(\n                source =>\n                    resourceDefinition.properties![source].format !== 'tsvector'\n            )\n            .map((source: string) =>\n                inferElementFromType({\n                    name: source,\n                    types: editFieldTypes,\n                    description:\n                        resourceDefinition.properties![source].description,\n                    format: resourceDefinition.properties![source].format,\n                    type: (resourceDefinition.properties &&\n                    resourceDefinition.properties[source] &&\n                    typeof resourceDefinition.properties[source].type ===\n                        'string'\n                        ? resourceDefinition.properties![source].type\n                        : 'string') as string,\n                    requiredFields,\n                })\n            );\n        const inferredForm = new InferredElement(\n            editFieldTypes.form,\n            null,\n            inferredInputs\n        );\n        setChild(inferredForm.getElement());\n        if (!enableLog) return;\n\n        const representation = inferredForm.getRepresentation();\n\n        const components = ['Edit']\n            .concat(\n                Array.from(\n                    new Set(\n                        Array.from(representation.matchAll(/<([^/\\s>]+)/g))\n                            .map(match => match[1])\n                            .filter(component => component !== 'span')\n                    )\n                )\n            )\n            .sort();\n\n        // eslint-disable-next-line no-console\n        console.log(\n            `Guessed Edit:\n            \nimport { ${components.join(', ')} } from 'react-admin';\n            \nexport const ${capitalize(singularize(resource))}Edit = () => (\n    <Edit>\n${representation}\n    </Edit>\n);`\n        );\n    }, [resource, isPending, error, schema, enableLog]);\n\n    if (isPending) return <Loading />;\n    if (error) return <p>Error: {error.message}</p>;\n\n    return <EditView {...rest}>{child}</EditView>;\n};\n", "import * as React from 'react';\nimport type { ReactNode } from 'react';\nimport { useAPISchema } from 'ra-supabase-core';\nimport { InferredElement, ListBase, useResourceContext } from 'ra-core';\nimport {\n    editFieldTypes,\n    listFieldTypes,\n    type ListProps,\n    ListView,\n    type ListViewProps,\n    Loading,\n    SearchInput,\n} from 'ra-ui-materialui';\nimport { capitalize, singularize } from 'inflection';\n\nimport { inferElementFromType } from './inferElementFromType';\n\nexport const ListGuesser = (props: ListProps & { enableLog?: boolean }) => {\n    const {\n        debounce,\n        disableAuthentication,\n        disableSyncWithLocation,\n        exporter,\n        filter,\n        filterDefaultValues,\n        perPage,\n        queryOptions,\n        resource,\n        sort,\n        storeKey,\n        ...rest\n    } = props;\n    return (\n        <ListBase\n            debounce={debounce}\n            disableAuthentication={disableAuthentication}\n            disableSyncWithLocation={disableSyncWithLocation}\n            exporter={exporter}\n            filter={filter}\n            filterDefaultValues={filterDefaultValues}\n            perPage={perPage}\n            queryOptions={queryOptions}\n            resource={resource}\n            sort={sort}\n            storeKey={storeKey}\n        >\n            <ListGuesserView {...rest} />\n        </ListBase>\n    );\n};\n\nexport const ListGuesserView = (\n    props: ListViewProps & {\n        enableLog?: boolean;\n    }\n) => {\n    const { data: schema, error, isPending } = useAPISchema();\n    const resource = useResourceContext();\n    const [child, setChild] = React.useState<ReactNode>(null);\n    const [filters, setFilters] = React.useState<\n        React.ReactElement[] | undefined\n    >(undefined);\n    if (!resource) {\n        throw new Error('ListGuesser must be used withing a ResourceContext');\n    }\n    const { enableLog = process.env.NODE_ENV === 'development', ...rest } =\n        props;\n\n    React.useEffect(() => {\n        if (isPending || error) {\n            return;\n        }\n        const resourceDefinition = schema.definitions?.[resource];\n        if (!resourceDefinition || !resourceDefinition.properties) {\n            throw new Error(\n                `The resource ${resource} is not defined in the API schema`\n            );\n        }\n        const inferredFields = Object.keys(resourceDefinition.properties)\n            .filter(\n                source =>\n                    resourceDefinition.properties![source].format !== 'tsvector'\n            )\n            .map((source: string) =>\n                inferElementFromType({\n                    name: source,\n                    types: listFieldTypes,\n                    description:\n                        resourceDefinition.properties![source].description,\n                    format: resourceDefinition.properties![source].format,\n                    type: (resourceDefinition.properties &&\n                    resourceDefinition.properties[source] &&\n                    typeof resourceDefinition.properties[source].type ===\n                        'string'\n                        ? resourceDefinition.properties![source].type\n                        : 'string') as string,\n                })\n            );\n        const inferredTable = new InferredElement(\n            listFieldTypes.table,\n            null,\n            inferredFields\n        );\n        setChild(inferredTable.getElement());\n\n        const rowFilters =\n            schema!\n                .paths![`/${resource}`].get!.parameters?.filter(obj =>\n                    obj['$ref'].includes('rowFilter')\n                )\n                .map(obj => obj['$ref'].split('.').pop()) ?? [];\n        const inferredInputsForFilters = rowFilters\n            .filter(\n                source =>\n                    resourceDefinition.properties![source].format !== 'tsvector'\n            )\n            .map(source => {\n                const field = resourceDefinition.properties![source];\n                return inferElementFromType({\n                    name: source,\n                    types: editFieldTypes,\n                    description: field.description,\n                    format: field.format,\n                    type: field.type as string,\n                });\n            });\n        if (\n            rowFilters.some(\n                source =>\n                    resourceDefinition.properties![source].format === 'tsvector'\n            )\n        ) {\n            const fullTextSearchSource = rowFilters.find(\n                source =>\n                    resourceDefinition.properties![source].format === 'tsvector'\n            );\n            const field = resourceDefinition.properties![fullTextSearchSource!];\n            inferredInputsForFilters.unshift(\n                inferElementFromType({\n                    name: `${fullTextSearchSource!}@fts`,\n                    types: {\n                        string: {\n                            component: SearchInput,\n                            representation: props =>\n                                `<SearchInput alwaysOn source=\"${props.source}\" />`,\n                        },\n                    },\n                    description: field.description,\n                    format: 'tsvector',\n                    props: {\n                        alwaysOn: true,\n                        parse: value => (value ? `${value}:*` : undefined),\n                        format: value =>\n                            value ? value.substring(0, value.length - 2) : '',\n                    },\n                    type: field.type as string,\n                })\n            );\n        }\n        if (inferredInputsForFilters.length > 0) {\n            const filterElements = inferredInputsForFilters\n                .map(inferredInput => inferredInput.getElement())\n                .filter(el => el != null) as React.ReactElement[];\n            setFilters(filterElements);\n        }\n\n        if (!enableLog) return;\n\n        const tableRepresentation = inferredTable.getRepresentation();\n\n        const filterRepresentation =\n            inferredInputsForFilters.length > 0\n                ? `const filters = [\n${inferredInputsForFilters\n    .map(inferredInput => '    ' + inferredInput.getRepresentation())\n    .join(',\\n')}\n];\n`\n                : '';\n\n        const fieldComponents = Array.from(\n            tableRepresentation.matchAll(/<([^/\\s>]+)/g)\n        )\n            .map(match => match[1])\n            .filter(component => component !== 'span');\n        const filterComponents = Array.from(\n            filterRepresentation.matchAll(/<([^/\\s>]+)/g)\n        )\n            .map(match => match[1])\n            .filter(component => component !== 'span');\n        const components = Array.from(\n            new Set(['List', ...fieldComponents, ...filterComponents])\n        ).sort();\n\n        // eslint-disable-next-line no-console\n        console.log(\n            `Guessed List:\n            \nimport { ${components.join(', ')} } from 'react-admin';\n\n${filterRepresentation}\nexport const ${capitalize(singularize(resource))}List = () => (\n    <List${filterRepresentation ? ' filters={filters}' : ''}>\n${tableRepresentation}\n    </List>\n);`\n        );\n    }, [resource, isPending, error, schema, enableLog]);\n\n    if (isPending) return <Loading />;\n    if (error) return <p>Error: {error.message}</p>;\n\n    return (\n        <ListView filters={filters} {...rest}>\n            {child}\n        </ListView>\n    );\n};\n", "import { useMemo } from 'react';\nimport { useAPISchema } from 'ra-supabase-core';\nimport type { ResourceProps } from 'ra-core';\n\nimport { ListGuesser } from './ListGuesser';\nimport { CreateGuesser } from './CreateGuesser';\nimport { EditGuesser } from './EditGuesser';\nimport { ShowGuesser } from './ShowGuesser';\n\nexport const useCrudGuesser = () => {\n    const { data: schema, error, isPending } = useAPISchema();\n    return useMemo<ResourceProps[]>(() => {\n        if (isPending || error) {\n            return [];\n        }\n        const resourceNames = Object.keys(schema.definitions!);\n        return resourceNames.map(name => {\n            const resourcePaths = schema.paths[`/${name}`] ?? {};\n            return {\n                name,\n                list: resourcePaths.get ? ListGuesser : undefined,\n                show: resourcePaths.get ? ShowGuesser : undefined,\n                edit: resourcePaths.patch ? EditGuesser : undefined,\n                create: resourcePaths.post ? CreateGuesser : undefined,\n            };\n        });\n    }, [schema, isPending, error]);\n};\n", "import * as React from 'react';\nimport type { ReactNode } from 'react';\nimport { useAPISchema } from 'ra-supabase-core';\nimport { InferredElement, ShowBase, useResourceContext } from 'ra-core';\nimport {\n    Loading,\n    showFieldTypes,\n    type ShowProps,\n    ShowView,\n    type ShowViewProps,\n} from 'ra-ui-materialui';\nimport { capitalize, singularize } from 'inflection';\n\nimport { inferElementFromType } from './inferElementFromType';\n\nexport const ShowGuesser = (props: ShowProps & { enableLog?: boolean }) => {\n    const { id, disableAuthentication, queryOptions, resource, ...rest } =\n        props;\n    return (\n        <ShowBase\n            id={id}\n            disableAuthentication={disableAuthentication}\n            queryOptions={queryOptions}\n            resource={resource}\n        >\n            <ShowGuesserView {...rest} />\n        </ShowBase>\n    );\n};\n\nexport const ShowGuesserView = (\n    props: ShowViewProps & {\n        enableLog?: boolean;\n    }\n) => {\n    const { data: schema, error, isPending } = useAPISchema();\n    const resource = useResourceContext();\n    const [child, setChild] = React.useState<ReactNode>(null);\n    if (!resource) {\n        throw new Error('ShowGuesser must be used withing a ResourceContext');\n    }\n    const { enableLog = process.env.NODE_ENV === 'development', ...rest } =\n        props;\n\n    React.useEffect(() => {\n        if (isPending || error) {\n            return;\n        }\n        const resourceDefinition = schema.definitions?.[resource];\n        if (!resourceDefinition || !resourceDefinition.properties) {\n            throw new Error(\n                `The resource ${resource} is not defined in the API schema`\n            );\n        }\n        const inferredFields = Object.keys(resourceDefinition.properties)\n            .filter(\n                source =>\n                    resourceDefinition.properties![source].format !== 'tsvector'\n            )\n            .map((source: string) =>\n                inferElementFromType({\n                    name: source,\n                    types: showFieldTypes,\n                    description:\n                        resourceDefinition.properties![source].description,\n                    format: resourceDefinition.properties![source].format,\n                    type: (resourceDefinition.properties &&\n                    resourceDefinition.properties[source] &&\n                    typeof resourceDefinition.properties[source].type ===\n                        'string'\n                        ? resourceDefinition.properties![source].type\n                        : 'string') as string,\n                })\n            );\n        const inferredLayout = new InferredElement(\n            showFieldTypes.show,\n            null,\n            inferredFields\n        );\n        setChild(inferredLayout.getElement());\n        if (!enableLog) return;\n\n        const representation = inferredLayout.getRepresentation();\n\n        const components = ['Show']\n            .concat(\n                Array.from(\n                    new Set(\n                        Array.from(representation.matchAll(/<([^/\\s>]+)/g))\n                            .map(match => match[1])\n                            .filter(component => component !== 'span')\n                    )\n                )\n            )\n            .sort();\n\n        // eslint-disable-next-line no-console\n        console.log(\n            `Guessed Show:\n            \nimport { ${components.join(', ')} } from 'react-admin';\n            \nexport const ${capitalize(singularize(resource))}Show = () => (\n    <Show>\n${representation}\n    </Show>\n);`\n        );\n    }, [resource, isPending, error, schema, enableLog]);\n\n    if (isPending) return <Loading />;\n    if (error) return <p>Error: {error.message}</p>;\n\n    return <ShowView {...rest}>{child}</ShowView>;\n};\n", "import * as React from 'react';\nimport { CardActions, Stack, styled, Typography } from '@mui/material';\nimport { useResetPassword } from 'ra-supabase-core';\nimport { Form, required, useNotify, useTranslate } from 'ra-core';\nimport { Link, SaveButton, TextInput } from 'ra-ui-materialui';\n\n/**\n * A component that renders a form for resetting the user password.\n */\nexport const ForgotPasswordForm = () => {\n    const notify = useNotify();\n    const translate = useTranslate();\n    const [resetPassword] = useResetPassword({\n        onError: error => {\n            notify(\n                typeof error === 'string'\n                    ? error\n                    : typeof error === 'undefined' || !error.message\n                    ? 'ra.auth.sign_in_error'\n                    : error.message,\n                {\n                    type: 'warning',\n                    messageArgs: {\n                        _:\n                            typeof error === 'string'\n                                ? error\n                                : error && error.message\n                                ? error.message\n                                : undefined,\n                    },\n                }\n            );\n        },\n    });\n\n    const submit = (values: FormData) => {\n        return resetPassword({\n            email: values.email,\n        });\n    };\n\n    return (\n        <Root onSubmit={submit}>\n            <div className={SupabaseLoginFormClasses.container}>\n                <Stack spacing={1}>\n                    <Typography variant=\"h5\" textAlign=\"center\">\n                        {translate(\n                            'ra-supabase.reset_password.forgot_password',\n                            { _: 'Forgot password?' }\n                        )}\n                    </Typography>\n\n                    <Typography\n                        variant=\"body2\"\n                        color=\"textSecondary\"\n                        textAlign=\"center\"\n                    >\n                        {translate(\n                            'ra-supabase.reset_password.forgot_password_details',\n                            {\n                                _: 'Enter your email to receive a reset password link.',\n                            }\n                        )}\n                    </Typography>\n                </Stack>\n\n                <div className={SupabaseLoginFormClasses.input}>\n                    <TextInput\n                        source=\"email\"\n                        label={translate('ra.auth.email', {\n                            _: 'Email',\n                        })}\n                        autoComplete=\"email\"\n                        fullWidth\n                        validate={required()}\n                    />\n                </div>\n            </div>\n            <CardActions sx={{ flexDirection: 'column', gap: 1 }}>\n                <SaveButton\n                    variant=\"contained\"\n                    type=\"submit\"\n                    className={SupabaseLoginFormClasses.button}\n                    label={translate('ra.action.reset_password', {\n                        _: 'Reset password',\n                    })}\n                    icon={<></>}\n                />\n                <Link to=\"/login\" variant=\"body2\">\n                    {translate('ra-supabase.auth.back_to_login', {\n                        _: 'Back to login page',\n                    })}\n                </Link>\n            </CardActions>\n        </Root>\n    );\n};\n\ninterface FormData {\n    email: string;\n}\n\nconst PREFIX = 'RaSupabaseForgotPasswordForm';\n\nconst SupabaseLoginFormClasses = {\n    container: `${PREFIX}-container`,\n    input: `${PREFIX}-input`,\n    button: `${PREFIX}-button`,\n};\n\nconst Root = styled(Form, {\n    name: PREFIX,\n    overridesResolver: (props, styles) => styles.root,\n})(({ theme }) => ({\n    [`& .${SupabaseLoginFormClasses.container}`]: {\n        padding: '0 1em 0 1em',\n    },\n    [`& .${SupabaseLoginFormClasses.input}`]: {\n        marginTop: '1em',\n    },\n    [`& .${SupabaseLoginFormClasses.button}`]: {\n        width: '100%',\n    },\n}));\n", "import * as React from 'react';\nimport type { ReactNode } from 'react';\n\nimport { AuthLayout } from './AuthLayout';\nimport { ForgotPasswordForm } from './ForgotPasswordForm';\n\n/**\n * A component that renders a page for resetting the current user password through Supabase.\n * @param props\n * @param props.children The content of the page. If not set, it will render a ForgotPasswordForm.\n *\n * @example\n * import { ForgotPasswordPage } from 'ra-supabase-ui-materialui';\n * import { Admin, CustomRoutes } from 'react-admin';\n *\n * const App = () => (\n *    <Admin dataProvider={dataProvider}>\n *      <CustomRoutes>\n *       <Route path={ForgotPasswordPage.path} element={<ForgotPasswordPage />} />\n *     </CustomRoutes>\n *      ...\n *  </Admin>\n * );\n */\nexport const ForgotPasswordPage = (props: ForgotPasswordPageProps) => {\n    const { children = <ForgotPasswordForm /> } = props;\n\n    return <AuthLayout>{children}</AuthLayout>;\n};\n\nForgotPasswordPage.path = '/forgot-password';\n\nexport type ForgotPasswordPageProps = {\n    children?: ReactNode;\n};\n", "import * as React from 'react';\nimport type { ComponentProps } from 'react';\nimport { CardActions, styled } from '@mui/material';\nimport { Form, required, useLogin, useNotify, useTranslate } from 'ra-core';\nimport { Link, PasswordInput, SaveButton, TextInput } from 'ra-ui-materialui';\n\nimport { ForgotPasswordPage } from './ForgotPasswordPage';\n\n/**\n * A component that renders a form to login to the application with an email and password.\n */\nexport const LoginForm = ({\n    disableForgotPassword,\n    ...props\n}: LoginFormProps) => {\n    const login = useLogin();\n    const notify = useNotify();\n    const translate = useTranslate();\n\n    const submit = (values: FormData) => {\n        return login(values).catch(error => {\n            notify(\n                typeof error === 'string'\n                    ? error\n                    : typeof error === 'undefined' || !error.message\n                    ? 'ra.auth.sign_in_error'\n                    : error.message,\n                {\n                    type: 'warning',\n                    messageArgs: {\n                        _:\n                            typeof error === 'string'\n                                ? error\n                                : error && error.message\n                                ? error.message\n                                : undefined,\n                    },\n                }\n            );\n        });\n    };\n\n    return (\n        <Root onSubmit={submit} {...props}>\n            <div className={SupabaseLoginFormClasses.container}>\n                <div className={SupabaseLoginFormClasses.input}>\n                    <TextInput\n                        autoFocus\n                        source=\"email\"\n                        type=\"email\"\n                        label={translate('ra-supabase.auth.email', {\n                            _: 'Email',\n                        })}\n                        fullWidth\n                        validate={required()}\n                    />\n                </div>\n                <div>\n                    <PasswordInput\n                        source=\"password\"\n                        label={translate('ra.auth.password', {\n                            _: 'Password',\n                        })}\n                        autoComplete=\"current-password\"\n                        fullWidth\n                        validate={required()}\n                    />\n                </div>\n            </div>\n            <CardActions sx={{ flexDirection: 'column', gap: 1 }}>\n                <SaveButton\n                    variant=\"contained\"\n                    type=\"submit\"\n                    className={SupabaseLoginFormClasses.button}\n                    label={translate('ra.auth.sign_in')}\n                    icon={<></>}\n                />\n                {!disableForgotPassword ? (\n                    <Link to={ForgotPasswordPage.path} variant=\"body2\">\n                        {translate('ra-supabase.auth.forgot_password', {\n                            _: 'Forgot password?',\n                        })}\n                    </Link>\n                ) : null}\n            </CardActions>\n        </Root>\n    );\n};\n\nexport interface LoginFormProps\n    extends Omit<ComponentProps<typeof Root>, 'onSubmit' | 'children'> {\n    disableForgotPassword?: boolean;\n}\n\ninterface FormData {\n    email?: string;\n    password?: string;\n}\n\nconst PREFIX = 'RaSupabaseLoginForm';\n\nconst SupabaseLoginFormClasses = {\n    container: `${PREFIX}-container`,\n    input: `${PREFIX}-input`,\n    button: `${PREFIX}-button`,\n};\n\nconst Root = styled(Form, {\n    name: PREFIX,\n    overridesResolver: (props, styles) => styles.root,\n})(({ theme }) => ({\n    [`& .${SupabaseLoginFormClasses.container}`]: {\n        padding: '0 1em 1em 1em',\n    },\n    [`& .${SupabaseLoginFormClasses.input}`]: {\n        marginTop: '1em',\n    },\n    [`& .${SupabaseLoginFormClasses.button}`]: {\n        width: '100%',\n    },\n}));\n", "// Adapted from https://github.com/supabase/auth-ui/blob/main/packages/react/src/components/Auth/Icons.tsx\n// By Supabase\nimport React from 'react';\nimport { SvgIcon, SvgIconProps } from '@mui/material';\n\nexport const GoogleIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        <path\n            fill=\"#FFC107\"\n            d=\"M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z\"\n        />\n        <path\n            fill=\"#FF3D00\"\n            d=\"M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z\"\n        />\n        <path\n            fill=\"#4CAF50\"\n            d=\"M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z\"\n        />\n        <path\n            fill=\"#1976D2\"\n            d=\"M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z\"\n        />\n    </SvgIcon>\n);\n\nexport const FacebookIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        <path fill=\"#039be5\" d=\"M24 5A19 19 0 1 0 24 43A19 19 0 1 0 24 5Z\" />\n        <path\n            fill=\"#fff\"\n            d=\"M26.572,29.036h4.917l0.772-4.995h-5.69v-2.73c0-2.075,0.678-3.915,2.619-3.915h3.119v-4.359c-0.548-0.074-1.707-0.236-3.897-0.236c-4.573,0-7.254,2.415-7.254,7.917v3.323h-4.701v4.995h4.701v13.729C22.089,42.905,23.032,43,24,43c0.875,0,1.729-0.08,2.572-0.194V29.036z\"\n        />\n    </SvgIcon>\n);\n\nexport const TwitterIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        <path\n            fill=\"#03A9F4\"\n            d=\"M42,12.429c-1.323,0.586-2.746,0.977-4.247,1.162c1.526-0.906,2.7-2.351,3.251-4.058c-1.428,0.837-3.01,1.452-4.693,1.776C34.967,9.884,33.05,9,30.926,9c-4.08,0-7.387,3.278-7.387,7.32c0,0.572,0.067,1.129,0.193,1.67c-6.138-0.308-11.582-3.226-15.224-7.654c-0.64,1.082-1,2.349-1,3.686c0,2.541,1.301,4.778,3.285,6.096c-1.211-0.037-2.351-0.374-3.349-0.914c0,0.022,0,0.055,0,0.086c0,3.551,2.547,6.508,5.923,7.181c-0.617,0.169-1.269,0.263-1.941,0.263c-0.477,0-0.942-0.054-1.392-0.135c0.94,2.902,3.667,5.023,6.898,5.086c-2.528,1.96-5.712,3.134-9.174,3.134c-0.598,0-1.183-0.034-1.761-0.104C9.268,36.786,13.152,38,17.321,38c13.585,0,21.017-11.156,21.017-20.834c0-0.317-0.01-0.633-0.025-0.945C39.763,15.197,41.013,13.905,42,12.429\"\n        />\n    </SvgIcon>\n);\n\nexport const AppleIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        fill=\"gray\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 24 24\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        {' '}\n        <path d=\"M 15.904297 1.078125 C 15.843359 1.06875 15.774219 1.0746094 15.699219 1.0996094 C 14.699219 1.2996094 13.600391 1.8996094 12.900391 2.5996094 C 12.300391 3.1996094 11.800781 4.1996094 11.800781 5.0996094 C 11.800781 5.2996094 11.999219 5.5 12.199219 5.5 C 13.299219 5.4 14.399609 4.7996094 15.099609 4.0996094 C 15.699609 3.2996094 16.199219 2.4 16.199219 1.5 C 16.199219 1.275 16.087109 1.10625 15.904297 1.078125 z M 16.199219 5.4003906 C 14.399219 5.4003906 13.600391 6.5 12.400391 6.5 C 11.100391 6.5 9.9003906 5.5 8.4003906 5.5 C 6.3003906 5.5 3.0996094 7.4996094 3.0996094 12.099609 C 2.9996094 16.299609 6.8 21 9 21 C 10.3 21 10.600391 20.199219 12.400391 20.199219 C 14.200391 20.199219 14.600391 21 15.900391 21 C 17.400391 21 18.500391 19.399609 19.400391 18.099609 C 19.800391 17.399609 20.100391 17.000391 20.400391 16.400391 C 20.600391 16.000391 20.4 15.600391 20 15.400391 C 17.4 14.100391 16.900781 9.9003906 19.800781 8.4003906 C 20.300781 8.1003906 20.4 7.4992188 20 7.1992188 C 18.9 6.1992187 17.299219 5.4003906 16.199219 5.4003906 z\" />\n    </SvgIcon>\n);\n\nexport const GithubIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        fill=\"gray\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 30 30\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        {' '}\n        <path d=\"M15,3C8.373,3,3,8.373,3,15c0,5.623,3.872,10.328,9.092,11.63C12.036,26.468,12,26.28,12,26.047v-2.051 c-0.487,0-1.303,0-1.508,0c-0.821,0-1.551-0.353-1.905-1.009c-0.393-0.729-0.461-1.844-1.435-2.526 c-0.289-0.227-0.069-0.486,0.264-0.451c0.615,0.174,1.125,0.596,1.605,1.222c0.478,0.627,0.703,0.769,1.596,0.769 c0.433,0,1.081-0.025,1.691-0.121c0.328-0.833,0.895-1.6,1.588-1.962c-3.996-0.411-5.903-2.399-5.903-5.098 c0-1.162,0.495-2.286,1.336-3.233C9.053,10.647,8.706,8.73,9.435,8c1.798,0,2.885,1.166,3.146,1.481C13.477,9.174,14.461,9,15.495,9 c1.036,0,2.024,0.174,2.922,0.483C18.675,9.17,19.763,8,21.565,8c0.732,0.731,0.381,2.656,0.102,3.594 c0.836,0.945,1.328,2.066,1.328,3.226c0,2.697-1.904,4.684-5.894,5.097C18.199,20.49,19,22.1,19,23.313v2.734 c0,0.104-0.023,0.179-0.035,0.268C23.641,24.676,27,20.236,27,15C27,8.373,21.627,3,15,3z\" />\n    </SvgIcon>\n);\n\nexport const GitlabIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        <path fill=\"#e53935\" d=\"M24 43L16 20 32 20z\" />\n        <path fill=\"#ff7043\" d=\"M24 43L42 20 32 20z\" />\n        <path fill=\"#e53935\" d=\"M37 5L42 20 32 20z\" />\n        <path fill=\"#ffa726\" d=\"M24 43L42 20 45 28z\" />\n        <path fill=\"#ff7043\" d=\"M24 43L6 20 16 20z\" />\n        <path fill=\"#e53935\" d=\"M11 5L6 20 16 20z\" />\n        <path fill=\"#ffa726\" d=\"M24 43L6 20 3 28z\" />\n    </SvgIcon>\n);\nexport const BitbucketIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"512\"\n        height=\"512\"\n        viewBox=\"0 0 62.42 62.42\"\n    >\n        <defs>\n            <linearGradient\n                id=\"New_Gradient_Swatch_1\"\n                x1=\"64.01\"\n                y1=\"30.27\"\n                x2=\"32.99\"\n                y2=\"54.48\"\n                gradientUnits=\"userSpaceOnUse\"\n            >\n                <stop offset=\"0.18\" stopColor=\"#0052cc\" />\n                <stop offset=\"1\" stopColor=\"#2684ff\" />\n            </linearGradient>\n        </defs>\n        <title>Bitbucket-blue</title>\n        <g id=\"Layer_2\" data-name=\"Layer 2\">\n            <g id=\"Blue\" transform=\"translate(0 -3.13)\">\n                <path\n                    d=\"M2,6.26A2,2,0,0,0,0,8.58L8.49,60.12a2.72,2.72,0,0,0,2.66,2.27H51.88a2,2,0,0,0,2-1.68L62.37,8.59a2,2,0,0,0-2-2.32ZM37.75,43.51h-13L21.23,25.12H40.9Z\"\n                    fill=\"#2684ff\"\n                />\n                <path\n                    d=\"M59.67,25.12H40.9L37.75,43.51h-13L9.4,61.73a2.71,2.71,0,0,0,1.75.66H51.89a2,2,0,0,0,2-1.68Z\"\n                    fill=\"url(#New_Gradient_Swatch_1)\"\n                />\n            </g>\n        </g>\n    </SvgIcon>\n);\n\nexport const DiscordIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        <path\n            fill=\"#536dfe\"\n            d=\"M39.248,10.177c-2.804-1.287-5.812-2.235-8.956-2.778c-0.057-0.01-0.114,0.016-0.144,0.068\tc-0.387,0.688-0.815,1.585-1.115,2.291c-3.382-0.506-6.747-0.506-10.059,0c-0.3-0.721-0.744-1.603-1.133-2.291\tc-0.03-0.051-0.087-0.077-0.144-0.068c-3.143,0.541-6.15,1.489-8.956,2.778c-0.024,0.01-0.045,0.028-0.059,0.051\tc-5.704,8.522-7.267,16.835-6.5,25.044c0.003,0.04,0.026,0.079,0.057,0.103c3.763,2.764,7.409,4.442,10.987,5.554\tc0.057,0.017,0.118-0.003,0.154-0.051c0.846-1.156,1.601-2.374,2.248-3.656c0.038-0.075,0.002-0.164-0.076-0.194\tc-1.197-0.454-2.336-1.007-3.432-1.636c-0.087-0.051-0.094-0.175-0.014-0.234c0.231-0.173,0.461-0.353,0.682-0.534\tc0.04-0.033,0.095-0.04,0.142-0.019c7.201,3.288,14.997,3.288,22.113,0c0.047-0.023,0.102-0.016,0.144,0.017\tc0.22,0.182,0.451,0.363,0.683,0.536c0.08,0.059,0.075,0.183-0.012,0.234c-1.096,0.641-2.236,1.182-3.434,1.634\tc-0.078,0.03-0.113,0.12-0.075,0.196c0.661,1.28,1.415,2.498,2.246,3.654c0.035,0.049,0.097,0.07,0.154,0.052\tc3.595-1.112,7.241-2.79,11.004-5.554c0.033-0.024,0.054-0.061,0.057-0.101c0.917-9.491-1.537-17.735-6.505-25.044\tC39.293,10.205,39.272,10.187,39.248,10.177z M16.703,30.273c-2.168,0-3.954-1.99-3.954-4.435s1.752-4.435,3.954-4.435\tc2.22,0,3.989,2.008,3.954,4.435C20.658,28.282,18.906,30.273,16.703,30.273z M31.324,30.273c-2.168,0-3.954-1.99-3.954-4.435\ts1.752-4.435,3.954-4.435c2.22,0,3.989,2.008,3.954,4.435C35.278,28.282,33.544,30.273,31.324,30.273z\"\n        />\n    </SvgIcon>\n);\n\nexport const AzureIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        <linearGradient\n            id=\"k8yl7~hDat~FaoWq8WjN6a\"\n            x1=\"-1254.397\"\n            x2=\"-1261.911\"\n            y1=\"877.268\"\n            y2=\"899.466\"\n            gradientTransform=\"translate(1981.75 -1362.063) scale(1.5625)\"\n            gradientUnits=\"userSpaceOnUse\"\n        >\n            <stop offset=\"0\" stopColor=\"#114a8b\" />\n            <stop offset=\"1\" stopColor=\"#0669bc\" />\n        </linearGradient>\n        <path\n            fill=\"url(#k8yl7~hDat~FaoWq8WjN6a)\"\n            d=\"M17.634,6h11.305L17.203,40.773c-0.247,0.733-0.934,1.226-1.708,1.226H6.697 c-0.994,0-1.8-0.806-1.8-1.8c0-0.196,0.032-0.39,0.094-0.576L15.926,7.227C16.173,6.494,16.86,6,17.634,6L17.634,6z\"\n        />\n        <path\n            fill=\"#0078d4\"\n            d=\"M34.062,29.324H16.135c-0.458-0.001-0.83,0.371-0.831,0.829c0,0.231,0.095,0.451,0.264,0.608 l11.52,10.752C27.423,41.826,27.865,42,28.324,42h10.151L34.062,29.324z\"\n        />\n        <linearGradient\n            id=\"k8yl7~hDat~FaoWq8WjN6b\"\n            x1=\"-1252.05\"\n            x2=\"-1253.788\"\n            y1=\"887.612\"\n            y2=\"888.2\"\n            gradientTransform=\"translate(1981.75 -1362.063) scale(1.5625)\"\n            gradientUnits=\"userSpaceOnUse\"\n        >\n            <stop offset=\"0\" stopOpacity=\".3\" />\n            <stop offset=\".071\" stopOpacity=\".2\" />\n            <stop offset=\".321\" stopOpacity=\".1\" />\n            <stop offset=\".623\" stopOpacity=\".05\" />\n            <stop offset=\"1\" stopOpacity=\"0\" />\n        </linearGradient>\n        <path\n            fill=\"url(#k8yl7~hDat~FaoWq8WjN6b)\"\n            d=\"M17.634,6c-0.783-0.003-1.476,0.504-1.712,1.25L5.005,39.595 c-0.335,0.934,0.151,1.964,1.085,2.299C6.286,41.964,6.493,42,6.702,42h9.026c0.684-0.122,1.25-0.603,1.481-1.259l2.177-6.416 l7.776,7.253c0.326,0.27,0.735,0.419,1.158,0.422h10.114l-4.436-12.676l-12.931,0.003L28.98,6H17.634z\"\n        />\n        <linearGradient\n            id=\"k8yl7~hDat~FaoWq8WjN6c\"\n            x1=\"-1252.952\"\n            x2=\"-1244.704\"\n            y1=\"876.6\"\n            y2=\"898.575\"\n            gradientTransform=\"translate(1981.75 -1362.063) scale(1.5625)\"\n            gradientUnits=\"userSpaceOnUse\"\n        >\n            <stop offset=\"0\" stopColor=\"#3ccbf4\" />\n            <stop offset=\"1\" stopColor=\"#2892df\" />\n        </linearGradient>\n        <path\n            fill=\"url(#k8yl7~hDat~FaoWq8WjN6c)\"\n            d=\"M32.074,7.225C31.827,6.493,31.141,6,30.368,6h-12.6c0.772,0,1.459,0.493,1.705,1.224 l10.935,32.399c0.318,0.942-0.188,1.963-1.13,2.281C29.093,41.968,28.899,42,28.703,42h12.6c0.994,0,1.8-0.806,1.8-1.801 c0-0.196-0.032-0.39-0.095-0.575L32.074,7.225z\"\n        />\n    </SvgIcon>\n);\n\nexport const KeycloakIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        width=\"512\"\n        height=\"512\"\n        viewBox=\"0 0 512 512\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n    >\n        <path\n            d=\"M472.136 163.959H408.584C407.401 163.959 406.218 163.327 405.666 162.3L354.651 73.6591C354.02 72.632 352.916 72 351.654 72H143.492C142.309 72 141.126 72.632 140.574 73.6591L87.5084 165.618L36.414 254.259C35.862 255.286 35.862 256.55 36.414 257.656L87.5084 346.297L140.495 438.335C141.047 439.362 142.23 440.073 143.413 439.994H351.654C352.837 439.994 354.02 439.362 354.651 438.335L405.745 349.694C406.297 348.667 407.48 347.956 408.663 348.035H472.215C474.344 348.035 476 346.297 476 344.243V167.83C475.921 165.697 474.186 163.959 472.136 163.959ZM228.728 349.694L212.721 377.345C212.485 377.74 212.091 378.135 211.696 378.372C211.223 378.609 210.75 378.767 210.198 378.767H178.422C177.318 378.767 176.293 378.214 175.82 377.187L128.431 294.787L123.779 286.65L106.748 257.498C106.511 257.103 106.353 256.629 106.432 256.076C106.432 255.602 106.59 255.049 106.827 254.654L123.937 224.949L175.899 134.886C176.451 133.938 177.476 133.306 178.501 133.306H210.198C210.75 133.306 211.302 133.464 211.854 133.701C212.248 133.938 212.643 134.254 212.879 134.728L228.886 162.537C229.359 163.485 229.28 164.67 228.728 165.539L177.397 254.654C177.16 255.049 177.081 255.523 177.081 255.918C177.081 256.392 177.239 256.787 177.397 257.182L228.728 346.218C229.438 347.403 229.359 348.667 228.728 349.694V349.694ZM388.083 257.498L371.051 286.65L366.399 294.787L319.011 377.187C318.459 378.135 317.512 378.767 316.409 378.767H284.632C284.08 378.767 283.607 378.609 283.134 378.372C282.74 378.135 282.346 377.819 282.109 377.345L266.103 349.694C265.393 348.667 265.393 347.403 266.024 346.376L317.355 257.34C317.591 256.945 317.67 256.471 317.67 256.076C317.67 255.602 317.513 255.207 317.355 254.812L266.024 165.697C265.472 164.749 265.393 163.643 265.866 162.695L281.873 134.886C282.109 134.491 282.503 134.096 282.898 133.859C283.371 133.543 283.923 133.464 284.553 133.464H316.409C317.512 133.464 318.538 134.017 319.011 135.044L370.972 225.107L388.083 254.812C388.319 255.286 388.477 255.76 388.477 256.234C388.477 256.55 388.319 257.024 388.083 257.498V257.498Z\"\n            fill=\"#008AAA\"\n        />\n    </SvgIcon>\n);\n\nexport const LinkedinIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        <path\n            fill=\"#0288D1\"\n            d=\"M42,37c0,2.762-2.238,5-5,5H11c-2.761,0-5-2.238-5-5V11c0-2.762,2.239-5,5-5h26c2.762,0,5,2.238,5,5V37z\"\n        />\n        <path\n            fill=\"#FFF\"\n            d=\"M12 19H17V36H12zM14.485 17h-.028C12.965 17 12 15.888 12 14.499 12 13.08 12.995 12 14.514 12c1.521 0 2.458 1.08 2.486 2.499C17 15.887 16.035 17 14.485 17zM36 36h-5v-9.099c0-2.198-1.225-3.698-3.192-3.698-1.501 0-2.313 1.012-2.707 1.99C24.957 25.543 25 26.511 25 27v9h-5V19h5v2.616C25.721 20.5 26.85 19 29.738 19c3.578 0 6.261 2.25 6.261 7.274L36 36 36 36z\"\n        />\n    </SvgIcon>\n);\n\nexport const NotionIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n    >\n        <path\n            fill=\"#fff\"\n            fillRule=\"evenodd\"\n            d=\"M11.553,11.099c1.232,1.001,1.694,0.925,4.008,0.77 l21.812-1.31c0.463,0,0.078-0.461-0.076-0.538l-3.622-2.619c-0.694-0.539-1.619-1.156-3.391-1.002l-21.12,1.54 c-0.77,0.076-0.924,0.461-0.617,0.77L11.553,11.099z\"\n            clipRule=\"evenodd\"\n        />\n        <path\n            fill=\"#fff\"\n            fillRule=\"evenodd\"\n            d=\"M12.862,16.182v22.95c0,1.233,0.616,1.695,2.004,1.619 l23.971-1.387c1.388-0.076,1.543-0.925,1.543-1.927V14.641c0-1-0.385-1.54-1.234-1.463l-25.05,1.463 C13.171,14.718,12.862,15.181,12.862,16.182L12.862,16.182z\"\n            clipRule=\"evenodd\"\n        />\n        <path\n            fill=\"#424242\"\n            fillRule=\"evenodd\"\n            d=\"M11.553,11.099c1.232,1.001,1.694,0.925,4.008,0.77 l21.812-1.31c0.463,0,0.078-0.461-0.076-0.538l-3.622-2.619c-0.694-0.539-1.619-1.156-3.391-1.002l-21.12,1.54 c-0.77,0.076-0.924,0.461-0.617,0.77L11.553,11.099z M12.862,16.182v22.95c0,1.233,0.616,1.695,2.004,1.619l23.971-1.387 c1.388-0.076,1.543-0.925,1.543-1.927V14.641c0-1-0.385-1.54-1.234-1.463l-25.05,1.463C13.171,14.718,12.862,15.181,12.862,16.182 L12.862,16.182z M36.526,17.413c0.154,0.694,0,1.387-0.695,1.465l-1.155,0.23v16.943c-1.003,0.539-1.928,0.847-2.698,0.847 c-1.234,0-1.543-0.385-2.467-1.54l-7.555-11.86v11.475l2.391,0.539c0,0,0,1.386-1.929,1.386l-5.317,0.308 c-0.154-0.308,0-1.078,0.539-1.232l1.388-0.385V20.418l-1.927-0.154c-0.155-0.694,0.23-1.694,1.31-1.772l5.704-0.385l7.862,12.015 V19.493l-2.005-0.23c-0.154-0.848,0.462-1.464,1.233-1.54L36.526,17.413z M7.389,5.862l21.968-1.618 c2.698-0.231,3.392-0.076,5.087,1.155l7.013,4.929C42.614,11.176,43,11.407,43,12.33v27.032c0,1.694-0.617,2.696-2.775,2.849 l-25.512,1.541c-1.62,0.077-2.391-0.154-3.239-1.232l-5.164-6.7C5.385,34.587,5,33.664,5,32.585V8.556 C5,7.171,5.617,6.015,7.389,5.862z\"\n            clipRule=\"evenodd\"\n        />\n    </SvgIcon>\n);\n\nexport const SlackIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 48 48\"\n        width=\"512px\"\n        height=\"512px\"\n    >\n        <path\n            fill=\"#33d375\"\n            d=\"M33,8c0-2.209-1.791-4-4-4s-4,1.791-4,4c0,1.254,0,9.741,0,11c0,2.209,1.791,4,4,4s4-1.791,4-4\tC33,17.741,33,9.254,33,8z\"\n        />\n        <path\n            fill=\"#33d375\"\n            d=\"M43,19c0,2.209-1.791,4-4,4c-1.195,0-4,0-4,0s0-2.986,0-4c0-2.209,1.791-4,4-4S43,16.791,43,19z\"\n        />\n        <path\n            fill=\"#40c4ff\"\n            d=\"M8,14c-2.209,0-4,1.791-4,4s1.791,4,4,4c1.254,0,9.741,0,11,0c2.209,0,4-1.791,4-4s-1.791-4-4-4\tC17.741,14,9.254,14,8,14z\"\n        />\n        <path\n            fill=\"#40c4ff\"\n            d=\"M19,4c2.209,0,4,1.791,4,4c0,1.195,0,4,0,4s-2.986,0-4,0c-2.209,0-4-1.791-4-4S16.791,4,19,4z\"\n        />\n        <path\n            fill=\"#e91e63\"\n            d=\"M14,39.006C14,41.212,15.791,43,18,43s4-1.788,4-3.994c0-1.252,0-9.727,0-10.984\tc0-2.206-1.791-3.994-4-3.994s-4,1.788-4,3.994C14,29.279,14,37.754,14,39.006z\"\n        />\n        <path\n            fill=\"#e91e63\"\n            d=\"M4,28.022c0-2.206,1.791-3.994,4-3.994c1.195,0,4,0,4,0s0,2.981,0,3.994c0,2.206-1.791,3.994-4,3.994\tS4,30.228,4,28.022z\"\n        />\n        <path\n            fill=\"#ffc107\"\n            d=\"M39,33c2.209,0,4-1.791,4-4s-1.791-4-4-4c-1.254,0-9.741,0-11,0c-2.209,0-4,1.791-4,4s1.791,4,4,4\tC29.258,33,37.746,33,39,33z\"\n        />\n        <path\n            fill=\"#ffc107\"\n            d=\"M28,43c-2.209,0-4-1.791-4-4c0-1.195,0-4,0-4s2.986,0,4,0c2.209,0,4,1.791,4,4S30.209,43,28,43z\"\n        />\n    </SvgIcon>\n);\n\nexport const SpotifyIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        width=\"512\"\n        height=\"512\"\n        viewBox=\"0 0 512 512\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n    >\n        <path\n            d=\"M255.498 31.0034C131.513 31.0034 31 131.515 31 255.502C31 379.492 131.513 480 255.498 480C379.497 480 480 379.495 480 255.502C480 131.522 379.497 31.0135 255.495 31.0135L255.498 31V31.0034ZM358.453 354.798C354.432 361.391 345.801 363.486 339.204 359.435C286.496 327.237 220.139 319.947 141.993 337.801C134.463 339.516 126.957 334.798 125.24 327.264C123.516 319.731 128.217 312.225 135.767 310.511C221.284 290.972 294.639 299.384 353.816 335.549C360.413 339.596 362.504 348.2 358.453 354.798ZM385.932 293.67C380.864 301.903 370.088 304.503 361.858 299.438C301.512 262.345 209.528 251.602 138.151 273.272C128.893 276.067 119.118 270.851 116.309 261.61C113.521 252.353 118.74 242.597 127.981 239.782C209.512 215.044 310.87 227.026 380.17 269.612C388.4 274.68 391 285.456 385.935 293.676V293.673L385.932 293.67ZM388.293 230.016C315.935 187.039 196.56 183.089 127.479 204.055C116.387 207.42 104.654 201.159 101.293 190.063C97.9326 178.964 104.189 167.241 115.289 163.87C194.59 139.796 326.418 144.446 409.723 193.902C419.722 199.826 422.995 212.71 417.068 222.675C411.168 232.653 398.247 235.943 388.303 230.016H388.293V230.016Z\"\n            fill=\"#1ED760\"\n        />\n    </SvgIcon>\n);\n\nexport const TwitchIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        width=\"512\"\n        height=\"512\"\n        viewBox=\"0 0 512 512\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n    >\n        <path\n            d=\"M416 240L352 304H288L232 360V304H160V64H416V240Z\"\n            fill=\"white\"\n        />\n        <path\n            d=\"M144 32L64 112V400H160V480L240 400H304L448 256V32H144ZM416 240L352 304H288L232 360V304H160V64H416V240Z\"\n            fill=\"#9146FF\"\n        />\n        <path d=\"M368 120H336V216H368V120Z\" fill=\"#9146FF\" />\n        <path d=\"M280 120H248V216H280V120Z\" fill=\"#9146FF\" />\n    </SvgIcon>\n);\n\nexport const WorkosIcon = (props: SvgIconProps) => (\n    <SvgIcon\n        {...props}\n        width=\"512\"\n        height=\"512\"\n        viewBox=\"0 0 512 512\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n    >\n        <path\n            d=\"M33 256.043C33 264.556 35.3159 273.069 39.4845 280.202L117.993 415.493C126.098 429.298 138.373 440.572 153.657 445.634C183.764 455.528 214.797 442.873 229.618 417.333L248.609 384.661L173.806 256.043L252.777 119.831L271.768 87.1591C277.557 77.2654 284.968 69.4424 294 63H285.894H172.185C150.878 63 131.193 74.2742 120.54 92.6812L39.7161 231.884C35.3159 239.016 33 247.53 33 256.043Z\"\n            fill=\"#6363F1\"\n        />\n        <path\n            d=\"M480 256.058C480 247.539 477.684 239.021 473.516 231.883L393.849 94.6596C379.028 69.3331 347.995 56.4396 317.888 66.34C302.603 71.4053 290.329 82.6871 282.224 96.5015L264.391 127.354L339.194 256.058L260.223 392.131L241.232 424.825C235.443 434.495 228.032 442.553 219 449H227.106H340.815C362.122 449 381.807 437.718 392.46 419.299L473.284 280.003C477.684 272.866 480 264.577 480 256.058Z\"\n            fill=\"#6363F1\"\n        />\n    </SvgIcon>\n);\n", "import * as React from 'react';\nimport { Provider } from '@supabase/supabase-js';\nimport { Button, ButtonProps } from '@mui/material';\nimport { useLogin, useNotify, useTranslate } from 'ra-core';\n\nimport {\n    AppleIcon,\n    AzureIcon,\n    BitbucketIcon,\n    DiscordIcon,\n    FacebookIcon,\n    GithubIcon,\n    GitlabIcon,\n    GoogleIcon,\n    KeycloakIcon,\n    LinkedinIcon,\n    NotionIcon,\n    SlackIcon,\n    SpotifyIcon,\n    TwitchIcon,\n    TwitterIcon,\n    WorkosIcon,\n} from './icons';\n\nexport const SocialAuthButton = ({\n    provider,\n    redirect: redirectTo,\n    ...props\n}: SocialAuthButtonProps) => {\n    const login = useLogin();\n    const notify = useNotify();\n\n    const handleClick = () => {\n        login({ provider }, redirectTo ?? window.location.toString()).catch(\n            error => {\n                // The authProvide always reject for OAuth login but there will be no error\n                // if the call actually succeeds. This is to avoid react-admin redirecting\n                // immediately to the provided redirect prop before users are redirected to\n                // the OAuth provider.\n                if (error) {\n                    notify((error as Error).message, { type: 'error' });\n                }\n            }\n        );\n    };\n\n    return (\n        <Button\n            onClick={handleClick}\n            variant=\"contained\"\n            size=\"medium\"\n            color=\"inherit\"\n            {...props}\n        />\n    );\n};\n\nexport type SocialAuthButtonProps = {\n    provider: Provider;\n    redirect?: string;\n} & ButtonProps;\n\nexport const AppleButton = (props: Omit<SocialAuthButtonProps, 'provider'>) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Apple',\n    });\n\n    return (\n        <SocialAuthButton startIcon={<AppleIcon />} provider=\"apple\" {...props}>\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const AzureButton = (props: Omit<SocialAuthButtonProps, 'provider'>) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Azure',\n    });\n\n    return (\n        <SocialAuthButton startIcon={<AzureIcon />} provider=\"azure\" {...props}>\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const BitbucketButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Bitbucket',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<BitbucketIcon />}\n            provider=\"bitbucket\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const DiscordButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Discord',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<DiscordIcon />}\n            provider=\"discord\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const FacebookButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Facebook',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<FacebookIcon />}\n            provider=\"facebook\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const GitlabButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Gitlab',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<GitlabIcon />}\n            provider=\"gitlab\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const GithubButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Github',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<GithubIcon />}\n            provider=\"github\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const GoogleButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Google',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<GoogleIcon />}\n            provider=\"google\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const KeycloakButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Keycloak',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<KeycloakIcon />}\n            provider=\"keycloak\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const LinkedInButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'LinkedIn',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<LinkedinIcon />}\n            provider=\"linkedin\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const NotionButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Notion',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<NotionIcon />}\n            provider=\"notion\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const SlackButton = (props: Omit<SocialAuthButtonProps, 'provider'>) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Slack',\n    });\n\n    return (\n        <SocialAuthButton startIcon={<SlackIcon />} provider=\"slack\" {...props}>\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const SpotifyButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Spotify',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<SpotifyIcon />}\n            provider=\"spotify\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const TwitchButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Twitch',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<TwitchIcon />}\n            provider=\"twitch\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const TwitterButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'Twitter',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<TwitterIcon />}\n            provider=\"twitter\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n\nexport const WorkosButton = (\n    props: Omit<SocialAuthButtonProps, 'provider'>\n) => {\n    const translate = useTranslate();\n    const label = translate('ra-supabase.auth.sign_in_with', {\n        provider: 'WorkOS',\n    });\n\n    return (\n        <SocialAuthButton\n            startIcon={<WorkosIcon />}\n            provider=\"workos\"\n            {...props}\n        >\n            {label}\n        </SocialAuthButton>\n    );\n};\n", "import * as React from 'react';\nimport type { ReactNode } from 'react';\nimport { Provider } from '@supabase/supabase-js';\nimport { Divider, Stack } from '@mui/material';\n\nimport {\n    AppleButton,\n    AzureButton,\n    BitbucketButton,\n    DiscordButton,\n    FacebookButton,\n    GithubButton,\n    GitlabButton,\n    GoogleButton,\n    KeycloakButton,\n    LinkedInButton,\n    NotionButton,\n    SlackButton,\n    SpotifyButton,\n    TwitchButton,\n    TwitterButton,\n    WorkosButton,\n} from './SocialAuthButton';\nimport { AuthLayout } from './AuthLayout';\nimport { LoginForm } from './LoginForm';\n\n/**\n * A component that renders a login page to login to the application through Supabase. It renders a LoginForm by default. It support social login providers.\n * @param props\n * @param props.children The content of the login page. If not set, it will render a LoginForm.\n * @param props.disableEmailPassword If true, the email/password login form will not be rendered.\n * @param props.providers The list of social login providers to render. Defaults to no providers.\n * @example\n * import { LoginPage } from 'ra-supabase-ui-materialui';\n * import { Admin } from 'react-admin';\n *\n * const App = () => (\n *    <Admin dataProvider={dataProvider} loginPage={LoginPage}>\n *       ...\n *   </Admin>\n * );\n *\n * @example With social login providers\n * import { LoginPage } from 'ra-supabase-ui-materialui';\n * import { Admin } from 'react-admin';\n *\n * const App = () => (\n *    <Admin dataProvider={dataProvider} loginPage={<LoginPage providers={['github', 'twitter']} />}>\n *       ...\n *   </Admin>\n * );\n *\n * @example With social login providers\n * import { LoginPage } from 'ra-supabase-ui-materialui';\n * import { Admin } from 'react-admin';\n *\n * const App = () => (\n *    <Admin dataProvider={dataProvider} loginPage={<LoginPage providers={['github', 'twitter']} />}>\n *       ...\n *   </Admin>\n * );\n *\n * @example With social login providers and no email/password login form\n * import { LoginPage } from 'ra-supabase-ui-materialui';\n * import { Admin } from 'react-admin';\n *\n * const App = () => (\n *    <Admin dataProvider={dataProvider} loginPage={<LoginPage disableEmailPassword providers={['github', 'twitter']} />}>\n *       ...\n *   </Admin>\n * );\n */\nexport const LoginPage = (props: LoginPageProps) => {\n    const {\n        children,\n        disableEmailPassword = false,\n        disableForgotPassword = false,\n        providers = [],\n    } = props;\n\n    return (\n        <AuthLayout>\n            {children ?? (\n                <>\n                    {disableEmailPassword ? null : (\n                        <LoginForm\n                            disableForgotPassword={disableForgotPassword}\n                        />\n                    )}\n                    {disableEmailPassword || providers.length === 0 ? null : (\n                        <Divider />\n                    )}\n                    {providers && providers.length > 0 ? (\n                        <>\n                            <Stack gap={1} padding={1}>\n                                {providers.includes('apple') ? (\n                                    <AppleButton />\n                                ) : null}\n                                {providers.includes('azure') ? (\n                                    <AzureButton />\n                                ) : null}\n                                {providers.includes('bitbucket') ? (\n                                    <BitbucketButton />\n                                ) : null}\n                                {providers.includes('discord') ? (\n                                    <DiscordButton />\n                                ) : null}\n                                {providers.includes('facebook') ? (\n                                    <FacebookButton />\n                                ) : null}\n                                {providers.includes('gitlab') ? (\n                                    <GitlabButton />\n                                ) : null}\n                                {providers.includes('github') ? (\n                                    <GithubButton />\n                                ) : null}\n                                {providers.includes('google') ? (\n                                    <GoogleButton />\n                                ) : null}\n                                {providers.includes('keycloak') ? (\n                                    <KeycloakButton />\n                                ) : null}\n                                {providers.includes('linkedin') ? (\n                                    <LinkedInButton />\n                                ) : null}\n                                {providers.includes('notion') ? (\n                                    <NotionButton />\n                                ) : null}\n                                {providers.includes('slack') ? (\n                                    <SlackButton />\n                                ) : null}\n                                {providers.includes('spotify') ? (\n                                    <SpotifyButton />\n                                ) : null}\n                                {providers.includes('twitch') ? (\n                                    <TwitchButton />\n                                ) : null}\n                                {providers.includes('twitter') ? (\n                                    <TwitterButton />\n                                ) : null}\n                                {providers.includes('workos') ? (\n                                    <WorkosButton />\n                                ) : null}\n                            </Stack>\n                        </>\n                    ) : null}\n                </>\n            )}\n        </AuthLayout>\n    );\n};\n\nexport type LoginPageProps = {\n    children?: ReactNode;\n    disableEmailPassword?: boolean;\n    disableForgotPassword?: boolean;\n    providers?: Provider[];\n};\n", "import * as React from 'react';\nimport { CardActions, styled, Typography } from '@mui/material';\nimport { Form, required, useNotify, useTranslate } from 'ra-core';\nimport { PasswordInput, SaveButton } from 'ra-ui-materialui';\nimport { useSetPassword, useSupabaseAccessToken } from 'ra-supabase-core';\n\n/**\n * A component that renders a form for setting the current user password through Supabase.\n * Can be used for the first login after a user has been invited or to reset the password.\n */\nexport const SetPasswordForm = () => {\n    const access_token = useSupabaseAccessToken();\n    const refresh_token = useSupabaseAccessToken({\n        parameterName: 'refresh_token',\n    });\n\n    const notify = useNotify();\n    const translate = useTranslate();\n    const [setPassword] = useSetPassword({\n        onError: error => {\n            notify(\n                typeof error === 'string'\n                    ? error\n                    : typeof error === 'undefined' || !error.message\n                    ? 'ra.auth.sign_in_error'\n                    : error.message,\n                {\n                    type: 'warning',\n                    messageArgs: {\n                        _:\n                            typeof error === 'string'\n                                ? error\n                                : error && error.message\n                                ? error.message\n                                : undefined,\n                    },\n                }\n            );\n        },\n    });\n\n    const validate = (values: FormData) => {\n        if (values.password !== values.confirmPassword) {\n            return {\n                password: 'ra-supabase.validation.password_mismatch',\n                confirmPassword: 'ra-supabase.validation.password_mismatch',\n            };\n        }\n        return {};\n    };\n\n    if (!access_token || !refresh_token) {\n        if (process.env.NODE_ENV === 'development') {\n            console.error(\n                'Missing access_token or refresh_token for set password'\n            );\n        }\n        return (\n            <div className={SupabaseLoginFormClasses.container}>\n                <div>{translate('ra-supabase.auth.missing_tokens')}</div>\n            </div>\n        );\n    }\n\n    const submit = (values: FormData) => {\n        return setPassword({\n            access_token,\n            refresh_token,\n            password: values.password,\n        });\n    };\n\n    return (\n        <Root onSubmit={submit} validate={validate}>\n            <div className={SupabaseLoginFormClasses.container}>\n                <Typography variant=\"h5\" textAlign=\"center\" gutterBottom>\n                    {translate('ra-supabase.set_password.new_password', {\n                        _: 'Choose your password',\n                    })}\n                </Typography>\n\n                <div className={SupabaseLoginFormClasses.input}>\n                    <PasswordInput\n                        source=\"password\"\n                        label={translate('ra.auth.password', {\n                            _: 'Password',\n                        })}\n                        autoComplete=\"new-password\"\n                        fullWidth\n                        validate={required()}\n                    />\n                </div>\n                <div>\n                    <PasswordInput\n                        source=\"confirmPassword\"\n                        label={translate('ra.auth.confirm_password', {\n                            _: 'Confirm password',\n                        })}\n                        fullWidth\n                        validate={required()}\n                    />\n                </div>\n            </div>\n            <CardActions>\n                <SaveButton\n                    variant=\"contained\"\n                    type=\"submit\"\n                    className={SupabaseLoginFormClasses.button}\n                    label={translate('ra.action.save')}\n                />\n            </CardActions>\n        </Root>\n    );\n};\n\ninterface FormData {\n    password: string;\n    confirmPassword: string;\n}\n\nconst PREFIX = 'RaSupabaseSetPasswordForm';\n\nconst SupabaseLoginFormClasses = {\n    container: `${PREFIX}-container`,\n    input: `${PREFIX}-input`,\n    button: `${PREFIX}-button`,\n};\n\nconst Root = styled(Form, {\n    name: PREFIX,\n    overridesResolver: (props, styles) => styles.root,\n})(({ theme }) => ({\n    [`& .${SupabaseLoginFormClasses.container}`]: {\n        padding: '0 1em 0 1em',\n    },\n    [`& .${SupabaseLoginFormClasses.input}`]: {\n        marginTop: '1em',\n    },\n    [`& .${SupabaseLoginFormClasses.button}`]: {\n        width: '100%',\n    },\n}));\n", "import * as React from 'react';\nimport type { ReactNode } from 'react';\n\nimport { AuthLayout } from './AuthLayout';\nimport { SetPasswordForm } from './SetPasswordForm';\n\n/**\n * A component that renders a page for setting the current user password through Supabase.\n * Can be used for the first login after a user has been invited or to reset the password.\n * @param props\n * @param props.children The content of the page. If not set, it will render a SetPasswordForm.\n *\n * @example\n * import { SetPasswordPage } from 'ra-supabase-ui-materialui';\n * import { Admin, CustomRoutes } from 'react-admin';\n *\n * const App = () => (\n *    <Admin dataProvider={dataProvider}>\n *      <CustomRoutes>\n *       <Route path={SetPasswordPage.path} element={<SetPasswordPage />} />\n *     </CustomRoutes>\n *      ...\n *  </Admin>\n * );\n */\nexport const SetPasswordPage = (props: SetPasswordPageProps) => {\n    const { children = <SetPasswordForm /> } = props;\n\n    return <AuthLayout>{children}</AuthLayout>;\n};\n\nSetPasswordPage.path = '/set-password';\n\nexport type SetPasswordPageProps = {\n    children?: ReactNode;\n};\n", "export const raSupabaseEnglishMessages = {\n    'ra-supabase': {\n        auth: {\n            email: 'Email',\n            confirm_password: 'Confirm password',\n            sign_in_with: 'Sign in with %{provider}',\n            forgot_password: 'Forgot password?',\n            reset_password: 'Reset password',\n            password_reset:\n                'Your password has been reset. You will receive an email containing a link to log in.',\n            missing_tokens: 'Access and refresh tokens are missing',\n            back_to_login: 'Back to login',\n        },\n        reset_password: {\n            forgot_password: 'Forgot password?',\n            forgot_password_details: 'Enter your email for instructions.',\n        },\n        set_password: {\n            new_password: 'Choose your password',\n        },\n        validation: {\n            password_mismatch: 'Passwords do not match',\n        },\n    },\n};\n", "import { mergeTranslations } from 'react-admin';\nimport polyglotI18nProvider from 'ra-i18n-polyglot';\nimport englishMessages from 'ra-language-english';\nimport { raSupabaseEnglishMessages } from 'ra-supabase-language-english';\n\nexport const defaultI18nProvider = polyglotI18nProvider(() => {\n    return mergeTranslations(englishMessages, raSupabaseEnglishMessages);\n}, 'en');\n", "import * as React from 'react';\nimport {\n    AdminContext,\n    AdminUI,\n    Resource,\n    Loading,\n    CustomRoutes,\n} from 'react-admin';\nimport type { AdminProps, AdminUIProps } from 'react-admin';\nimport { Route, BrowserRouter } from 'react-router-dom';\n\nimport { supabaseDataProvider, supabaseAuthProvider } from 'ra-supabase-core';\nimport {\n    useCrudGuesser,\n    LoginPage,\n    SetPasswordPage,\n    ForgotPasswordPage,\n} from 'ra-supabase-ui-materialui';\nimport { createClient } from '@supabase/supabase-js';\nimport { defaultI18nProvider } from './defaultI18nProvider';\n\nexport const AdminGuesser = (\n    props: AdminProps & { instanceUrl: string; apiKey: string }\n) => {\n    const {\n        instanceUrl,\n        apiKey,\n        dataProvider,\n        authProvider,\n        basename,\n        darkTheme,\n        defaultTheme,\n        i18nProvider = defaultI18nProvider,\n        lightTheme,\n        queryClient,\n        store,\n        theme,\n        ...rest\n    } = props;\n\n    const defaultSupabaseClient =\n        instanceUrl && apiKey ? createClient(instanceUrl, apiKey) : null;\n    const defaultDataProvider =\n        instanceUrl && apiKey && defaultSupabaseClient\n            ? supabaseDataProvider({\n                  instanceUrl,\n                  apiKey,\n                  supabaseClient: defaultSupabaseClient,\n              })\n            : undefined;\n    const defaultAuthProvider =\n        instanceUrl && apiKey && defaultSupabaseClient\n            ? supabaseAuthProvider(defaultSupabaseClient, {})\n            : undefined;\n\n    return (\n        <BrowserRouter>\n            <AdminContext\n                authProvider={authProvider ?? defaultAuthProvider}\n                basename={basename}\n                darkTheme={darkTheme}\n                dataProvider={dataProvider ?? defaultDataProvider}\n                defaultTheme={defaultTheme}\n                i18nProvider={i18nProvider}\n                lightTheme={lightTheme}\n                queryClient={queryClient}\n                store={store}\n                theme={theme}\n            >\n                <AdminUIGuesser {...rest} />\n            </AdminContext>\n        </BrowserRouter>\n    );\n};\n\nconst AdminUIGuesser = (props: AdminUIProps) => {\n    const resourceDefinitions = useCrudGuesser();\n    const { children, ...rest } = props;\n    // while we're guessing, we don't want to show the not found page\n    const [CatchAll, setCatchAll] = React.useState<\n        React.ComponentType | undefined\n    >(() => Loading);\n    React.useEffect(() => {\n        if (!children && resourceDefinitions.length > 0) {\n            console.log(\n                `Guessed Admin:\n\nimport { Admin, Resource, CustomRoutes } from 'react-admin';\nimport { BrowserRouter, Route } from 'react-router-dom';\nimport { createClient } from '@supabase/supabase-js';\nimport {\n    CreateGuesser,\n    EditGuesser,\n    ForgotPasswordPage,\n    ListGuesser,\n    LoginPage,\n    SetPasswordPage,\n    ShowGuesser,\n    defaultI18nProvider,\n    supabaseDataProvider,\n    supabaseAuthProvider\n} from 'ra-supabase';   \n\nconst instanceUrl = YOUR_SUPABASE_URL;\nconst apiKey = YOUR_SUPABASE_API_KEY;\nconst supabaseClient = createClient(instanceUrl, apiKey);\nconst dataProvider = supabaseDataProvider({ instanceUrl, apiKey, supabaseClient });\nconst authProvider = supabaseAuthProvider(supabaseClient, {});\n\nexport const App = () => (\n    <BrowserRouter>\n        <Admin\n            dataProvider={dataProvider}\n            authProvider={authProvider}\n            i18nProvider={defaultI18nProvider}\n            loginPage={LoginPage}\n        >${resourceDefinitions\n            .map(\n                def => `\n            <Resource name=\"${def.name}\"${\n                    def.list ? ' list={ListGuesser}' : ''\n                }${def.edit ? ' edit={EditGuesser}' : ''}${\n                    def.create ? ' create={CreateGuesser}' : ''\n                }${def.show ? ' show={ShowGuesser}' : ''} />`\n            )\n            .join('')}\n            <CustomRoutes noLayout>\n                <Route path={SetPasswordPage.path} element={<SetPasswordPage />} />\n                <Route path={ForgotPasswordPage.path} element={<ForgotPasswordPage />} />\n            </CustomRoutes>\n        </Admin>\n    </BrowserRouter>\n);`\n            );\n        }\n    }, [resourceDefinitions, children]);\n\n    React.useEffect(() => {\n        // once we have guessed all the resources, we can show the not found page for unknown paths\n        if (!children && resourceDefinitions.length > 0) {\n            setCatchAll(undefined);\n        }\n    }, [resourceDefinitions, children]);\n\n    const resourceElements = resourceDefinitions.map(resourceDefinition => (\n        <Resource key={resourceDefinition.name} {...resourceDefinition} />\n    )) as any;\n\n    return (\n        <AdminUI\n            ready={Loading}\n            catchAll={CatchAll}\n            loginPage={LoginPage}\n            {...rest}\n        >\n            {children ?? resourceElements}\n            <CustomRoutes noLayout>\n                <Route\n                    path={SetPasswordPage.path}\n                    element={<SetPasswordPage />}\n                />\n                <Route\n                    path={ForgotPasswordPage.path}\n                    element={<ForgotPasswordPage />}\n                />\n            </CustomRoutes>\n        </AdminUI>\n    );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,SAAS,OAAO,QAAQ,cAAc,IAAI;AAC9C,QAAI,oBAAoB,OAAO,4BAA4B,SAAS,OAAO,yBAAyB,IAAI,WAAW,MAAM,IAAI;AAC7H,QAAI,UAAU,UAAU,qBAAqB,OAAO,kBAAkB,QAAQ,aAAa,kBAAkB,MAAM;AACnH,QAAI,aAAa,UAAU,IAAI,UAAU;AACzC,QAAI,SAAS,OAAO,QAAQ,cAAc,IAAI;AAC9C,QAAI,oBAAoB,OAAO,4BAA4B,SAAS,OAAO,yBAAyB,IAAI,WAAW,MAAM,IAAI;AAC7H,QAAI,UAAU,UAAU,qBAAqB,OAAO,kBAAkB,QAAQ,aAAa,kBAAkB,MAAM;AACnH,QAAI,aAAa,UAAU,IAAI,UAAU;AACzC,QAAI,aAAa,OAAO,YAAY,cAAc,QAAQ;AAC1D,QAAI,aAAa,aAAa,QAAQ,UAAU,MAAM;AACtD,QAAI,aAAa,OAAO,YAAY,cAAc,QAAQ;AAC1D,QAAI,aAAa,aAAa,QAAQ,UAAU,MAAM;AACtD,QAAI,aAAa,OAAO,YAAY,cAAc,QAAQ;AAC1D,QAAI,eAAe,aAAa,QAAQ,UAAU,QAAQ;AAC1D,QAAI,iBAAiB,QAAQ,UAAU;AACvC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,SAAS,UAAU;AAC1C,QAAI,SAAS,OAAO,UAAU;AAC9B,QAAI,SAAS,OAAO,UAAU;AAC9B,QAAI,WAAW,OAAO,UAAU;AAChC,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,UAAU,MAAM,UAAU;AAC9B,QAAI,QAAQ,MAAM,UAAU;AAC5B,QAAI,YAAY,MAAM,UAAU;AAChC,QAAI,SAAS,KAAK;AAClB,QAAI,gBAAgB,OAAO,WAAW,aAAa,OAAO,UAAU,UAAU;AAC9E,QAAI,OAAO,OAAO;AAClB,QAAI,cAAc,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,OAAO,UAAU,WAAW;AACpH,QAAI,oBAAoB,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa;AAEnF,QAAI,cAAc,OAAO,WAAW,cAAc,OAAO,gBAAgB,OAAO,OAAO,gBAAgB,oBAAoB,WAAW,YAChI,OAAO,cACP;AACN,QAAI,eAAe,OAAO,UAAU;AAEpC,QAAI,OAAO,OAAO,YAAY,aAAa,QAAQ,iBAAiB,OAAO,oBACvE,CAAC,EAAE,cAAc,MAAM,YACjB,SAAU,GAAG;AACX,aAAO,EAAE;AAAA,IACb,IACE;AAGV,aAAS,oBAAoB,KAAK,KAAK;AACnC,UACI,QAAQ,YACL,QAAQ,aACR,QAAQ,OACP,OAAO,MAAM,QAAS,MAAM,OAC7B,MAAM,KAAK,KAAK,GAAG,GACxB;AACE,eAAO;AAAA,MACX;AACA,UAAI,WAAW;AACf,UAAI,OAAO,QAAQ,UAAU;AACzB,YAAI,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,OAAO,GAAG;AAC9C,YAAI,QAAQ,KAAK;AACb,cAAI,SAAS,OAAO,GAAG;AACvB,cAAI,MAAM,OAAO,KAAK,KAAK,OAAO,SAAS,CAAC;AAC5C,iBAAO,SAAS,KAAK,QAAQ,UAAU,KAAK,IAAI,MAAM,SAAS,KAAK,SAAS,KAAK,KAAK,eAAe,KAAK,GAAG,MAAM,EAAE;AAAA,QAC1H;AAAA,MACJ;AACA,aAAO,SAAS,KAAK,KAAK,UAAU,KAAK;AAAA,IAC7C;AAEA,QAAI,cAAc;AAClB,QAAI,gBAAgB,YAAY;AAChC,QAAI,gBAAgB,SAAS,aAAa,IAAI,gBAAgB;AAE9D,QAAI,SAAS;AAAA,MACT,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,IACZ;AACA,QAAI,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,QAAQ;AAAA,IACZ;AAEA,WAAO,UAAU,SAAS,SAAS,KAAK,SAAS,OAAO,MAAM;AAC1D,UAAI,OAAO,WAAW,CAAC;AAEvB,UAAI,IAAI,MAAM,YAAY,KAAK,CAAC,IAAI,QAAQ,KAAK,UAAU,GAAG;AAC1D,cAAM,IAAI,UAAU,kDAAkD;AAAA,MAC1E;AACA,UACI,IAAI,MAAM,iBAAiB,MAAM,OAAO,KAAK,oBAAoB,WAC3D,KAAK,kBAAkB,KAAK,KAAK,oBAAoB,WACrD,KAAK,oBAAoB,OAEjC;AACE,cAAM,IAAI,UAAU,wFAAwF;AAAA,MAChH;AACA,UAAI,gBAAgB,IAAI,MAAM,eAAe,IAAI,KAAK,gBAAgB;AACtE,UAAI,OAAO,kBAAkB,aAAa,kBAAkB,UAAU;AAClE,cAAM,IAAI,UAAU,+EAA+E;AAAA,MACvG;AAEA,UACI,IAAI,MAAM,QAAQ,KACf,KAAK,WAAW,QAChB,KAAK,WAAW,OAChB,EAAE,SAAS,KAAK,QAAQ,EAAE,MAAM,KAAK,UAAU,KAAK,SAAS,IAClE;AACE,cAAM,IAAI,UAAU,0DAA0D;AAAA,MAClF;AACA,UAAI,IAAI,MAAM,kBAAkB,KAAK,OAAO,KAAK,qBAAqB,WAAW;AAC7E,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AACA,UAAI,mBAAmB,KAAK;AAE5B,UAAI,OAAO,QAAQ,aAAa;AAC5B,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,MAAM;AACd,eAAO;AAAA,MACX;AACA,UAAI,OAAO,QAAQ,WAAW;AAC1B,eAAO,MAAM,SAAS;AAAA,MAC1B;AAEA,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO,cAAc,KAAK,IAAI;AAAA,MAClC;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,YAAI,QAAQ,GAAG;AACX,iBAAO,WAAW,MAAM,IAAI,MAAM;AAAA,QACtC;AACA,YAAI,MAAM,OAAO,GAAG;AACpB,eAAO,mBAAmB,oBAAoB,KAAK,GAAG,IAAI;AAAA,MAC9D;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,YAAI,YAAY,OAAO,GAAG,IAAI;AAC9B,eAAO,mBAAmB,oBAAoB,KAAK,SAAS,IAAI;AAAA,MACpE;AAEA,UAAI,WAAW,OAAO,KAAK,UAAU,cAAc,IAAI,KAAK;AAC5D,UAAI,OAAO,UAAU,aAAa;AAAE,gBAAQ;AAAA,MAAG;AAC/C,UAAI,SAAS,YAAY,WAAW,KAAK,OAAO,QAAQ,UAAU;AAC9D,eAAO,QAAQ,GAAG,IAAI,YAAY;AAAA,MACtC;AAEA,UAAI,SAAS,UAAU,MAAM,KAAK;AAElC,UAAI,OAAO,SAAS,aAAa;AAC7B,eAAO,CAAC;AAAA,MACZ,WAAW,QAAQ,MAAM,GAAG,KAAK,GAAG;AAChC,eAAO;AAAA,MACX;AAEA,eAAS,QAAQ,OAAO,MAAM,UAAU;AACpC,YAAI,MAAM;AACN,iBAAO,UAAU,KAAK,IAAI;AAC1B,eAAK,KAAK,IAAI;AAAA,QAClB;AACA,YAAI,UAAU;AACV,cAAI,UAAU;AAAA,YACV,OAAO,KAAK;AAAA,UAChB;AACA,cAAI,IAAI,MAAM,YAAY,GAAG;AACzB,oBAAQ,aAAa,KAAK;AAAA,UAC9B;AACA,iBAAO,SAAS,OAAO,SAAS,QAAQ,GAAG,IAAI;AAAA,QACnD;AACA,eAAO,SAAS,OAAO,MAAM,QAAQ,GAAG,IAAI;AAAA,MAChD;AAEA,UAAI,OAAO,QAAQ,cAAc,CAAC,SAAS,GAAG,GAAG;AAC7C,YAAI,OAAO,OAAO,GAAG;AACrB,YAAI,OAAO,WAAW,KAAK,OAAO;AAClC,eAAO,eAAe,OAAO,OAAO,OAAO,kBAAkB,OAAO,KAAK,SAAS,IAAI,QAAQ,MAAM,KAAK,MAAM,IAAI,IAAI,OAAO;AAAA,MAClI;AACA,UAAI,SAAS,GAAG,GAAG;AACf,YAAI,YAAY,oBAAoB,SAAS,KAAK,OAAO,GAAG,GAAG,0BAA0B,IAAI,IAAI,YAAY,KAAK,GAAG;AACrH,eAAO,OAAO,QAAQ,YAAY,CAAC,oBAAoB,UAAU,SAAS,IAAI;AAAA,MAClF;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,YAAI,IAAI,MAAM,aAAa,KAAK,OAAO,IAAI,QAAQ,CAAC;AACpD,YAAI,QAAQ,IAAI,cAAc,CAAC;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAK,MAAM,MAAM,CAAC,EAAE,OAAO,MAAM,WAAW,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,UAAU,IAAI;AAAA,QACrF;AACA,aAAK;AACL,YAAI,IAAI,cAAc,IAAI,WAAW,QAAQ;AAAE,eAAK;AAAA,QAAO;AAC3D,aAAK,OAAO,aAAa,KAAK,OAAO,IAAI,QAAQ,CAAC,IAAI;AACtD,eAAO;AAAA,MACX;AACA,UAAI,QAAQ,GAAG,GAAG;AACd,YAAI,IAAI,WAAW,GAAG;AAAE,iBAAO;AAAA,QAAM;AACrC,YAAI,KAAK,WAAW,KAAK,OAAO;AAChC,YAAI,UAAU,CAAC,iBAAiB,EAAE,GAAG;AACjC,iBAAO,MAAM,aAAa,IAAI,MAAM,IAAI;AAAA,QAC5C;AACA,eAAO,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI;AAAA,MACzC;AACA,UAAI,QAAQ,GAAG,GAAG;AACd,YAAI,QAAQ,WAAW,KAAK,OAAO;AACnC,YAAI,EAAE,WAAW,MAAM,cAAc,WAAW,OAAO,CAAC,aAAa,KAAK,KAAK,OAAO,GAAG;AACrF,iBAAO,QAAQ,OAAO,GAAG,IAAI,OAAO,MAAM,KAAK,QAAQ,KAAK,cAAc,QAAQ,IAAI,KAAK,GAAG,KAAK,GAAG,IAAI,IAAI;AAAA,QAClH;AACA,YAAI,MAAM,WAAW,GAAG;AAAE,iBAAO,MAAM,OAAO,GAAG,IAAI;AAAA,QAAK;AAC1D,eAAO,QAAQ,OAAO,GAAG,IAAI,OAAO,MAAM,KAAK,OAAO,IAAI,IAAI;AAAA,MAClE;AACA,UAAI,OAAO,QAAQ,YAAY,eAAe;AAC1C,YAAI,iBAAiB,OAAO,IAAI,aAAa,MAAM,cAAc,aAAa;AAC1E,iBAAO,YAAY,KAAK,EAAE,OAAO,WAAW,MAAM,CAAC;AAAA,QACvD,WAAW,kBAAkB,YAAY,OAAO,IAAI,YAAY,YAAY;AACxE,iBAAO,IAAI,QAAQ;AAAA,QACvB;AAAA,MACJ;AACA,UAAI,MAAM,GAAG,GAAG;AACZ,YAAI,WAAW,CAAC;AAChB,YAAI,YAAY;AACZ,qBAAW,KAAK,KAAK,SAAU,OAAO,KAAK;AACvC,qBAAS,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,SAAS,QAAQ,OAAO,GAAG,CAAC;AAAA,UACxE,CAAC;AAAA,QACL;AACA,eAAO,aAAa,OAAO,QAAQ,KAAK,GAAG,GAAG,UAAU,MAAM;AAAA,MAClE;AACA,UAAI,MAAM,GAAG,GAAG;AACZ,YAAI,WAAW,CAAC;AAChB,YAAI,YAAY;AACZ,qBAAW,KAAK,KAAK,SAAU,OAAO;AAClC,qBAAS,KAAK,QAAQ,OAAO,GAAG,CAAC;AAAA,UACrC,CAAC;AAAA,QACL;AACA,eAAO,aAAa,OAAO,QAAQ,KAAK,GAAG,GAAG,UAAU,MAAM;AAAA,MAClE;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,iBAAiB,SAAS;AAAA,MACrC;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,iBAAiB,SAAS;AAAA,MACrC;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,iBAAiB,SAAS;AAAA,MACrC;AACA,UAAI,SAAS,GAAG,GAAG;AACf,eAAO,UAAU,QAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,MACzC;AACA,UAAI,SAAS,GAAG,GAAG;AACf,eAAO,UAAU,QAAQ,cAAc,KAAK,GAAG,CAAC,CAAC;AAAA,MACrD;AACA,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,MAC7C;AACA,UAAI,SAAS,GAAG,GAAG;AACf,eAAO,UAAU,QAAQ,OAAO,GAAG,CAAC,CAAC;AAAA,MACzC;AAGA,UAAI,OAAO,WAAW,eAAe,QAAQ,QAAQ;AACjD,eAAO;AAAA,MACX;AACA,UACK,OAAO,eAAe,eAAe,QAAQ,cAC1C,OAAO,WAAW,eAAe,QAAQ,QAC/C;AACE,eAAO;AAAA,MACX;AACA,UAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,GAAG,GAAG;AAChC,YAAI,KAAK,WAAW,KAAK,OAAO;AAChC,YAAI,gBAAgB,MAAM,IAAI,GAAG,MAAM,OAAO,YAAY,eAAe,UAAU,IAAI,gBAAgB;AACvG,YAAI,WAAW,eAAe,SAAS,KAAK;AAC5C,YAAI,YAAY,CAAC,iBAAiB,eAAe,OAAO,GAAG,MAAM,OAAO,eAAe,MAAM,OAAO,KAAK,MAAM,GAAG,GAAG,GAAG,EAAE,IAAI,WAAW,WAAW;AACpJ,YAAI,iBAAiB,iBAAiB,OAAO,IAAI,gBAAgB,aAAa,KAAK,IAAI,YAAY,OAAO,IAAI,YAAY,OAAO,MAAM;AACvI,YAAI,MAAM,kBAAkB,aAAa,WAAW,MAAM,MAAM,KAAK,QAAQ,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,IAAI,IAAI,OAAO;AACvI,YAAI,GAAG,WAAW,GAAG;AAAE,iBAAO,MAAM;AAAA,QAAM;AAC1C,YAAI,QAAQ;AACR,iBAAO,MAAM,MAAM,aAAa,IAAI,MAAM,IAAI;AAAA,QAClD;AACA,eAAO,MAAM,OAAO,MAAM,KAAK,IAAI,IAAI,IAAI;AAAA,MAC/C;AACA,aAAO,OAAO,GAAG;AAAA,IACrB;AAEA,aAAS,WAAW,GAAG,cAAc,MAAM;AACvC,UAAI,QAAQ,KAAK,cAAc;AAC/B,UAAI,YAAY,OAAO,KAAK;AAC5B,aAAO,YAAY,IAAI;AAAA,IAC3B;AAEA,aAAS,MAAM,GAAG;AACd,aAAO,SAAS,KAAK,OAAO,CAAC,GAAG,MAAM,QAAQ;AAAA,IAClD;AAEA,aAAS,iBAAiB,KAAK;AAC3B,aAAO,CAAC,eAAe,EAAE,OAAO,QAAQ,aAAa,eAAe,OAAO,OAAO,IAAI,WAAW,MAAM;AAAA,IAC3G;AACA,aAAS,QAAQ,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,oBAAoB,iBAAiB,GAAG;AAAA,IAAG;AACzF,aAAS,OAAO,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,mBAAmB,iBAAiB,GAAG;AAAA,IAAG;AACvF,aAAS,SAAS,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,qBAAqB,iBAAiB,GAAG;AAAA,IAAG;AAC3F,aAAS,QAAQ,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,oBAAoB,iBAAiB,GAAG;AAAA,IAAG;AACzF,aAAS,SAAS,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,qBAAqB,iBAAiB,GAAG;AAAA,IAAG;AAC3F,aAAS,SAAS,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,qBAAqB,iBAAiB,GAAG;AAAA,IAAG;AAC3F,aAAS,UAAU,KAAK;AAAE,aAAO,MAAM,GAAG,MAAM,sBAAsB,iBAAiB,GAAG;AAAA,IAAG;AAG7F,aAAS,SAAS,KAAK;AACnB,UAAI,mBAAmB;AACnB,eAAO,OAAO,OAAO,QAAQ,YAAY,eAAe;AAAA,MAC5D;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO;AAAA,MACX;AACA,UAAI,CAAC,OAAO,OAAO,QAAQ,YAAY,CAAC,aAAa;AACjD,eAAO;AAAA,MACX;AACA,UAAI;AACA,oBAAY,KAAK,GAAG;AACpB,eAAO;AAAA,MACX,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,SAAS,KAAK;AACnB,UAAI,CAAC,OAAO,OAAO,QAAQ,YAAY,CAAC,eAAe;AACnD,eAAO;AAAA,MACX;AACA,UAAI;AACA,sBAAc,KAAK,GAAG;AACtB,eAAO;AAAA,MACX,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,QAAI,SAAS,OAAO,UAAU,kBAAkB,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAM;AACrF,aAAS,IAAI,KAAK,KAAK;AACnB,aAAO,OAAO,KAAK,KAAK,GAAG;AAAA,IAC/B;AAEA,aAAS,MAAM,KAAK;AAChB,aAAO,eAAe,KAAK,GAAG;AAAA,IAClC;AAEA,aAAS,OAAO,GAAG;AACf,UAAI,EAAE,MAAM;AAAE,eAAO,EAAE;AAAA,MAAM;AAC7B,UAAI,IAAI,OAAO,KAAK,iBAAiB,KAAK,CAAC,GAAG,sBAAsB;AACpE,UAAI,GAAG;AAAE,eAAO,EAAE,CAAC;AAAA,MAAG;AACtB,aAAO;AAAA,IACX;AAEA,aAAS,QAAQ,IAAI,GAAG;AACpB,UAAI,GAAG,SAAS;AAAE,eAAO,GAAG,QAAQ,CAAC;AAAA,MAAG;AACxC,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,KAAK;AACvC,YAAI,GAAG,CAAC,MAAM,GAAG;AAAE,iBAAO;AAAA,QAAG;AAAA,MACjC;AACA,aAAO;AAAA,IACX;AAEA,aAAS,MAAM,GAAG;AACd,UAAI,CAAC,WAAW,CAAC,KAAK,OAAO,MAAM,UAAU;AACzC,eAAO;AAAA,MACX;AACA,UAAI;AACA,gBAAQ,KAAK,CAAC;AACd,YAAI;AACA,kBAAQ,KAAK,CAAC;AAAA,QAClB,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AACA,eAAO,aAAa;AAAA,MACxB,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,GAAG;AAClB,UAAI,CAAC,cAAc,CAAC,KAAK,OAAO,MAAM,UAAU;AAC5C,eAAO;AAAA,MACX;AACA,UAAI;AACA,mBAAW,KAAK,GAAG,UAAU;AAC7B,YAAI;AACA,qBAAW,KAAK,GAAG,UAAU;AAAA,QACjC,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AACA,eAAO,aAAa;AAAA,MACxB,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,GAAG;AAClB,UAAI,CAAC,gBAAgB,CAAC,KAAK,OAAO,MAAM,UAAU;AAC9C,eAAO;AAAA,MACX;AACA,UAAI;AACA,qBAAa,KAAK,CAAC;AACnB,eAAO;AAAA,MACX,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,MAAM,GAAG;AACd,UAAI,CAAC,WAAW,CAAC,KAAK,OAAO,MAAM,UAAU;AACzC,eAAO;AAAA,MACX;AACA,UAAI;AACA,gBAAQ,KAAK,CAAC;AACd,YAAI;AACA,kBAAQ,KAAK,CAAC;AAAA,QAClB,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AACA,eAAO,aAAa;AAAA,MACxB,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,GAAG;AAClB,UAAI,CAAC,cAAc,CAAC,KAAK,OAAO,MAAM,UAAU;AAC5C,eAAO;AAAA,MACX;AACA,UAAI;AACA,mBAAW,KAAK,GAAG,UAAU;AAC7B,YAAI;AACA,qBAAW,KAAK,GAAG,UAAU;AAAA,QACjC,SAAS,GAAG;AACR,iBAAO;AAAA,QACX;AACA,eAAO,aAAa;AAAA,MACxB,SAAS,GAAG;AAAA,MAAC;AACb,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,GAAG;AAClB,UAAI,CAAC,KAAK,OAAO,MAAM,UAAU;AAAE,eAAO;AAAA,MAAO;AACjD,UAAI,OAAO,gBAAgB,eAAe,aAAa,aAAa;AAChE,eAAO;AAAA,MACX;AACA,aAAO,OAAO,EAAE,aAAa,YAAY,OAAO,EAAE,iBAAiB;AAAA,IACvE;AAEA,aAAS,cAAc,KAAK,MAAM;AAC9B,UAAI,IAAI,SAAS,KAAK,iBAAiB;AACnC,YAAI,YAAY,IAAI,SAAS,KAAK;AAClC,YAAI,UAAU,SAAS,YAAY,qBAAqB,YAAY,IAAI,MAAM;AAC9E,eAAO,cAAc,OAAO,KAAK,KAAK,GAAG,KAAK,eAAe,GAAG,IAAI,IAAI;AAAA,MAC5E;AACA,UAAI,UAAU,SAAS,KAAK,cAAc,QAAQ;AAClD,cAAQ,YAAY;AAEpB,UAAI,IAAI,SAAS,KAAK,SAAS,KAAK,KAAK,SAAS,MAAM,GAAG,gBAAgB,OAAO;AAClF,aAAO,WAAW,GAAG,UAAU,IAAI;AAAA,IACvC;AAEA,aAAS,QAAQ,GAAG;AAChB,UAAI,IAAI,EAAE,WAAW,CAAC;AACtB,UAAI,IAAI;AAAA,QACJ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACR,EAAE,CAAC;AACH,UAAI,GAAG;AAAE,eAAO,OAAO;AAAA,MAAG;AAC1B,aAAO,SAAS,IAAI,KAAO,MAAM,MAAM,aAAa,KAAK,EAAE,SAAS,EAAE,CAAC;AAAA,IAC3E;AAEA,aAAS,UAAU,KAAK;AACpB,aAAO,YAAY,MAAM;AAAA,IAC7B;AAEA,aAAS,iBAAiB,MAAM;AAC5B,aAAO,OAAO;AAAA,IAClB;AAEA,aAAS,aAAa,MAAM,MAAM,SAAS,QAAQ;AAC/C,UAAI,gBAAgB,SAAS,aAAa,SAAS,MAAM,IAAI,MAAM,KAAK,SAAS,IAAI;AACrF,aAAO,OAAO,OAAO,OAAO,QAAQ,gBAAgB;AAAA,IACxD;AAEA,aAAS,iBAAiB,IAAI;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,YAAI,QAAQ,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG;AAC3B,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,aAAS,UAAU,MAAM,OAAO;AAC5B,UAAI;AACJ,UAAI,KAAK,WAAW,KAAM;AACtB,qBAAa;AAAA,MACjB,WAAW,OAAO,KAAK,WAAW,YAAY,KAAK,SAAS,GAAG;AAC3D,qBAAa,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,GAAG,GAAG;AAAA,MACvD,OAAO;AACH,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,MAAM;AAAA,QACN,MAAM,MAAM,KAAK,MAAM,QAAQ,CAAC,GAAG,UAAU;AAAA,MACjD;AAAA,IACJ;AAEA,aAAS,aAAa,IAAI,QAAQ;AAC9B,UAAI,GAAG,WAAW,GAAG;AAAE,eAAO;AAAA,MAAI;AAClC,UAAI,aAAa,OAAO,OAAO,OAAO,OAAO;AAC7C,aAAO,aAAa,MAAM,KAAK,IAAI,MAAM,UAAU,IAAI,OAAO,OAAO;AAAA,IACzE;AAEA,aAAS,WAAW,KAAK,SAAS;AAC9B,UAAI,QAAQ,QAAQ,GAAG;AACvB,UAAI,KAAK,CAAC;AACV,UAAI,OAAO;AACP,WAAG,SAAS,IAAI;AAChB,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,aAAG,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI;AAAA,QACjD;AAAA,MACJ;AACA,UAAI,OAAO,OAAO,SAAS,aAAa,KAAK,GAAG,IAAI,CAAC;AACrD,UAAI;AACJ,UAAI,mBAAmB;AACnB,iBAAS,CAAC;AACV,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,iBAAO,MAAM,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AAAA,QAClC;AAAA,MACJ;AAEA,eAAS,OAAO,KAAK;AACjB,YAAI,CAAC,IAAI,KAAK,GAAG,GAAG;AAAE;AAAA,QAAU;AAChC,YAAI,SAAS,OAAO,OAAO,GAAG,CAAC,MAAM,OAAO,MAAM,IAAI,QAAQ;AAAE;AAAA,QAAU;AAC1E,YAAI,qBAAqB,OAAO,MAAM,GAAG,aAAa,QAAQ;AAE1D;AAAA,QACJ,WAAW,MAAM,KAAK,UAAU,GAAG,GAAG;AAClC,aAAG,KAAK,QAAQ,KAAK,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;AAAA,QAC7D,OAAO;AACH,aAAG,KAAK,MAAM,OAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;AAAA,QAC/C;AAAA,MACJ;AACA,UAAI,OAAO,SAAS,YAAY;AAC5B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,cAAI,aAAa,KAAK,KAAK,KAAK,CAAC,CAAC,GAAG;AACjC,eAAG,KAAK,MAAM,QAAQ,KAAK,CAAC,CAAC,IAAI,QAAQ,QAAQ,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;AAAA,UACvE;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;AC/hBA;AAAA;AAAA;AAEA,QAAI,UAAU;AAEd,QAAI,aAAa;AAUjB,QAAI,cAAc,SAAU,MAAM,KAAK,UAAU;AAEhD,UAAI,OAAO;AAEX,UAAI;AAEJ,cAAQ,OAAO,KAAK,SAAS,MAAM,OAAO,MAAM;AAC/C,YAAI,KAAK,QAAQ,KAAK;AACrB,eAAK,OAAO,KAAK;AACjB,cAAI,CAAC,UAAU;AAEd,iBAAK;AAAA,YAAqD,KAAK;AAC/D,iBAAK,OAAO;AAAA,UACb;AACA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAGA,QAAI,UAAU,SAAU,SAAS,KAAK;AACrC,UAAI,CAAC,SAAS;AACb,eAAO;AAAA,MACR;AACA,UAAI,OAAO,YAAY,SAAS,GAAG;AACnC,aAAO,QAAQ,KAAK;AAAA,IACrB;AAEA,QAAI,UAAU,SAAU,SAAS,KAAK,OAAO;AAC5C,UAAI,OAAO,YAAY,SAAS,GAAG;AACnC,UAAI,MAAM;AACT,aAAK,QAAQ;AAAA,MACd,OAAO;AAEN,gBAAQ;AAAA,QAAgF;AAAA;AAAA,UACvF;AAAA,UACA,MAAM,QAAQ;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,UAAU,SAAU,SAAS,KAAK;AACrC,UAAI,CAAC,SAAS;AACb,eAAO;AAAA,MACR;AACA,aAAO,CAAC,CAAC,YAAY,SAAS,GAAG;AAAA,IAClC;AAGA,QAAI,aAAa,SAAU,SAAS,KAAK;AACxC,UAAI,SAAS;AACZ,eAAO,YAAY,SAAS,KAAK,IAAI;AAAA,MACtC;AAAA,IACD;AAGA,WAAO,UAAU,SAAS,qBAAqB;AAKkB,UAAI;AAGpE,UAAI,UAAU;AAAA,QACb,QAAQ,SAAU,KAAK;AACtB,cAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACtB,kBAAM,IAAI,WAAW,mCAAmC,QAAQ,GAAG,CAAC;AAAA,UACrE;AAAA,QACD;AAAA,QACA,UAAU,SAAU,KAAK;AACxB,cAAI,OAAO,MAAM,GAAG;AACpB,cAAI,cAAc,WAAW,IAAI,GAAG;AACpC,cAAI,eAAe,QAAQ,SAAS,aAAa;AAChD,iBAAK;AAAA,UACN;AACA,iBAAO,CAAC,CAAC;AAAA,QACV;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,iBAAO,QAAQ,IAAI,GAAG;AAAA,QACvB;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,iBAAO,QAAQ,IAAI,GAAG;AAAA,QACvB;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AAC1B,cAAI,CAAC,IAAI;AAER,iBAAK;AAAA,cACJ,MAAM;AAAA,YACP;AAAA,UACD;AAEA;AAAA;AAAA,YAA+C;AAAA,YAAK;AAAA,YAAK;AAAA,UAAK;AAAA,QAC/D;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AChHA;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,YAAY;AAChB,QAAI,UAAU;AAEd,QAAI,aAAa;AACjB,QAAI,OAAO,aAAa,SAAS,IAAI;AAGrC,QAAI,UAAU,UAAU,qBAAqB,IAAI;AAEjD,QAAI,UAAU,UAAU,qBAAqB,IAAI;AAEjD,QAAI,UAAU,UAAU,qBAAqB,IAAI;AAEjD,QAAI,aAAa,UAAU,wBAAwB,IAAI;AAEvD,QAAI,WAAW,UAAU,sBAAsB,IAAI;AAGnD,WAAO,UAAU,CAAC,CAAC;AAAA,IAAmD,SAAS,oBAAoB;AAK7D,UAAI;AAGzC,UAAI,UAAU;AAAA,QACb,QAAQ,SAAU,KAAK;AACtB,cAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACtB,kBAAM,IAAI,WAAW,mCAAmC,QAAQ,GAAG,CAAC;AAAA,UACrE;AAAA,QACD;AAAA,QACA,UAAU,SAAU,KAAK;AACxB,cAAI,IAAI;AACP,gBAAI,SAAS,WAAW,IAAI,GAAG;AAC/B,gBAAI,SAAS,EAAE,MAAM,GAAG;AACvB,mBAAK;AAAA,YACN;AACA,mBAAO;AAAA,UACR;AACA,iBAAO;AAAA,QACR;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,cAAI,IAAI;AACP,mBAAO,QAAQ,IAAI,GAAG;AAAA,UACvB;AAAA,QACD;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,cAAI,IAAI;AACP,mBAAO,QAAQ,IAAI,GAAG;AAAA,UACvB;AACA,iBAAO;AAAA,QACR;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AAC1B,cAAI,CAAC,IAAI;AAER,iBAAK,IAAI,KAAK;AAAA,UACf;AACA,kBAAQ,IAAI,KAAK,KAAK;AAAA,QACvB;AAAA,MACD;AAGA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACnEA;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,YAAY;AAChB,QAAI,UAAU;AACd,QAAI,oBAAoB;AAExB,QAAI,aAAa;AACjB,QAAI,WAAW,aAAa,aAAa,IAAI;AAG7C,QAAI,cAAc,UAAU,yBAAyB,IAAI;AAEzD,QAAI,cAAc,UAAU,yBAAyB,IAAI;AAEzD,QAAI,cAAc,UAAU,yBAAyB,IAAI;AAEzD,QAAI,iBAAiB,UAAU,4BAA4B,IAAI;AAG/D,WAAO,UAAU;AAAA;AAAA,MAC6B,SAAS,wBAAwB;AAK3B,YAAI;AACnB,YAAI;AAGvC,YAAI,UAAU;AAAA,UACb,QAAQ,SAAU,KAAK;AACtB,gBAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACtB,oBAAM,IAAI,WAAW,mCAAmC,QAAQ,GAAG,CAAC;AAAA,YACrE;AAAA,UACD;AAAA,UACA,UAAU,SAAU,KAAK;AACxB,gBAAI,YAAY,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AAC9E,kBAAI,KAAK;AACR,uBAAO,eAAe,KAAK,GAAG;AAAA,cAC/B;AAAA,YACD,WAAW,mBAAmB;AAC7B,kBAAI,IAAI;AACP,uBAAO,GAAG,QAAQ,EAAE,GAAG;AAAA,cACxB;AAAA,YACD;AACA,mBAAO;AAAA,UACR;AAAA,UACA,KAAK,SAAU,KAAK;AACnB,gBAAI,YAAY,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AAC9E,kBAAI,KAAK;AACR,uBAAO,YAAY,KAAK,GAAG;AAAA,cAC5B;AAAA,YACD;AACA,mBAAO,MAAM,GAAG,IAAI,GAAG;AAAA,UACxB;AAAA,UACA,KAAK,SAAU,KAAK;AACnB,gBAAI,YAAY,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AAC9E,kBAAI,KAAK;AACR,uBAAO,YAAY,KAAK,GAAG;AAAA,cAC5B;AAAA,YACD;AACA,mBAAO,CAAC,CAAC,MAAM,GAAG,IAAI,GAAG;AAAA,UAC1B;AAAA,UACA,KAAK,SAAU,KAAK,OAAO;AAC1B,gBAAI,YAAY,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AAC9E,kBAAI,CAAC,KAAK;AACT,sBAAM,IAAI,SAAS;AAAA,cACpB;AACA,0BAAY,KAAK,KAAK,KAAK;AAAA,YAC5B,WAAW,mBAAmB;AAC7B,kBAAI,CAAC,IAAI;AACR,qBAAK,kBAAkB;AAAA,cACxB;AAEsC,cAAC,GAAI,IAAI,KAAK,KAAK;AAAA,YAC1D;AAAA,UACD;AAAA,QACD;AAGA,eAAO;AAAA,MACR;AAAA,QACE;AAAA;AAAA;;;ACnFH;AAAA;AAAA;AAEA,QAAI,aAAa;AACjB,QAAI,UAAU;AACd,QAAI,qBAAqB;AACzB,QAAI,oBAAoB;AACxB,QAAI,wBAAwB;AAE5B,QAAI,cAAc,yBAAyB,qBAAqB;AAGhE,WAAO,UAAU,SAAS,iBAAiB;AAGP,UAAI;AAGvC,UAAI,UAAU;AAAA,QACb,QAAQ,SAAU,KAAK;AACtB,cAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AACtB,kBAAM,IAAI,WAAW,mCAAmC,QAAQ,GAAG,CAAC;AAAA,UACrE;AAAA,QACD;AAAA,QACA,UAAU,SAAU,KAAK;AACxB,iBAAO,CAAC,CAAC,gBAAgB,aAAa,QAAQ,EAAE,GAAG;AAAA,QACpD;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,iBAAO,gBAAgB,aAAa,IAAI,GAAG;AAAA,QAC5C;AAAA,QACA,KAAK,SAAU,KAAK;AACnB,iBAAO,CAAC,CAAC,gBAAgB,aAAa,IAAI,GAAG;AAAA,QAC9C;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AAC1B,cAAI,CAAC,cAAc;AAClB,2BAAe,YAAY;AAAA,UAC5B;AAEA,uBAAa,IAAI,KAAK,KAAK;AAAA,QAC5B;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC1CA;AAAA;AAAA;AAEA,QAAI,UAAU,OAAO,UAAU;AAC/B,QAAI,kBAAkB;AAEtB,QAAI,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,IACb;AAEA,WAAO,UAAU;AAAA,MACb,WAAW,OAAO;AAAA,MAClB,YAAY;AAAA,QACR,SAAS,SAAU,OAAO;AACtB,iBAAO,QAAQ,KAAK,OAAO,iBAAiB,GAAG;AAAA,QACnD;AAAA,QACA,SAAS,SAAU,OAAO;AACtB,iBAAO,OAAO,KAAK;AAAA,QACvB;AAAA,MACJ;AAAA,MACA,SAAS,OAAO;AAAA,MAChB,SAAS,OAAO;AAAA,IACpB;AAAA;AAAA;;;ACtBA;AAAA;AAAA;AAEA,QAAI,UAAU;AAEd,QAAI,MAAM,OAAO,UAAU;AAC3B,QAAI,UAAU,MAAM;AAEpB,QAAI,WAAY,WAAY;AACxB,UAAI,QAAQ,CAAC;AACb,eAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,cAAM,KAAK,QAAQ,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,EAAE,GAAG,YAAY,CAAC;AAAA,MACzE;AAEA,aAAO;AAAA,IACX,EAAE;AAEF,QAAI,eAAe,SAASA,cAAa,OAAO;AAC5C,aAAO,MAAM,SAAS,GAAG;AACrB,YAAI,OAAO,MAAM,IAAI;AACrB,YAAI,MAAM,KAAK,IAAI,KAAK,IAAI;AAE5B,YAAI,QAAQ,GAAG,GAAG;AACd,cAAI,YAAY,CAAC;AAEjB,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACjC,gBAAI,OAAO,IAAI,CAAC,MAAM,aAAa;AAC/B,wBAAU,KAAK,IAAI,CAAC,CAAC;AAAA,YACzB;AAAA,UACJ;AAEA,eAAK,IAAI,KAAK,IAAI,IAAI;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,gBAAgB,SAASC,eAAc,QAAQ,SAAS;AACxD,UAAI,MAAM,WAAW,QAAQ,eAAe,EAAE,WAAW,KAAK,IAAI,CAAC;AACnE,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,YAAI,OAAO,OAAO,CAAC,MAAM,aAAa;AAClC,cAAI,CAAC,IAAI,OAAO,CAAC;AAAA,QACrB;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,QAAQ,SAASC,OAAM,QAAQ,QAAQ,SAAS;AAEhD,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,WAAW,YAAY,OAAO,WAAW,YAAY;AAC5D,YAAI,QAAQ,MAAM,GAAG;AACjB,iBAAO,KAAK,MAAM;AAAA,QACtB,WAAW,UAAU,OAAO,WAAW,UAAU;AAC7C,cACK,YAAY,QAAQ,gBAAgB,QAAQ,oBAC1C,CAAC,IAAI,KAAK,OAAO,WAAW,MAAM,GACvC;AACE,mBAAO,MAAM,IAAI;AAAA,UACrB;AAAA,QACJ,OAAO;AACH,iBAAO,CAAC,QAAQ,MAAM;AAAA,QAC1B;AAEA,eAAO;AAAA,MACX;AAEA,UAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACvC,eAAO,CAAC,MAAM,EAAE,OAAO,MAAM;AAAA,MACjC;AAEA,UAAI,cAAc;AAClB,UAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,GAAG;AACrC,sBAAc,cAAc,QAAQ,OAAO;AAAA,MAC/C;AAEA,UAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,GAAG;AACpC,eAAO,QAAQ,SAAU,MAAM,GAAG;AAC9B,cAAI,IAAI,KAAK,QAAQ,CAAC,GAAG;AACrB,gBAAI,aAAa,OAAO,CAAC;AACzB,gBAAI,cAAc,OAAO,eAAe,YAAY,QAAQ,OAAO,SAAS,UAAU;AAClF,qBAAO,CAAC,IAAIA,OAAM,YAAY,MAAM,OAAO;AAAA,YAC/C,OAAO;AACH,qBAAO,KAAK,IAAI;AAAA,YACpB;AAAA,UACJ,OAAO;AACH,mBAAO,CAAC,IAAI;AAAA,UAChB;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AAEA,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,KAAK;AAClD,YAAI,QAAQ,OAAO,GAAG;AAEtB,YAAI,IAAI,KAAK,KAAK,GAAG,GAAG;AACpB,cAAI,GAAG,IAAIA,OAAM,IAAI,GAAG,GAAG,OAAO,OAAO;AAAA,QAC7C,OAAO;AACH,cAAI,GAAG,IAAI;AAAA,QACf;AACA,eAAO;AAAA,MACX,GAAG,WAAW;AAAA,IAClB;AAEA,QAAI,SAAS,SAAS,mBAAmB,QAAQ,QAAQ;AACrD,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,KAAK;AAClD,YAAI,GAAG,IAAI,OAAO,GAAG;AACrB,eAAO;AAAA,MACX,GAAG,MAAM;AAAA,IACb;AAEA,QAAI,SAAS,SAAU,KAAK,gBAAgB,SAAS;AACjD,UAAI,iBAAiB,IAAI,QAAQ,OAAO,GAAG;AAC3C,UAAI,YAAY,cAAc;AAE1B,eAAO,eAAe,QAAQ,kBAAkB,QAAQ;AAAA,MAC5D;AAEA,UAAI;AACA,eAAO,mBAAmB,cAAc;AAAA,MAC5C,SAAS,GAAG;AACR,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,QAAQ;AAIZ,QAAI,SAAS,SAASC,QAAO,KAAK,gBAAgB,SAAS,MAAM,QAAQ;AAGrE,UAAI,IAAI,WAAW,GAAG;AAClB,eAAO;AAAA,MACX;AAEA,UAAI,SAAS;AACb,UAAI,OAAO,QAAQ,UAAU;AACzB,iBAAS,OAAO,UAAU,SAAS,KAAK,GAAG;AAAA,MAC/C,WAAW,OAAO,QAAQ,UAAU;AAChC,iBAAS,OAAO,GAAG;AAAA,MACvB;AAEA,UAAI,YAAY,cAAc;AAC1B,eAAO,OAAO,MAAM,EAAE,QAAQ,mBAAmB,SAAU,IAAI;AAC3D,iBAAO,WAAW,SAAS,GAAG,MAAM,CAAC,GAAG,EAAE,IAAI;AAAA,QAClD,CAAC;AAAA,MACL;AAEA,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,OAAO;AAC3C,YAAI,UAAU,OAAO,UAAU,QAAQ,OAAO,MAAM,GAAG,IAAI,KAAK,IAAI;AACpE,YAAI,MAAM,CAAC;AAEX,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,cAAI,IAAI,QAAQ,WAAW,CAAC;AAC5B,cACI,MAAM,MACH,MAAM,MACN,MAAM,MACN,MAAM,OACL,KAAK,MAAQ,KAAK,MAClB,KAAK,MAAQ,KAAK,MAClB,KAAK,MAAQ,KAAK,OAClB,WAAW,QAAQ,YAAY,MAAM,MAAQ,MAAM,KACzD;AACE,gBAAI,IAAI,MAAM,IAAI,QAAQ,OAAO,CAAC;AAClC;AAAA,UACJ;AAEA,cAAI,IAAI,KAAM;AACV,gBAAI,IAAI,MAAM,IAAI,SAAS,CAAC;AAC5B;AAAA,UACJ;AAEA,cAAI,IAAI,MAAO;AACX,gBAAI,IAAI,MAAM,IAAI,SAAS,MAAQ,KAAK,CAAE,IACpC,SAAS,MAAQ,IAAI,EAAK;AAChC;AAAA,UACJ;AAEA,cAAI,IAAI,SAAU,KAAK,OAAQ;AAC3B,gBAAI,IAAI,MAAM,IAAI,SAAS,MAAQ,KAAK,EAAG,IACrC,SAAS,MAAS,KAAK,IAAK,EAAK,IACjC,SAAS,MAAQ,IAAI,EAAK;AAChC;AAAA,UACJ;AAEA,eAAK;AACL,cAAI,UAAa,IAAI,SAAU,KAAO,QAAQ,WAAW,CAAC,IAAI;AAE9D,cAAI,IAAI,MAAM,IAAI,SAAS,MAAQ,KAAK,EAAG,IACrC,SAAS,MAAS,KAAK,KAAM,EAAK,IAClC,SAAS,MAAS,KAAK,IAAK,EAAK,IACjC,SAAS,MAAQ,IAAI,EAAK;AAAA,QACpC;AAEA,eAAO,IAAI,KAAK,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,UAAU,SAASC,SAAQ,OAAO;AAClC,UAAI,QAAQ,CAAC,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG,MAAM,IAAI,CAAC;AAC7C,UAAI,OAAO,CAAC;AAEZ,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACnC,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,MAAM,KAAK,IAAI,KAAK,IAAI;AAE5B,YAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,cAAI,MAAM,KAAK,CAAC;AAChB,cAAI,MAAM,IAAI,GAAG;AACjB,cAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,KAAK,QAAQ,GAAG,MAAM,IAAI;AACrE,kBAAM,KAAK,EAAE,KAAU,MAAM,IAAI,CAAC;AAClC,iBAAK,KAAK,GAAG;AAAA,UACjB;AAAA,QACJ;AAAA,MACJ;AAEA,mBAAa,KAAK;AAElB,aAAO;AAAA,IACX;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK;AAClC,aAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,IACnD;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK;AAClC,UAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACjC,eAAO;AAAA,MACX;AAEA,aAAO,CAAC,EAAE,IAAI,eAAe,IAAI,YAAY,YAAY,IAAI,YAAY,SAAS,GAAG;AAAA,IACzF;AAEA,QAAI,UAAU,SAASC,SAAQ,GAAG,GAAG;AACjC,aAAO,CAAC,EAAE,OAAO,GAAG,CAAC;AAAA,IACzB;AAEA,QAAI,WAAW,SAASC,UAAS,KAAK,IAAI;AACtC,UAAI,QAAQ,GAAG,GAAG;AACd,YAAI,SAAS,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACpC,iBAAO,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA,QAC1B;AACA,eAAO;AAAA,MACX;AACA,aAAO,GAAG,GAAG;AAAA,IACjB;AAEA,WAAO,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA;AAAA;;;AC3QA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AACrB,QAAI,QAAQ;AACZ,QAAI,UAAU;AACd,QAAI,MAAM,OAAO,UAAU;AAE3B,QAAI,wBAAwB;AAAA,MACxB,UAAU,SAAS,SAAS,QAAQ;AAChC,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,OAAO;AAAA,MACP,SAAS,SAAS,QAAQ,QAAQ,KAAK;AACnC,eAAO,SAAS,MAAM,MAAM;AAAA,MAChC;AAAA,MACA,QAAQ,SAAS,OAAO,QAAQ;AAC5B,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,QAAI,UAAU,MAAM;AACpB,QAAI,OAAO,MAAM,UAAU;AAC3B,QAAI,cAAc,SAAU,KAAK,cAAc;AAC3C,WAAK,MAAM,KAAK,QAAQ,YAAY,IAAI,eAAe,CAAC,YAAY,CAAC;AAAA,IACzE;AAEA,QAAI,QAAQ,KAAK,UAAU;AAE3B,QAAI,gBAAgB,QAAQ,SAAS;AACrC,QAAI,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,SAAS,MAAM;AAAA,MACf,kBAAkB;AAAA,MAClB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,WAAW,QAAQ,WAAW,aAAa;AAAA;AAAA,MAE3C,SAAS;AAAA,MACT,eAAe,SAAS,cAAc,MAAM;AACxC,eAAO,MAAM,KAAK,IAAI;AAAA,MAC1B;AAAA,MACA,WAAW;AAAA,MACX,oBAAoB;AAAA,IACxB;AAEA,QAAI,wBAAwB,SAASC,uBAAsB,GAAG;AAC1D,aAAO,OAAO,MAAM,YACb,OAAO,MAAM,YACb,OAAO,MAAM,aACb,OAAO,MAAM,YACb,OAAO,MAAM;AAAA,IACxB;AAEA,QAAI,WAAW,CAAC;AAEhB,QAAI,YAAY,SAASC,WACrB,QACA,QACA,qBACA,gBACA,kBACA,oBACA,WACA,iBACA,SACA,QACA,MACA,WACA,eACA,QACA,WACA,kBACA,SACA,aACF;AACE,UAAI,MAAM;AAEV,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,UAAI,WAAW;AACf,cAAQ,QAAQ,MAAM,IAAI,QAAQ,OAAO,UAAkB,CAAC,UAAU;AAElE,YAAI,MAAM,MAAM,IAAI,MAAM;AAC1B,gBAAQ;AACR,YAAI,OAAO,QAAQ,aAAa;AAC5B,cAAI,QAAQ,MAAM;AACd,kBAAM,IAAI,WAAW,qBAAqB;AAAA,UAC9C,OAAO;AACH,uBAAW;AAAA,UACf;AAAA,QACJ;AACA,YAAI,OAAO,MAAM,IAAI,QAAQ,MAAM,aAAa;AAC5C,iBAAO;AAAA,QACX;AAAA,MACJ;AAEA,UAAI,OAAO,WAAW,YAAY;AAC9B,cAAM,OAAO,QAAQ,GAAG;AAAA,MAC5B,WAAW,eAAe,MAAM;AAC5B,cAAM,cAAc,GAAG;AAAA,MAC3B,WAAW,wBAAwB,WAAW,QAAQ,GAAG,GAAG;AACxD,cAAM,MAAM,SAAS,KAAK,SAAUC,QAAO;AACvC,cAAIA,kBAAiB,MAAM;AACvB,mBAAO,cAAcA,MAAK;AAAA,UAC9B;AACA,iBAAOA;AAAA,QACX,CAAC;AAAA,MACL;AAEA,UAAI,QAAQ,MAAM;AACd,YAAI,oBAAoB;AACpB,iBAAO,WAAW,CAAC,mBAAmB,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,MAAM,IAAI;AAAA,QACtG;AAEA,cAAM;AAAA,MACV;AAEA,UAAI,sBAAsB,GAAG,KAAK,MAAM,SAAS,GAAG,GAAG;AACnD,YAAI,SAAS;AACT,cAAI,WAAW,mBAAmB,SAAS,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,MAAM;AACnG,iBAAO,CAAC,UAAU,QAAQ,IAAI,MAAM,UAAU,QAAQ,KAAK,SAAS,SAAS,SAAS,SAAS,MAAM,CAAC,CAAC;AAAA,QAC3G;AACA,eAAO,CAAC,UAAU,MAAM,IAAI,MAAM,UAAU,OAAO,GAAG,CAAC,CAAC;AAAA,MAC5D;AAEA,UAAI,SAAS,CAAC;AAEd,UAAI,OAAO,QAAQ,aAAa;AAC5B,eAAO;AAAA,MACX;AAEA,UAAI;AACJ,UAAI,wBAAwB,WAAW,QAAQ,GAAG,GAAG;AAEjD,YAAI,oBAAoB,SAAS;AAC7B,gBAAM,MAAM,SAAS,KAAK,OAAO;AAAA,QACrC;AACA,kBAAU,CAAC,EAAE,OAAO,IAAI,SAAS,IAAI,IAAI,KAAK,GAAG,KAAK,OAAO,OAAe,CAAC;AAAA,MACjF,WAAW,QAAQ,MAAM,GAAG;AACxB,kBAAU;AAAA,MACd,OAAO;AACH,YAAI,OAAO,OAAO,KAAK,GAAG;AAC1B,kBAAU,OAAO,KAAK,KAAK,IAAI,IAAI;AAAA,MACvC;AAEA,UAAI,gBAAgB,kBAAkB,OAAO,MAAM,EAAE,QAAQ,OAAO,KAAK,IAAI,OAAO,MAAM;AAE1F,UAAI,iBAAiB,kBAAkB,QAAQ,GAAG,KAAK,IAAI,WAAW,IAAI,gBAAgB,OAAO;AAEjG,UAAI,oBAAoB,QAAQ,GAAG,KAAK,IAAI,WAAW,GAAG;AACtD,eAAO,iBAAiB;AAAA,MAC5B;AAEA,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,YAAI,MAAM,QAAQ,CAAC;AACnB,YAAI,QAAQ,OAAO,QAAQ,YAAY,OAAO,OAAO,IAAI,UAAU,cAC7D,IAAI,QACJ,IAAI,GAAG;AAEb,YAAI,aAAa,UAAU,MAAM;AAC7B;AAAA,QACJ;AAEA,YAAI,aAAa,aAAa,kBAAkB,OAAO,GAAG,EAAE,QAAQ,OAAO,KAAK,IAAI,OAAO,GAAG;AAC9F,YAAI,YAAY,QAAQ,GAAG,IACrB,OAAO,wBAAwB,aAAa,oBAAoB,gBAAgB,UAAU,IAAI,iBAC9F,kBAAkB,YAAY,MAAM,aAAa,MAAM,aAAa;AAE1E,oBAAY,IAAI,QAAQ,IAAI;AAC5B,YAAI,mBAAmB,eAAe;AACtC,yBAAiB,IAAI,UAAU,WAAW;AAC1C,oBAAY,QAAQD;AAAA,UAChB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,wBAAwB,WAAW,oBAAoB,QAAQ,GAAG,IAAI,OAAO;AAAA,UAC7E;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,4BAA4B,SAASE,2BAA0B,MAAM;AACrE,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,KAAK,qBAAqB,eAAe,OAAO,KAAK,qBAAqB,WAAW;AAC5F,cAAM,IAAI,UAAU,wEAAwE;AAAA,MAChG;AAEA,UAAI,OAAO,KAAK,oBAAoB,eAAe,OAAO,KAAK,oBAAoB,WAAW;AAC1F,cAAM,IAAI,UAAU,uEAAuE;AAAA,MAC/F;AAEA,UAAI,KAAK,YAAY,QAAQ,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,YAAY,YAAY;AACpG,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACvD;AAEA,UAAI,UAAU,KAAK,WAAW,SAAS;AACvC,UAAI,OAAO,KAAK,YAAY,eAAe,KAAK,YAAY,WAAW,KAAK,YAAY,cAAc;AAClG,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AAEA,UAAI,SAAS,QAAQ,SAAS;AAC9B,UAAI,OAAO,KAAK,WAAW,aAAa;AACpC,YAAI,CAAC,IAAI,KAAK,QAAQ,YAAY,KAAK,MAAM,GAAG;AAC5C,gBAAM,IAAI,UAAU,iCAAiC;AAAA,QACzD;AACA,iBAAS,KAAK;AAAA,MAClB;AACA,UAAI,YAAY,QAAQ,WAAW,MAAM;AAEzC,UAAI,SAAS,SAAS;AACtB,UAAI,OAAO,KAAK,WAAW,cAAc,QAAQ,KAAK,MAAM,GAAG;AAC3D,iBAAS,KAAK;AAAA,MAClB;AAEA,UAAI;AACJ,UAAI,KAAK,eAAe,uBAAuB;AAC3C,sBAAc,KAAK;AAAA,MACvB,WAAW,aAAa,MAAM;AAC1B,sBAAc,KAAK,UAAU,YAAY;AAAA,MAC7C,OAAO;AACH,sBAAc,SAAS;AAAA,MAC3B;AAEA,UAAI,oBAAoB,QAAQ,OAAO,KAAK,mBAAmB,WAAW;AACtE,cAAM,IAAI,UAAU,+CAA+C;AAAA,MACvE;AAEA,UAAI,YAAY,OAAO,KAAK,cAAc,cAAc,KAAK,oBAAoB,OAAO,OAAO,SAAS,YAAY,CAAC,CAAC,KAAK;AAE3H,aAAO;AAAA,QACH,gBAAgB,OAAO,KAAK,mBAAmB,YAAY,KAAK,iBAAiB,SAAS;AAAA,QAC1F;AAAA,QACA,kBAAkB,OAAO,KAAK,qBAAqB,YAAY,CAAC,CAAC,KAAK,mBAAmB,SAAS;AAAA,QAClG;AAAA,QACA;AAAA,QACA,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,gBAAgB,CAAC,CAAC,KAAK;AAAA,QACvB,WAAW,OAAO,KAAK,cAAc,cAAc,SAAS,YAAY,KAAK;AAAA,QAC7E,QAAQ,OAAO,KAAK,WAAW,YAAY,KAAK,SAAS,SAAS;AAAA,QAClE,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,SAAS,OAAO,KAAK,YAAY,aAAa,KAAK,UAAU,SAAS;AAAA,QACtE,kBAAkB,OAAO,KAAK,qBAAqB,YAAY,KAAK,mBAAmB,SAAS;AAAA,QAChG;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe,OAAO,KAAK,kBAAkB,aAAa,KAAK,gBAAgB,SAAS;AAAA,QACxF,WAAW,OAAO,KAAK,cAAc,YAAY,KAAK,YAAY,SAAS;AAAA,QAC3E,MAAM,OAAO,KAAK,SAAS,aAAa,KAAK,OAAO;AAAA,QACpD,oBAAoB,OAAO,KAAK,uBAAuB,YAAY,KAAK,qBAAqB,SAAS;AAAA,MAC1G;AAAA,IACJ;AAEA,WAAO,UAAU,SAAU,QAAQ,MAAM;AACrC,UAAI,MAAM;AACV,UAAI,UAAU,0BAA0B,IAAI;AAE5C,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,QAAQ,WAAW,YAAY;AACtC,iBAAS,QAAQ;AACjB,cAAM,OAAO,IAAI,GAAG;AAAA,MACxB,WAAW,QAAQ,QAAQ,MAAM,GAAG;AAChC,iBAAS,QAAQ;AACjB,kBAAU;AAAA,MACd;AAEA,UAAI,OAAO,CAAC;AAEZ,UAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,eAAO;AAAA,MACX;AAEA,UAAI,sBAAsB,sBAAsB,QAAQ,WAAW;AACnE,UAAI,iBAAiB,wBAAwB,WAAW,QAAQ;AAEhE,UAAI,CAAC,SAAS;AACV,kBAAU,OAAO,KAAK,GAAG;AAAA,MAC7B;AAEA,UAAI,QAAQ,MAAM;AACd,gBAAQ,KAAK,QAAQ,IAAI;AAAA,MAC7B;AAEA,UAAI,cAAc,eAAe;AACjC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACrC,YAAI,MAAM,QAAQ,CAAC;AACnB,YAAI,QAAQ,IAAI,GAAG;AAEnB,YAAI,QAAQ,aAAa,UAAU,MAAM;AACrC;AAAA,QACJ;AACA,oBAAY,MAAM;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ,SAAS,QAAQ,UAAU;AAAA,UACnC,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,UAAI,SAAS,KAAK,KAAK,QAAQ,SAAS;AACxC,UAAI,SAAS,QAAQ,mBAAmB,OAAO,MAAM;AAErD,UAAI,QAAQ,iBAAiB;AACzB,YAAI,QAAQ,YAAY,cAAc;AAElC,oBAAU;AAAA,QACd,OAAO;AAEH,oBAAU;AAAA,QACd;AAAA,MACJ;AAEA,aAAO,OAAO,SAAS,IAAI,SAAS,SAAS;AAAA,IACjD;AAAA;AAAA;;;ACnWA;AAAA;AAAA;AAEA,QAAI,QAAQ;AAEZ,QAAI,MAAM,OAAO,UAAU;AAC3B,QAAI,UAAU,MAAM;AAEpB,QAAI,WAAW;AAAA,MACX,WAAW;AAAA,MACX,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,SAAS,MAAM;AAAA,MACf,WAAW;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,cAAc;AAAA,MACd,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,sBAAsB;AAAA,IAC1B;AAEA,QAAI,2BAA2B,SAAU,KAAK;AAC1C,aAAO,IAAI,QAAQ,aAAa,SAAU,IAAI,WAAW;AACrD,eAAO,OAAO,aAAa,SAAS,WAAW,EAAE,CAAC;AAAA,MACtD,CAAC;AAAA,IACL;AAEA,QAAI,kBAAkB,SAAU,KAAK,SAAS,oBAAoB;AAC9D,UAAI,OAAO,OAAO,QAAQ,YAAY,QAAQ,SAAS,IAAI,QAAQ,GAAG,IAAI,IAAI;AAC1E,eAAO,IAAI,MAAM,GAAG;AAAA,MACxB;AAEA,UAAI,QAAQ,wBAAwB,sBAAsB,QAAQ,YAAY;AAC1E,cAAM,IAAI,WAAW,gCAAgC,QAAQ,aAAa,cAAc,QAAQ,eAAe,IAAI,KAAK,OAAO,uBAAuB;AAAA,MAC1J;AAEA,aAAO;AAAA,IACX;AAOA,QAAI,cAAc;AAGlB,QAAI,kBAAkB;AAEtB,QAAI,cAAc,SAAS,uBAAuB,KAAK,SAAS;AAC5D,UAAI,MAAM,EAAE,WAAW,KAAK;AAE5B,UAAI,WAAW,QAAQ,oBAAoB,IAAI,QAAQ,OAAO,EAAE,IAAI;AACpE,iBAAW,SAAS,QAAQ,SAAS,GAAG,EAAE,QAAQ,SAAS,GAAG;AAE9D,UAAI,QAAQ,QAAQ,mBAAmB,WAAW,SAAY,QAAQ;AACtE,UAAI,QAAQ,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR,QAAQ,uBAAuB,QAAQ,IAAI;AAAA,MAC/C;AAEA,UAAI,QAAQ,wBAAwB,MAAM,SAAS,OAAO;AACtD,cAAM,IAAI,WAAW,oCAAoC,QAAQ,gBAAgB,UAAU,IAAI,KAAK,OAAO,WAAW;AAAA,MAC1H;AAEA,UAAI,YAAY;AAChB,UAAI;AAEJ,UAAI,UAAU,QAAQ;AACtB,UAAI,QAAQ,iBAAiB;AACzB,aAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AAC/B,cAAI,MAAM,CAAC,EAAE,QAAQ,OAAO,MAAM,GAAG;AACjC,gBAAI,MAAM,CAAC,MAAM,iBAAiB;AAC9B,wBAAU;AAAA,YACd,WAAW,MAAM,CAAC,MAAM,aAAa;AACjC,wBAAU;AAAA,YACd;AACA,wBAAY;AACZ,gBAAI,MAAM;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAEA,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AAC/B,YAAI,MAAM,WAAW;AACjB;AAAA,QACJ;AACA,YAAI,OAAO,MAAM,CAAC;AAElB,YAAI,mBAAmB,KAAK,QAAQ,IAAI;AACxC,YAAI,MAAM,qBAAqB,KAAK,KAAK,QAAQ,GAAG,IAAI,mBAAmB;AAE3E,YAAI;AACJ,YAAI;AACJ,YAAI,QAAQ,IAAI;AACZ,gBAAM,QAAQ,QAAQ,MAAM,SAAS,SAAS,SAAS,KAAK;AAC5D,gBAAM,QAAQ,qBAAqB,OAAO;AAAA,QAC9C,OAAO;AACH,gBAAM,QAAQ,QAAQ,KAAK,MAAM,GAAG,GAAG,GAAG,SAAS,SAAS,SAAS,KAAK;AAE1E,gBAAM,MAAM;AAAA,YACR;AAAA,cACI,KAAK,MAAM,MAAM,CAAC;AAAA,cAClB;AAAA,cACA,QAAQ,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,SAAS;AAAA,YAC1C;AAAA,YACA,SAAU,YAAY;AAClB,qBAAO,QAAQ,QAAQ,YAAY,SAAS,SAAS,SAAS,OAAO;AAAA,YACzE;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,OAAO,QAAQ,4BAA4B,YAAY,cAAc;AACrE,gBAAM,yBAAyB,OAAO,GAAG,CAAC;AAAA,QAC9C;AAEA,YAAI,KAAK,QAAQ,KAAK,IAAI,IAAI;AAC1B,gBAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,IAAI;AAAA,QACjC;AAEA,YAAI,WAAW,IAAI,KAAK,KAAK,GAAG;AAChC,YAAI,YAAY,QAAQ,eAAe,WAAW;AAC9C,cAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,GAAG,GAAG;AAAA,QAC1C,WAAW,CAAC,YAAY,QAAQ,eAAe,QAAQ;AACnD,cAAI,GAAG,IAAI;AAAA,QACf;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,cAAc,SAAU,OAAO,KAAK,SAAS,cAAc;AAC3D,UAAI,qBAAqB;AACzB,UAAI,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,CAAC,MAAM,MAAM;AACtD,YAAI,YAAY,MAAM,MAAM,GAAG,EAAE,EAAE,KAAK,EAAE;AAC1C,6BAAqB,MAAM,QAAQ,GAAG,KAAK,IAAI,SAAS,IAAI,IAAI,SAAS,EAAE,SAAS;AAAA,MACxF;AAEA,UAAI,OAAO,eAAe,MAAM,gBAAgB,KAAK,SAAS,kBAAkB;AAEhF,eAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACxC,YAAI;AACJ,YAAI,OAAO,MAAM,CAAC;AAElB,YAAI,SAAS,QAAQ,QAAQ,aAAa;AACtC,gBAAM,QAAQ,qBAAqB,SAAS,MAAO,QAAQ,sBAAsB,SAAS,QACpF,CAAC,IACD,MAAM,QAAQ,CAAC,GAAG,IAAI;AAAA,QAChC,OAAO;AACH,gBAAM,QAAQ,eAAe,EAAE,WAAW,KAAK,IAAI,CAAC;AACpD,cAAI,YAAY,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM,KAAK,MAAM,GAAG,EAAE,IAAI;AACrG,cAAI,cAAc,QAAQ,kBAAkB,UAAU,QAAQ,QAAQ,GAAG,IAAI;AAC7E,cAAI,QAAQ,SAAS,aAAa,EAAE;AACpC,cAAI,CAAC,QAAQ,eAAe,gBAAgB,IAAI;AAC5C,kBAAM,EAAE,GAAG,KAAK;AAAA,UACpB,WACI,CAAC,MAAM,KAAK,KACT,SAAS,eACT,OAAO,KAAK,MAAM,eAClB,SAAS,MACR,QAAQ,eAAe,SAAS,QAAQ,aAC9C;AACE,kBAAM,CAAC;AACP,gBAAI,KAAK,IAAI;AAAA,UACjB,WAAW,gBAAgB,aAAa;AACpC,gBAAI,WAAW,IAAI;AAAA,UACvB;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,YAAY,SAAS,qBAAqB,UAAU,KAAK,SAAS,cAAc;AAChF,UAAI,CAAC,UAAU;AACX;AAAA,MACJ;AAGA,UAAI,MAAM,QAAQ,YAAY,SAAS,QAAQ,eAAe,MAAM,IAAI;AAIxE,UAAI,WAAW;AACf,UAAI,QAAQ;AAIZ,UAAI,UAAU,QAAQ,QAAQ,KAAK,SAAS,KAAK,GAAG;AACpD,UAAI,SAAS,UAAU,IAAI,MAAM,GAAG,QAAQ,KAAK,IAAI;AAIrD,UAAI,OAAO,CAAC;AACZ,UAAI,QAAQ;AAER,YAAI,CAAC,QAAQ,gBAAgB,IAAI,KAAK,OAAO,WAAW,MAAM,GAAG;AAC7D,cAAI,CAAC,QAAQ,iBAAiB;AAC1B;AAAA,UACJ;AAAA,QACJ;AAEA,aAAK,KAAK,MAAM;AAAA,MACpB;AAIA,UAAI,IAAI;AACR,aAAO,QAAQ,QAAQ,MAAM,UAAU,MAAM,KAAK,GAAG,OAAO,QAAQ,IAAI,QAAQ,OAAO;AACnF,aAAK;AACL,YAAI,CAAC,QAAQ,gBAAgB,IAAI,KAAK,OAAO,WAAW,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,GAAG;AAC9E,cAAI,CAAC,QAAQ,iBAAiB;AAC1B;AAAA,UACJ;AAAA,QACJ;AACA,aAAK,KAAK,QAAQ,CAAC,CAAC;AAAA,MACxB;AAIA,UAAI,SAAS;AACT,YAAI,QAAQ,gBAAgB,MAAM;AAC9B,gBAAM,IAAI,WAAW,0CAA0C,QAAQ,QAAQ,0BAA0B;AAAA,QAC7G;AACA,aAAK,KAAK,MAAM,IAAI,MAAM,QAAQ,KAAK,IAAI,GAAG;AAAA,MAClD;AAEA,aAAO,YAAY,MAAM,KAAK,SAAS,YAAY;AAAA,IACvD;AAEA,QAAI,wBAAwB,SAASC,uBAAsB,MAAM;AAC7D,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,KAAK,qBAAqB,eAAe,OAAO,KAAK,qBAAqB,WAAW;AAC5F,cAAM,IAAI,UAAU,wEAAwE;AAAA,MAChG;AAEA,UAAI,OAAO,KAAK,oBAAoB,eAAe,OAAO,KAAK,oBAAoB,WAAW;AAC1F,cAAM,IAAI,UAAU,uEAAuE;AAAA,MAC/F;AAEA,UAAI,KAAK,YAAY,QAAQ,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,YAAY,YAAY;AACpG,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACvD;AAEA,UAAI,OAAO,KAAK,YAAY,eAAe,KAAK,YAAY,WAAW,KAAK,YAAY,cAAc;AAClG,cAAM,IAAI,UAAU,mEAAmE;AAAA,MAC3F;AAEA,UAAI,OAAO,KAAK,yBAAyB,eAAe,OAAO,KAAK,yBAAyB,WAAW;AACpG,cAAM,IAAI,UAAU,iDAAiD;AAAA,MACzE;AAEA,UAAI,UAAU,OAAO,KAAK,YAAY,cAAc,SAAS,UAAU,KAAK;AAE5E,UAAI,aAAa,OAAO,KAAK,eAAe,cAAc,SAAS,aAAa,KAAK;AAErF,UAAI,eAAe,aAAa,eAAe,WAAW,eAAe,QAAQ;AAC7E,cAAM,IAAI,UAAU,8DAA8D;AAAA,MACtF;AAEA,UAAI,YAAY,OAAO,KAAK,cAAc,cAAc,KAAK,oBAAoB,OAAO,OAAO,SAAS,YAAY,CAAC,CAAC,KAAK;AAE3H,aAAO;AAAA,QACH;AAAA,QACA,kBAAkB,OAAO,KAAK,qBAAqB,YAAY,CAAC,CAAC,KAAK,mBAAmB,SAAS;AAAA,QAClG,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,aAAa,OAAO,KAAK,gBAAgB,YAAY,KAAK,cAAc,SAAS;AAAA,QACjF,YAAY,OAAO,KAAK,eAAe,WAAW,KAAK,aAAa,SAAS;AAAA,QAC7E;AAAA,QACA,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,OAAO,OAAO,KAAK,UAAU,YAAY,KAAK,QAAQ,SAAS;AAAA,QAC/D,iBAAiB,OAAO,KAAK,oBAAoB,YAAY,KAAK,kBAAkB,SAAS;AAAA,QAC7F,SAAS,OAAO,KAAK,YAAY,aAAa,KAAK,UAAU,SAAS;AAAA,QACtE,WAAW,OAAO,KAAK,cAAc,YAAY,MAAM,SAAS,KAAK,SAAS,IAAI,KAAK,YAAY,SAAS;AAAA;AAAA,QAE5G,OAAQ,OAAO,KAAK,UAAU,YAAY,KAAK,UAAU,QAAS,CAAC,KAAK,QAAQ,SAAS;AAAA,QACzF;AAAA,QACA,mBAAmB,KAAK,sBAAsB;AAAA,QAC9C,0BAA0B,OAAO,KAAK,6BAA6B,YAAY,KAAK,2BAA2B,SAAS;AAAA,QACxH,gBAAgB,OAAO,KAAK,mBAAmB,WAAW,KAAK,iBAAiB,SAAS;AAAA,QACzF,aAAa,KAAK,gBAAgB;AAAA,QAClC,cAAc,OAAO,KAAK,iBAAiB,YAAY,KAAK,eAAe,SAAS;AAAA,QACpF,aAAa,OAAO,KAAK,gBAAgB,YAAY,CAAC,CAAC,KAAK,cAAc,SAAS;AAAA,QACnF,oBAAoB,OAAO,KAAK,uBAAuB,YAAY,KAAK,qBAAqB,SAAS;AAAA,QACtG,sBAAsB,OAAO,KAAK,yBAAyB,YAAY,KAAK,uBAAuB;AAAA,MACvG;AAAA,IACJ;AAEA,WAAO,UAAU,SAAU,KAAK,MAAM;AAClC,UAAI,UAAU,sBAAsB,IAAI;AAExC,UAAI,QAAQ,MAAM,QAAQ,QAAQ,OAAO,QAAQ,aAAa;AAC1D,eAAO,QAAQ,eAAe,EAAE,WAAW,KAAK,IAAI,CAAC;AAAA,MACzD;AAEA,UAAI,UAAU,OAAO,QAAQ,WAAW,YAAY,KAAK,OAAO,IAAI;AACpE,UAAI,MAAM,QAAQ,eAAe,EAAE,WAAW,KAAK,IAAI,CAAC;AAIxD,UAAI,OAAO,OAAO,KAAK,OAAO;AAC9B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,YAAI,MAAM,KAAK,CAAC;AAChB,YAAI,SAAS,UAAU,KAAK,QAAQ,GAAG,GAAG,SAAS,OAAO,QAAQ,QAAQ;AAC1E,cAAM,MAAM,MAAM,KAAK,QAAQ,OAAO;AAAA,MAC1C;AAEA,UAAI,QAAQ,gBAAgB,MAAM;AAC9B,eAAO;AAAA,MACX;AAEA,aAAO,MAAM,QAAQ,GAAG;AAAA,IAC5B;AAAA;AAAA;;;ACvUA;AAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,QAAQ;AACZ,QAAI,UAAU;AAEd,WAAO,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA;AAAA;;;ACVO,SAAS,kBAAkB;AAC9B,QAAM,SAAS,OAAO,SAAS;AAC/B,QAAM,OAAO,OAAO,SAAS,KAAK,UAAU,CAAC;AAE7C,SAAO,UAAU,WAAW,KACtB,SACA,KAAK,SAAS,GAAG,IACjB,KAAK,MAAM,GAAG,EAAE,CAAC,IACjB;AACV;;;ACLO,IAAM,uBAAuB,CAChC,QACA,EAAE,aAAa,gBAAgB,WAAW,MACnB;AACvB,QAAM,eAAqC;AAAA,IACvC,MAAM,MAAM,QAAQ;AAChB,YAAM,sBAAsB;AAC5B,UAAI,oBAAoB,SAAS,oBAAoB,UAAU;AAC3D,cAAM,EAAE,MAAM,IAAI,MAAM,OAAO,KAAK;AAAA,UAChC;AAAA,QACJ;AAEA,YAAI,OAAO;AACP,gBAAM;AAAA,QACV;AAEA;AAAA,MACJ;AAEA,YAAM,cAAc;AACpB,UAAI,YAAY,UAAU;AACtB,eAAO,KAAK,gBAAgB;AAAA,UACxB,GAAG;AAAA,UACH,SAAS,EAAE,WAAW;AAAA,QAC1B,CAAC;AAGD,eAAO,QAAQ,OAAO;AAAA,MAC1B;AACA,aAAO,QAAQ,OAAO,IAAI,MAAM,0BAA0B,CAAC;AAAA,IAC/D;AAAA,IACA,MAAM,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,IACJ,GAAsB;AAClB,YAAM,EAAE,OAAO,aAAa,IAAI,MAAM,OAAO,KAAK,WAAW;AAAA,QACzD;AAAA,QACA;AAAA,MACJ,CAAC;AAED,UAAI,cAAc;AACd,cAAM;AAAA,MACV;AACA,YAAM,EAAE,MAAM,IAAI,MAAM,OAAO,KAAK,WAAW;AAAA,QAC3C;AAAA,MACJ,CAAC;AAED,UAAI,OAAO;AACP,cAAM;AAAA,MACV;AACA,aAAO;AAAA,IACX;AAAA,IACA,MAAM,cAAc,QAA6B;AAC7C,YAAM,EAAE,OAAO,GAAG,QAAQ,IAAI;AAE9B,YAAM,EAAE,MAAM,IAAI,MAAM,OAAO,KAAK;AAAA,QAChC;AAAA,QACA;AAAA,MACJ;AAEA,UAAI,OAAO;AACP,cAAM;AAAA,MACV;AACA,aAAO;AAAA,IACX;AAAA,IACA,MAAM,SAAS;AACX,YAAM,EAAE,MAAM,IAAI,MAAM,OAAO,KAAK,QAAQ;AAC5C,UAAI,OAAO;AACP,cAAM;AAAA,MACV;AAAA,IACJ;AAAA,IACA,MAAM,WAAW,OAAO;AACpB,UACI,MAAM,WAAW,OACjB,MAAM,WAAW;AAAA,MAEhB,MAAM,WAAW,OACd,MAAM,SAAS,2BACrB;AACE,eAAO,QAAQ,OAAO;AAAA,MAC1B;AAEA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AAAA,IACA,MAAM,iBAAiB;AACnB,YAAM,EAAE,cAAc,eAAe,KAAK,IAAI,aAAa;AAG3D,UAAI,SAAS,cAAc,SAAS,UAAU;AAC1C,YAAI,gBAAgB,eAAe;AAC/B,iBAAO;AAAA,YACH,YAAY,OAAO;AAAA,cACf,UAAU,aACJ,GAAG,UAAU,kBACb;AAAA,cACN,QAAQ,gBAAgB,YAAY,kBAAkB,aAAa,SAAS,IAAI;AAAA,YACpF;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,MAAwC;AACxC,kBAAQ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,MAAM,YAAY;AAEd,UACI,OAAO,SAAS,aAAa,mBAC7B,OAAO,SAAS,KAAK,SAAS,gBAAgB,GAChD;AACE;AAAA,MACJ;AAEA,UACI,OAAO,SAAS,aAAa,sBAC7B,OAAO,SAAS,KAAK,SAAS,mBAAmB,GACnD;AACE;AAAA,MACJ;AAEA,YAAM,EAAE,cAAc,eAAe,KAAK,IAAI,aAAa;AAE3D,UAAI,SAAS,cAAc,SAAS,UAAU;AAC1C,YAAI,gBAAgB,eAAe;AAE/B,gBAAM;AAAA,YACF,YAAY,OAAO;AAAA,cACf,UAAU,aACJ,GAAG,UAAU,kBACb;AAAA,cACN,QAAQ,gBAAgB,YAAY,kBAAkB,aAAa,SAAS,IAAI;AAAA,YACpF;AAAA,YACA,SAAS;AAAA,UACb;AAAA,QACJ;AAEA,YAAI,MAAwC;AACxC,kBAAQ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,YAAM,EAAE,KAAK,IAAI,MAAM,OAAO,KAAK,WAAW;AAC9C,UAAI,KAAK,WAAW,MAAM;AACtB,eAAO,QAAQ,OAAO;AAAA,MAC1B;AAEA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AAAA,IACA,MAAM,iBAAiB;AACnB,UAAI,OAAO,mBAAmB,YAAY;AACtC;AAAA,MACJ;AAGA,UACI,OAAO,SAAS,aAAa,mBAC7B,OAAO,SAAS,KAAK,SAAS,gBAAgB,KAC9C,OAAO,SAAS,aAAa,sBAC7B,OAAO,SAAS,KAAK,SAAS,mBAAmB,GACnD;AACE;AAAA,MACJ;AAEA,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK,QAAQ;AAClD,UAAI,SAAS,KAAK,QAAQ,MAAM;AAC5B;AAAA,MACJ;AAEA,YAAM,cAAc,MAAM,eAAe,KAAK,IAAI;AAClD,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,MAAI,OAAO,gBAAgB,YAAY;AACnC,iBAAa,cAAc,YAAY;AACnC,YAAM,EAAE,KAAK,IAAI,MAAM,OAAO,KAAK,QAAQ;AAC3C,UAAI,KAAK,QAAQ,MAAM;AACnB,cAAM,IAAI,MAAM;AAAA,MACpB;AAEA,YAAM,WAAW,MAAM,YAAY,KAAK,IAAI;AAC5C,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,SAAO;AACX;AA8CA,IAAM,eAAe,MAAM;AACvB,QAAM,YAAY,gBAAgB;AAClC,QAAM,kBAAkB,IAAI,gBAAgB,SAAS;AACrD,QAAM,eAAe,gBAAgB,IAAI,cAAc;AACvD,QAAM,gBAAgB,gBAAgB,IAAI,eAAe;AACzD,QAAM,OAAO,gBAAgB,IAAI,MAAM;AAEvC,SAAO,EAAE,cAAc,eAAe,KAAK;AAC/C;;;AC1PA,IAAI,WAAsC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,UAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AACA,IAAI,SAAkC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAkCA,IAAI,WAAW,SAAU,KAAK;AAC1B,SAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG,KAAK,QAAQ;AACrE;AACA,IAAI,cAAc,SAAU,QAAQ,MAAM;AACtC,MAAI,SAAS,OAAO,KAAK,CAAC,CAAC;AAC3B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAClC,aAAS,OAAO,KAAK,CAAC,CAAC;AAAA,EAC3B;AACA,SAAO;AACX;AACO,IAAI,eAAe,SAAU,QAAQ,eAAe;AACvD,MAAI,SAAS,OAAO,QAAQ,KAAK,OAAO,MAAM,OAAO,OAAO,SAAS,CAAC,IAAI;AAC1E,MAAI,SAAS,CAAC;AACd,SAAO,SAAS,CAAC;AACjB,SAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AAEvC,QAAI,WAAW,CAAC,GAAG;AACnB,QAAI;AAIJ,QAAI,IAAI,MAAM,GAAG,EAAE,CAAC,MAAM,MAAM,SAAS,OAAO,GAAG,CAAC,GAAG;AACnD,UAAI,WAAW,OAAO,GAAG;AACzB,SAAG;AACC,YAAI,QAAQ,YAAY,QAAQ,QAAQ;AACxC,YAAI,WAAW,OAAO,KAAK,KAAK,EAAE,CAAC;AACnC,iBAAS,KAAK,QAAQ;AACtB,mBAAW,MAAM,QAAQ;AAAA,MAC7B,SAAS,SAAS,QAAQ;AAC1B,YAAM,SAAS,KAAK,GAAG;AACvB,eAAS,CAAC,QAAQ;AAAA,IACtB,OACK;AACD,eAAS,CAAC,OAAO,GAAG,CAAC;AAAA,IACzB;AACA,QAAI,WAAW,IAAI,MAAM,GAAG;AAC5B,QAAI,YAAY,SAAS,UAAU,IAC7B,SAAS,CAAC,IACV;AACN,QAAI,CAAC,QAAQ,OAAO,EAAE,SAAS,SAAS,GAAG;AAEvC,eAAS,YAAY,QAAQ,QAAQ,EAAE,KAAK,EAAE,MAAM,KAAK;AAAA,IAC7D,WAES,CAAC,MAAM,KAAK,EAAE,SAAS,SAAS,GAAG;AAExC,UAAI,YAAY,aAAa,EAAE,QAAQ,YAAY,QAAQ,QAAQ,EAAE,GAAG,aAAa,EAAE;AAEvF,UAAI,sBAAsB,CAAC;AAC3B,aAAO,QAAQ,SAAS,EAAE,QAAQ,SAAUC,KAAI;AAC5C,YAAI,KAAKA,IAAG,CAAC,GAAG,MAAMA,IAAG,CAAC;AAC1B,YAAI,MAAM,QAAQ,GAAG;AACjB,8BAAoB,KAAK,MAAM,qBAAqB,IAAI,IAAI,SAAU,GAAG;AAAE,mBAAO,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG;AAAA,UAAG,CAAC,CAAC;AAAA;AAEvG,8BAAoB,KAAK,CAAC,IAAI,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,MACpD,CAAC;AAED,eAAS,CAAC,IAAI,OAAO,oBAAoB,KAAK,GAAG,GAAG,GAAG,CAAC;AAAA,IAC5D;AACA,WAAO,QAAQ,SAAU,OAAO;AAC5B,UAAI,KAAM,WAAY;AAElB,YAAI,UAAU,WAAW;AACrB,iBAAO,GAAG,OAAO,KAAK;AAC1B,YAAI,UAAU,SAAS,MAAM;AACzB,iBAAO,GAAG,OAAO,WAAW,IAAI,EAAE,OAAO,OAAO,GAAG;AACvD,YAAI,CAAC,OAAO,IAAI,EAAE,SAAS,SAAS;AAChC,iBAAO,GAAG,OAAO,KAAK;AAC1B,eAAO,GAAG,OAAO,WAAW,GAAG,EAAE,OAAO,KAAK;AAAA,MACjD,EAAG;AAEH,UAAI,OAAO,OAAO,SAAS,CAAC,CAAC,MAAM,QAAW;AAE1C,YAAI,CAAC,OAAO,IAAI,EAAE,SAAS,SAAS,GAAG;AACnC,iBAAO,OAAO,SAAS,IAAI;AAAA,QAC/B,OACK;AAED,iBAAO,OAAO,SAAS,CAAC,CAAC,IAAI;AAAA,QACjC;AAAA,MACJ,OACK;AACD,YAAI,CAAC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG;AAErC,iBAAO,OAAO,SAAS,CAAC,CAAC,IAAI;AAAA,YACzB,OAAO,OAAO,SAAS,CAAC,CAAC;AAAA,YACzB;AAAA,UACJ;AAAA,QACJ,OACK;AAED,iBAAO,OAAO,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE;AAAA,QACtC;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACD,MAAI,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,SAAS;AAC1D,WAAO,SAAS,MAAM,QAAQ,KAAK,OAAO,IACpC,KAAK,QAAQ,KAAK,GAAG,IACrB,KAAK;AAAA,EACf;AACA,SAAO;AACX;AACO,IAAI,gBAAgB,SAAU,UAAU,aAAa;AACxD,SAAQ,eAAe,YAAY,IAAI,QAAQ,KAAM,CAAC,IAAI;AAC9D;AACO,IAAI,WAAW,SAAU,IAAI,YAAY;AAC5C,MAAI,cAAc,UAAU,GAAG;AAC3B,WAAO,KAAK,MAAM,GAAG,SAAS,CAAC;AAAA,EACnC,OACK;AACD,WAAO,CAAC,GAAG,SAAS,CAAC;AAAA,EACzB;AACJ;AACO,IAAI,WAAW,SAAU,MAAM,YAAY;AAC9C,MAAI,cAAc,UAAU,GAAG;AAC3B,WAAO,KAAK,UAAU,WAAW,IAAI,SAAU,KAAK;AAAE,aAAO,KAAK,GAAG;AAAA,IAAG,CAAC,CAAC;AAAA,EAC9E,OACK;AACD,WAAO,KAAK,WAAW,CAAC,CAAC;AAAA,EAC7B;AACJ;AACO,IAAI,mBAAmB,SAAU,MAAM,YAAY;AACtD,MAAI,UAAU,SAAS,CAAC,GAAG,IAAI;AAC/B,aAAW,QAAQ,SAAU,KAAK;AAC9B,WAAO,QAAQ,GAAG;AAAA,EACtB,CAAC;AACD,SAAO;AACX;AACO,IAAI,oBAAoB,SAAU,MAAM,YAAY;AACvD,MAAI,WAAW,WAAW,KAAK,WAAW,CAAC,MAAM,MAAM;AACnD,WAAO;AAAA,EACX;AACA,SAAO,OAAO,OAAO,MAAM;AAAA,IACvB,IAAI,SAAS,MAAM,UAAU;AAAA,EACjC,CAAC;AACL;AACO,IAAI,uBAAuB,SAAU,MAAM,YAAY;AAC1D,MAAI,WAAW,WAAW,KAAK,WAAW,CAAC,MAAM,MAAM;AACnD,WAAO;AAAA,EACX;AACA,MAAI,KAAK,KAAK,IAAI,gBAAgB,OAAO,MAAM,CAAC,IAAI,CAAC;AACrD,SAAO;AACX;AACA,IAAI,gBAAgB,SAAU,YAAY;AACtC,SAAO,WAAW,SAAS;AAC/B;AACO,IAAI,WAAW,SAAU,YAAY,KAAK,UAAU,MAAM;AAC7D,MAAI,IAAI;AACR,MAAI,SAAS,QAAQ;AAAE,WAAO;AAAA,EAAM;AACpC,MAAI,SAAS,CAAC;AACd,MAAI,MAAM,QAAQ,GAAG,KAAK,IAAI,SAAS,GAAG;AAEtC,QAAI,SAAS,WAAW,MAAM,GAAG;AAC7B,cAAQ,MAAM,wIAAwI;AACtJ;AAAA,IACJ;AACA,QAAI,cAAc,UAAU,GAAG;AAC3B,eAAS;AAAA,QACL,IAAI,IAAI,OAAO,IAAI,IAAI,SAAUC,KAAI;AACjC,cAAI,mBAAmB,SAASA,KAAI,UAAU;AAC9C,iBAAO,OAAO,OAAO,WAChB,IAAI,SAAU,KAAK,GAAG;AAAE,mBAAO,GAAG,OAAO,KAAK,MAAM,EAAE,OAAO,iBAAiB,CAAC,CAAC;AAAA,UAAG,CAAC,EACpF,KAAK,GAAG,GAAG,GAAG;AAAA,QACvB,CAAC,GAAG,GAAG;AAAA,MACX;AAAA,IACJ,OACK;AACD,gBAAU,KAAK,CAAC,GACZ,GAAG,WAAW,CAAC,CAAC,IAAI,OAAO,OAAO,IAAI,KAAK,GAAG,GAAG,GAAG,GACpD;AAAA,IACR;AAAA,EACJ,WACS,KAAK;AAEV,QAAI,KAAK,IAAI,SAAS;AACtB,QAAI,qBAAqB,SAAS,IAAI,UAAU;AAChD,QAAI,cAAc,UAAU,GAAG;AAC3B,UAAI,SAAS,WAAW,MAAM,GAAG;AAC7B,iBAAS,CAAC;AACV,mBAAW,IAAI,SAAU,KAAK,GAAG;AAC7B,iBAAQ,OAAO,GAAG,IAAI,GAAG,OAAO,mBAAmB,CAAC,CAAC;AAAA,QACzD,CAAC;AAAA,MACL,OACK;AACD,iBAAS;AAAA,UACL,KAAK,IAAI,OAAO,WAAW,IAAI,SAAU,KAAK,GAAG;AAC7C,mBAAO,GAAG,OAAO,KAAK,MAAM,EAAE,OAAO,mBAAmB,CAAC,CAAC;AAAA,UAC9D,CAAC,GAAG,GAAG;AAAA,QACX;AAAA,MACJ;AAAA,IACJ,OACK;AACD,gBAAU,KAAK,CAAC,GACZ,GAAG,WAAW,CAAC,CAAC,IAAI,MAAM,OAAO,EAAE,GACnC;AAAA,IACR;AAAA,EACJ;AACA,MAAI,QAAQ,KAAK,SAAS;AACtB,WAAO,SAAS,MAAM,QAAQ,KAAK,OAAO,IACpC,KAAK,QAAQ,KAAK,GAAG,IACrB,KAAK;AAAA,EACf;AACA,SAAO;AACX;AACO,IAAI,aAAa,SAAU,OAAO,OAAO,YAAY,WAAW;AACnE,MAAI,cAAc,QAAQ;AAAE,gBAAY;AAAA,EAA4E;AACpH,MAAI,iBAAiB,UAAU,MAAM,GAAG,EAAE,UAAU,QAAQ,IAAI,CAAC;AACjE,MAAI,SAAS,MAAM;AACf,WAAO,WAAW,IAAI,SAAU,KAAK;AAAE,aAAO,GAAG,OAAO,KAAK,GAAG,EAAE,OAAO,cAAc;AAAA,IAAG,CAAC,EAAE,KAAK,GAAG;AAAA,EACzG,OACK;AACD,WAAO,GAAG,OAAO,OAAO,GAAG,EAAE,OAAO,cAAc;AAAA,EACtD;AACJ;;;ACjQA,gBAAe;AACf,qBAAoB;AAbpB,IAAIC,YAAsC,WAAY;AAClD,EAAAA,YAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,UAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACzC;AAIO,IAAI,qBAAqB,oBAAI,IAAI;AACjC,IAAI,gBAAgB,WAAY;AAAE,SAAO;AAAI;AACpD,IAAI,kBAAkB,SAAU,QAAQ,YAAY,QAAQ;AACxD,MAAI;AACJ,MAAI,mBAAmB;AACvB,MAAI,eAAe,QAAW;AAC1B,uBAAmB,WAAY;AAAE,aAAO;AAAA,IAAY;AAAA,EACxD;AACA,MAAI,iBAAiB,EAAE,SAAS,GAAG;AAC/B,QAAI,eAAe;AACnB,QAAI,CAAC,OAAO,MAAM,EAAE,SAAS,MAAM,GAAG;AAClC,qBAAe;AAAA,IACnB,WACS,CAAC,QAAQ,SAAS,OAAO,QAAQ,EAAE,SAAS,MAAM,GAAG;AAC1D,qBAAe;AAAA,IACnB;AAEI,aAAO,CAAC;AACZ,WAAO,KAAK,CAAC,GAAG,GAAG,YAAY,IAAI,iBAAiB,GAAG;AAAA,EAC3D;AAEI,WAAO,CAAC;AAChB;AACA,IAAO,cAAS,SAAU,QAAQ;AAAE,SAAQ;AAAA,IACxC,SAAS,SAAU,UAAU,QAAQ;AACjC,UAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,KAAK,OAAO,YAAY,OAAO,GAAG,MAAM,UAAU,GAAG;AACzD,UAAI,KAAK,OAAO,QAAQ,CAAC,GAAG,QAAQ,GAAG,OAAO,QAAQ,GAAG;AACzD,UAAI,KAAK,aAAa,QAAQ,OAAO,aAAa,GAAG,SAAS,GAAG,QAAQ,SAAS,GAAG;AACrF,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,UAAI,QAAQA,UAAS,EAAE,QAAQ,QAAQ,OAAO,KAAK,OAAO,GAAG,OAAO,OAAO,OAAO,EAAE,GAAG,MAAM;AAC7F,UAAI,OAAO;AACP,cAAM,QAAQ,WAAW,OAAO,OAAO,YAAY,OAAO,SAAS;AACnE,cAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,eAC5D,CAAC,MAAM,MAAM,SAAS,YAAY,GAAG;AACrC,gBAAM,QAAQ,MAAM,MAAM,SAAS,WAAW,IACxC,MAAM,MAAM,QAAQ,aAAa,YAAY,IAC7C,GAAG,OAAO,MAAM,OAAO,aAAa;AAAA,QAC9C;AACA,cAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,SAC5E,MAAM,MAAM,SAAS,YAAY,GAAG;AACpC,gBAAM,QAAQ,MAAM,MAAM,QAAQ,eAAe,EAAE;AAAA,QACvD;AACA,cAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,CAAC,MAAM,MAAM,SAAS,WAAW,GAAG;AAC9G,gBAAM,QAAQ,MAAM,MAAM,SAAS,YAAY,IACzC,MAAM,MAAM,QAAQ,cAAc,WAAW,IAC7C,GAAG,OAAO,MAAM,OAAO,YAAY;AAAA,QAC7C;AACA,cAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,eAAe,SAC3E,MAAM,MAAM,SAAS,WAAW,GAAG;AACnC,gBAAM,QAAQ,MAAM,MAAM,QAAQ,cAAc,EAAE;AAAA,QACtD;AAAA,MACJ;AACA,UAAI,QAAQ;AACR,cAAM,SAAS;AAAA,MACnB;AAEA,UAAI,UAAU;AAAA,QACV,SAAS,IAAI,QAAQA,UAASA,UAAS,EAAE,QAAQ,oBAAoB,QAAQ,cAAc,KAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,CAAC,CAAE,GAAG,gBAAgB,OAAO,QAAQ,YAAY,KAAK,CAAC,CAAC;AAAA,MACnO;AACA,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,UAAAC,QAAG,UAAU,KAAK,CAAC;AACxF,aAAO,OAAO,WAAW,KAAK,OAAO,EAAE,KAAK,SAAUC,KAAI;AACtD,YAAI,UAAUA,IAAG,SAAS,OAAOA,IAAG;AACpC,YAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AAC/B,gBAAM,IAAI,MAAM,yUAAyU;AAAA,QAC7V;AACA,eAAO;AAAA,UACH,MAAM,KAAK,IAAI,SAAU,KAAK;AAAE,mBAAO,kBAAkB,KAAK,UAAU;AAAA,UAAG,CAAC;AAAA,UAC5E,OAAO,SAAS,QAAQ,IAAI,eAAe,EAAE,MAAM,GAAG,EAAE,IAAI,GAAG,EAAE;AAAA,QACrE;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,QAAQ,SAAU,UAAU,QAAQ;AAChC,UAAI,IAAI;AACR,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,KAAK,OAAO,IAAI,OAAO,OAAO;AAClC,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,QAAQ,SAAS,YAAY,IAAI,UAAU,IAAI;AACnD,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,UAAAD,QAAG,UAAU,KAAK,CAAC;AACxF,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,aAAO,OACF,WAAW,KAAK;AAAA,QACjB,SAAS,IAAI,QAAQD,UAASA,UAAS,EAAE,QAAQ,oCAAoC,KAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,CAAC,CAAE,GAAG,gBAAgB,OAAO,QAAQ,YAAY,KAAK,CAAC,CAAC;AAAA,MAC7N,CAAC,EACI,KAAK,SAAUE,KAAI;AACpB,YAAI,OAAOA,IAAG;AACd,eAAQ;AAAA,UACJ,MAAM,kBAAkB,MAAM,UAAU;AAAA,QAC5C;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,SAAS,SAAU,UAAU,QAAQ;AACjC,UAAI;AACJ,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,MAAM,OAAO;AACjB,UAAI,IAAI,WAAW,GAAG;AAClB,eAAO,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;AAAA,MACvC;AACA,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,QAAQ,SAAS,YAAY,KAAK,UAAU,OAAO,IAAI;AAC3D,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,UAAAD,QAAG,UAAU,KAAK,CAAC;AACxF,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,aAAO,OACF,WAAW,KAAK;AAAA,QACjB,SAAS,IAAI,QAAQD,UAAS,CAAC,GAAG,gBAAgB,OAAO,QAAQ,YAAY,KAAK,CAAC,CAAC;AAAA,MACxF,CAAC,EACI,KAAK,SAAUE,KAAI;AACpB,YAAI,OAAOA,IAAG;AACd,eAAQ;AAAA,UACJ,MAAM,KAAK,IAAI,SAAU,MAAM;AAAE,mBAAO,kBAAkB,MAAM,UAAU;AAAA,UAAG,CAAC;AAAA,QAClF;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,kBAAkB,SAAU,UAAU,QAAQ;AAC1C,UAAI;AACJ,UAAI,IAAI;AACR,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,KAAK,OAAO,YAAY,OAAO,GAAG,MAAM,UAAU,GAAG;AACzD,UAAI,KAAK,OAAO,MAAM,QAAQ,GAAG,OAAO,QAAQ,GAAG;AACnD,UAAI,KAAK,aAAa,QAAQ,OAAO,aAAa,GAAG,SAAS,GAAG,QAAQ,SAAS,GAAG;AACrF,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,UAAI,QAAQ,OAAO,SACbF,WAAU,KAAK,CAAC,GAAG,GAAG,OAAO,MAAM,IAAI,MAAM,OAAO,OAAO,EAAE,GAAG,GAAG,QAAQ,WAAW,OAAO,OAAO,YAAY,OAAO,SAAS,GAAG,GAAG,SAAS,QAAQ,OAAO,KAAK,OAAO,GAAG,GAAG,QAAQ,OAAO,OAAO,GAAG,KAAK,MAAM,IAAIA,UAAS,EAAE,OAAO,WAAW,OAAO,OAAO,UAAU,GAAG,QAAQ,QAAQ,OAAO,KAAK,OAAO,GAAG,OAAO,OAAO,OAAO,EAAE,GAAG,MAAM;AAC5V,UAAI,QAAQ;AACR,cAAM,SAAS;AAAA,MACnB;AAEA,UAAI,UAAU;AAAA,QACV,SAAS,IAAI,QAAQA,UAASA,UAAS,EAAE,QAAQ,oBAAoB,QAAQ,cAAc,KAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,CAAC,CAAE,GAAG,gBAAgB,OAAO,QAAQ,YAAY,KAAK,CAAC,CAAC;AAAA,MACnO;AACA,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,UAAAC,QAAG,UAAU,KAAK,CAAC;AACxF,aAAO,OAAO,WAAW,KAAK,OAAO,EAAE,KAAK,SAAUC,KAAI;AACtD,YAAI,UAAUA,IAAG,SAAS,OAAOA,IAAG;AACpC,YAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AAC/B,gBAAM,IAAI,MAAM,yUAAyU;AAAA,QAC7V;AACA,eAAO;AAAA,UACH,MAAM,KAAK,IAAI,SAAU,MAAM;AAAE,mBAAO,kBAAkB,MAAM,UAAU;AAAA,UAAG,CAAC;AAAA,UAC9E,OAAO,SAAS,QAAQ,IAAI,eAAe,EAAE,MAAM,GAAG,EAAE,IAAI,GAAG,EAAE;AAAA,QACrE;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,QAAQ,SAAU,UAAU,QAAQ;AAChC,UAAI,IAAI;AACR,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,KAAK,OAAO,IAAI,OAAO,OAAO,MAAM,OAAO,OAAO,MAAM,eAAe,OAAO;AAClF,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,QAAQ,SAAS,YAAY,IAAI,UAAU,IAAI;AACnD,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,UAAAD,QAAG,UAAU,KAAK,CAAC;AACxF,UAAI,cAAc,WAAW,MAAM,YAAY;AAC/C,UAAI,OAAO,KAAK,WAAW,EAAE,WAAW,GAAG;AACvC,eAAO,QAAQ,QAAQ,EAAE,MAAMD,UAAS,CAAC,GAAG,YAAY,EAAE,CAAC;AAAA,MAC/D;AACA,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,UAAI,OAAO,KAAK,UAAUA,UAAS,CAAC,GAAG,qBAAqB,iBAAiB,aAAa,UAAU,GAAG,UAAU,CAAC,CAAC;AACnH,aAAO,OACF,WAAW,KAAK;AAAA,QACjB,QAAQ;AAAA,QACR,SAAS,IAAI,QAAQA,UAASA,UAAS,EAAE,QAAQ,qCAAqC,QAAQ,yBAAyB,gBAAgB,mBAAmB,KAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,CAAC,CAAE,GAAG,gBAAgB,OAAO,QAAQ,YAAY,OAAO,CAAC,CAAC;AAAA,QAChS;AAAA,MACJ,CAAC,EACI,KAAK,SAAUE,KAAI;AACpB,YAAI,OAAOA,IAAG;AACd,eAAQ;AAAA,UACJ,MAAM,kBAAkB,MAAM,UAAU;AAAA,QAC5C;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,YAAY,SAAU,UAAU,QAAQ;AACpC,UAAI,IAAI;AACR,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,MAAM,OAAO,KAAK,OAAO,OAAO,MAAM,OAAO,OAAO;AACxD,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,QAAQ,SAAS,YAAY,KAAK,UAAU,IAAI;AACpD,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,UAAAD,QAAG,UAAU,KAAK,CAAC;AACxF,UAAI,OAAO,KAAK,UAAUD,UAAS,CAAC,GAAG,qBAAqB,iBAAiB,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC;AAC5G,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,aAAO,OACF,WAAW,KAAK;AAAA,QACjB,QAAQ;AAAA,QACR,SAAS,IAAI,QAAQA,UAASA,UAAS,EAAE,QAAQ,yBAAyB,gBAAgB,mBAAmB,KAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,CAAC,CAAE,GAAG,gBAAgB,OAAO,QAAQ,YAAY,OAAO,CAAC,CAAC;AAAA,QACnP;AAAA,MACJ,CAAC,EACI,KAAK,SAAUE,KAAI;AACpB,YAAI,OAAOA,IAAG;AACd,eAAQ;AAAA,UACJ,MAAM,KAAK,IAAI,SAAUC,OAAM;AAAE,mBAAO,SAASA,OAAM,UAAU;AAAA,UAAG,CAAC;AAAA,QACzE;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,QAAQ,SAAU,UAAU,QAAQ;AAChC,UAAI,IAAI;AACR,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,OAAO,OAAO;AAClB,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,QAAQ,SAAS,YAAY,QAAW,UAAU,IAAI;AAC1D,UAAI,WAAW,UAAAF,QAAG,UAAU,KAAK;AACjC,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,SAAS,IAAI,MAAM,EAAE,EAAE,OAAO,QAAQ;AAC/G,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,aAAO,OACF,WAAW,KAAK;AAAA,QACjB,QAAQ;AAAA,QACR,SAAS,IAAI,QAAQD,UAASA,UAAS,EAAE,QAAQ,qCAAqC,QAAQ,yBAAyB,gBAAgB,mBAAmB,KAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,CAAC,CAAE,GAAG,gBAAgB,OAAO,QAAQ,YAAY,MAAM,CAAC,CAAC;AAAA,QAC/R,MAAM,KAAK,UAAU,qBAAqB,OAAO,MAAM,UAAU,CAAC;AAAA,MACtE,CAAC,EACI,KAAK,SAAUE,KAAI;AACpB,YAAI,OAAOA,IAAG;AACd,eAAQ;AAAA,UACJ,MAAMF,UAASA,UAAS,CAAC,GAAG,IAAI,GAAG,EAAE,IAAI,SAAS,MAAM,UAAU,EAAE,CAAC;AAAA,QACzE;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,QAAQ,SAAU,UAAU,QAAQ;AAChC,UAAI,IAAI;AACR,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,KAAK,OAAO,IAAI,OAAO,OAAO;AAClC,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,QAAQ,SAAS,YAAY,IAAI,UAAU,IAAI;AACnD,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,UAAAC,QAAG,UAAU,KAAK,CAAC;AACxF,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,aAAO,OACF,WAAW,KAAK;AAAA,QACjB,QAAQ;AAAA,QACR,SAAS,IAAI,QAAQD,UAASA,UAAS,EAAE,QAAQ,qCAAqC,QAAQ,yBAAyB,gBAAgB,mBAAmB,KAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,CAAC,CAAE,GAAG,gBAAgB,OAAO,QAAQ,YAAY,QAAQ,CAAC,CAAC;AAAA,MACrS,CAAC,EACI,KAAK,SAAUE,KAAI;AACpB,YAAI,OAAOA,IAAG;AACd,eAAQ;AAAA,UACJ,MAAM,kBAAkB,MAAM,UAAU;AAAA,QAC5C;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,YAAY,SAAU,UAAU,QAAQ;AACpC,UAAI,IAAI;AACR,UAAI,WAAW,QAAQ;AAAE,iBAAS,CAAC;AAAA,MAAG;AACtC,UAAI,MAAM,OAAO,KAAK,OAAO,OAAO;AACpC,UAAI,aAAa,cAAc,UAAU,OAAO,WAAW;AAC3D,UAAI,QAAQ,SAAS,YAAY,KAAK,UAAU,IAAI;AACpD,UAAI,MAAM,GAAG,OAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,UAAAD,QAAG,UAAU,KAAK,CAAC;AACxF,UAAI,cAAc,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC5E,aAAO,OACF,WAAW,KAAK;AAAA,QACjB,QAAQ;AAAA,QACR,SAAS,IAAI,QAAQD,UAASA,UAAS,EAAE,QAAQ,yBAAyB,gBAAgB,mBAAmB,KAAM,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,CAAC,CAAE,GAAG,gBAAgB,OAAO,QAAQ,YAAY,QAAQ,CAAC,CAAC;AAAA,MACxP,CAAC,EACI,KAAK,SAAUE,KAAI;AACpB,YAAI,OAAOA,IAAG;AACd,eAAQ;AAAA,UACJ,MAAM,KAAK,IAAI,SAAU,MAAM;AAAE,mBAAO,SAAS,MAAM,UAAU;AAAA,UAAG,CAAC;AAAA,QACzE;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAAI;AACJ,IAAI,aAAa,SAAU,MAAM,cAAc;AAC3C,MAAI,UAAU,OAAO,KAAK,IAAI,EAAE,OAAO,SAAUE,UAAS,KAAK;AAC3D,QAAI,KAAC,eAAAC,SAAQ,KAAK,GAAG,GAAG,aAAa,GAAG,CAAC,GAAG;AACxC,MAAAD,SAAQ,GAAG,IAAI,KAAK,GAAG;AAAA,IAC3B;AACA,WAAOA;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO;AACX;;;ACjQO,IAAM,uBAAuB,CAAC;AAAA,EACjC;AAAA,EACA;AAAA,EACA,iBAAiB,aAAa,aAAa,MAAM;AAAA,EACjD,aAAa,mBAAmB,EAAE,QAAQ,eAAe,CAAC;AAAA,EAC1D,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,SAAS;AAAA,EACT,GAAG;AACP,MAImE;AAC/D,QAAM,SAA8B;AAAA,IAChC,QAAQ,GAAG,WAAW;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP;AACA,SAAO;AAAA,IACH,gBAAgB,CAAC,KAAa,YAC1B,WAAW,GAAG,OAAO,MAAM,IAAI,GAAG,IAAI,OAAO;AAAA,IACjD,WAAW,YAAyC;AAChD,YAAM,EAAE,KAAK,IAAI,MAAM,WAAW,GAAG,OAAO,MAAM,KAAK,CAAC,CAAC;AACzD,UAAI,CAAC,QAAQ,CAAC,KAAK,SAAS;AACxB,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACzD;AACA,aAAO;AAAA,IACX;AAAA,IACA,GAAG,YAAsB,MAAM;AAAA,EACnC;AACJ;AAQO,IAAM,qBACT,CAAC;AAAA,EACG;AAAA,EACA;AACJ,MAIA,OAAO,KAAa,UAAe,CAAC,MAAM;AACtC,QAAM,EAAE,KAAK,IAAI,MAAM,eAAe,KAAK,WAAW;AACtD,MAAI,CAAC,QAAQ,QAAS,SAAQ,UAAU,IAAI,QAAQ,CAAC,CAAC;AAEtD,MAAI,eAAe,SAAS,GAAG;AAC3B,WAAO,QAAQ,eAAe,SAAS,CAAC,EAAE;AAAA,MAAQ,CAAC,CAAC,MAAM,KAAK,MAC3D,QAAQ,QAAQ,IAAI,MAAM,KAAK;AAAA,IACnC;AAAA,EACJ;AACA,MAAI,KAAK,SAAS;AACd,YAAQ,OAAO;AAAA,MACX,eAAe;AAAA;AAAA,MAEf,OAAO,UAAU,KAAK,QAAQ,YAAY;AAAA,IAC9C;AAAA,EACJ;AAEA,UAAQ,QAAQ,IAAI,UAAU,MAAM;AAEpC,SAAO,cAAW,UAAU,KAAK,OAAO;AAC5C;;;ACtFG,IAAM,eAAe,CAAC;AAAA,EACzB;AACJ,IAII,CAAC,MAAM;AACP,QAAM,eAAe,gBAAgB;AACrC,MAAI,CAAC,aAAa,WAAW;AACzB,UAAM,IAAI;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,SAA6B;AAAA,IAChC,UAAU,CAAC,WAAW;AAAA,IACtB,SAAS,MAAM,aAAa,UAAU;AAAA,IACtC,WAAW,MAAO;AAAA;AAAA,IAClB,GAAG;AAAA,EACP,CAAC;AACL;;;ACvBA,mBAA0B;AAanB,IAAM,6BAA6B,CACtC,aAAgD,QAC/C;AACD,QAAM,WAAW,YAAY;AAC7B,QAAM,YAAY,aAAa;AAE/B,8BAAU,MAAM;AACZ,cAAU,CAAC,GAAG,OAAO,MAAS,EACzB,KAAK,MAAM;AAER,eAAS,UAAU;AAAA,IACvB,CAAC,EACA,MAAM,MAAM;AAAA,IAEb,CAAC;AAAA,EACT,GAAG,CAAC,WAAW,UAAU,UAAU,CAAC;AACxC;;;ACIO,IAAM,mBAAmB,CAC5B,YAIC;AACD,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,eAAe,wBAAsC;AAE3D,MAAI,gBAAgB,MAAM;AACtB,UAAM,IAAI;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,aAAa,iBAAiB,MAAM;AACpC,UAAM,IAAI;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM;AAAA,IACF,YAAY,MAAM;AACd,eAAS,QAAQ;AACjB,aAAO,mCAAmC,EAAE,MAAM,OAAO,CAAC;AAAA,IAC9D;AAAA,IACA,UAAU,WAAS,OAAO,MAAM,SAAS,EAAE,MAAM,QAAQ,CAAC;AAAA,EAC9D,IAAI,WAAW,CAAC;AAEhB,QAAM,WAAW,YAAiD;AAAA,IAC9D,YAAY,YAAU;AAClB,aAAO,aAAa,cAAc,MAAM;AAAA,IAC5C;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACX,CAAC;AAED,SAAO,CAAC,SAAS,QAAQ,QAAQ;AACrC;;;ACtCO,IAAM,iBAAiB,CAC1B,YAIC;AACD,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,eAAe,wBAAsC;AAE3D,MAAI,gBAAgB,MAAM;AACtB,UAAM,IAAI;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,aAAa,eAAe,MAAM;AAClC,UAAM,IAAI;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM;AAAA,IACF,YAAY,MAAM,SAAS,GAAG;AAAA,IAC9B,UAAU,WAAS,OAAO,MAAM,SAAS,EAAE,MAAM,QAAQ,CAAC;AAAA,EAC9D,IAAI,WAAW,CAAC;AAEhB,QAAM,WAAW,YAA+C;AAAA,IAC5D,YAAY,YAAU;AAClB,aAAO,aAAa,YAAY,MAAM;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACX,CAAC;AAED,SAAO,CAAC,SAAS,QAAQ,QAAQ;AACrC;;;ACxEA,IAAAE,gBAA0B;AA8BnB,IAAM,yBAAyB,CAAC;AAAA,EACnC,aAAa;AAAA,EACb,gBAAgB;AACpB,IAAmC,CAAC,MAAM;AACtC,QAAM,WAAW,YAAY;AAE7B,QAAM,YAAY,gBAAgB;AAClC,QAAM,kBAAkB,IAAI,gBAAgB,SAAS;AACrD,QAAM,eAAe,gBAAgB,IAAI,aAAa;AACtD,+BAAU,MAAM;AACZ,QAAI,gBAAgB,MAAM;AACtB,UAAI,eAAe,OAAO;AACtB,iBAAS,UAAU;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ,CAAC;AAED,SAAO;AACX;;;ACjDA,IAAAC,gBAQO;AAQP,kBAAqB;AA8DL;AAxCT,IAAM,aAAkD,WAAS;AACpE,QAAM;AAAA,IACF,QAAQ;AAAA,IACR;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA,GAAG;AAAA,EACP,IAAI;AACJ,QAAM,mBAAe,sBAAuB,IAAI;AAChD,QAAM,eAAW,uBAAQ,MAAM,oBAAY,KAAK,GAAG,CAAC,KAAK,CAAC;AAC1D,MAAI,wBAAwB;AAE5B,QAAM,wBAAwB,MAAM;AAChC,QAAI,CAAC,yBAAyB,aAAa,SAAS;AAChD,mBAAa,QAAQ,MAAM,kBAAkB,OAAO,eAAe;AACnE,8BAAwB;AAAA,IAC5B;AAAA,EACJ;AAGA,QAAM,0BAA0B,MAAM;AAClC,QAAI,iBAAiB;AACjB,YAAM,MAAM,IAAI,MAAM;AACtB,UAAI,SAAS;AACb,UAAI,MAAM;AAAA,IACd;AAAA,EACJ;AAEA,+BAAU,MAAM;AACZ,QAAI,CAAC,uBAAuB;AACxB,8BAAwB;AAAA,IAC5B;AAAA,EACJ,CAAC;AAED,aACI,wBAAC,iBAAc,OAAO,UAClB,uCAAC,QAAM,GAAG,MAAM,KAAK,cACjB;AAAA,iCAAC,gBAAK,WAAW,kBAAkB,MAC/B;AAAA,kCAAC,SAAI,WAAW,kBAAkB,QAC9B,sCAAC,kBAAO,WAAW,kBAAkB,MACjC,sCAAC,YAAAC,SAAA,EAAS,GACd,GACJ;AAAA,MACC;AAAA,OACL;AAAA,IACC,mBAAe,6BAAc,YAAY,IAAI;AAAA,KAClD,GACJ;AAER;AAaA,IAAM,SAAS;AAER,IAAM,oBAAoB;AAAA,EAC7B,MAAM,GAAG,MAAM;AAAA,EACf,QAAQ,GAAG,MAAM;AAAA,EACjB,MAAM,GAAG,MAAM;AACnB;AAEA,IAAM,OAAO,eAAO,OAAO;AAAA,EACvB,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AACjD,CAAC,EAAE,CAAC,EAAE,MAAM,OAAO;AAAA,EACf,SAAS;AAAA,EACT,eAAe;AAAA,EACf,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,iBACI;AAAA,EACJ,CAAC,MAAM,kBAAkB,IAAI,EAAE,GAAG;AAAA,IAC9B,UAAU;AAAA,IACV,WAAW;AAAA,EACf;AAAA,EACA,CAAC,MAAM,kBAAkB,MAAM,EAAE,GAAG;AAAA,IAChC,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,gBAAgB;AAAA,EACpB;AAAA,EACA,CAAC,MAAM,kBAAkB,IAAI,EAAE,GAAG;AAAA,IAC9B,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,EAC3C;AACJ,EAAE;;;ACzIF,IAAAC,SAAuB;AAWvB,IAAAC,qBAAwC;;;ACVxC,wBAA0B;AAE1B,IAAM,UAAU,CAAC,MAAM,UAAU,OAAO,MAAM,IAAI,MAAM;AAEjD,IAAM,uBAAuB,CAAC;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ,MAQM;AACF,MAAI,SAAS,QAAQ,QAAQ,MAAM,KAAK,GAAG;AACvC,WAAO,IAAI,wBAAgB,MAAM,IAAI,EAAE,QAAQ,KAAK,CAAC;AAAA,EACzD;AACA,QAAM,YAAW,iDAAgB,SAAS,SAAQ,CAAC,SAAS,CAAC,IAAI;AACjE,OACI,2CAAa,WAAW,uCACxB,QAAQ,aAAa,KAAK,GAC5B;AACE,UAAM,YAAY,YAAY,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AACxD,WAAO,IAAI,wBAAgB,MAAM,WAAW;AAAA,MACxC,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AACA,MACI,KAAK,UAAU,KAAK,SAAS,CAAC,MAAM,UACpC,QAAQ,aAAa,KAAK,GAC5B;AACE,UAAM,gBAAY,6BAAU,KAAK,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC;AAC3D,WAAO,IAAI,wBAAgB,MAAM,gBAAgB;AAAA,MAC7C,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AACA,MAAI,SAAS,SAAS;AAElB,WAAO,IAAI,wBAAgB,MAAM,QAAQ;AAAA,MACrC,QAAQ;AAAA,MACR;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,SAAS,UAAU;AACnB,QAAI,SAAS,WAAW,QAAQ,SAAS,KAAK,GAAG;AAC7C,aAAO,IAAI,wBAAgB,MAAM,OAAO;AAAA,QACpC,QAAQ;AAAA,QACR;AAAA,QACA,GAAG;AAAA,MACP,CAAC;AAAA,IACL;AACA,QAAI,CAAC,OAAO,SAAS,EAAE,SAAS,IAAI,KAAK,QAAQ,OAAO,KAAK,GAAG;AAC5D,aAAO,IAAI,wBAAgB,MAAM,KAAK;AAAA,QAClC,QAAQ;AAAA,QACR;AAAA,QACA,GAAG;AAAA,MACP,CAAC;AAAA,IACL;AACA,QACI,UACA;AAAA,MACI;AAAA,MACA;AAAA,IACJ,EAAE,SAAS,MAAM,KACjB,QAAQ,QAAQ,KAAK,GACvB;AACE,aAAO,IAAI,wBAAgB,MAAM,MAAM;AAAA,QACnC,QAAQ;AAAA,QACR;AAAA,QACA,GAAG;AAAA,MACP,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,SAAS,aAAa,QAAQ,UAAU,KAAK,GAAG;AAChD,WAAO,IAAI,wBAAgB,MAAM,QAAQ;AAAA,MACrC,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AACA,MAAI,QAAQ,QAAQ,MAAM,KAAK,GAAG;AAC9B,WAAO,IAAI,wBAAgB,MAAM,IAAI,GAAG;AAAA,MACpC,QAAQ;AAAA,MACR;AAAA,MACA,GAAG;AAAA,IACP,CAAC;AAAA,EACL;AACA,SAAO,IAAI,wBAAgB,MAAM,QAAQ;AAAA,IACrC,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACP,CAAC;AACL;;;ADtEY,IAAAC,sBAAA;AAnBL,IAAM,gBAAgB,CAAC,UAAiD;AAC3E,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP,IAAI;AACJ,aACI;AAAA,IAAC;AAAA;AAAA,MACG;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA,uCAAC,qBAAmB,GAAG,MAAM;AAAA;AAAA,EACjC;AAER;AAEO,IAAM,oBAAoB,CAC7B,UAGC;AACD,QAAM,EAAE,MAAM,QAAQ,OAAO,UAAU,IAAI,aAAa;AACxD,QAAM,WAAW,mBAAmB;AACpC,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAoB,IAAI;AACxD,MAAI,CAAC,UAAU;AACX,UAAM,IAAI,MAAM,sDAAsD;AAAA,EAC1E;AACA,QAAM,EAAE,YAAY,MAAwC,GAAG,KAAK,IAChE;AAEJ,EAAM,iBAAU,MAAM;AArD1B;AAsDQ,QAAI,aAAa,OAAO;AACpB;AAAA,IACJ;AACA,UAAM,sBAAqB,YAAO,gBAAP,mBAAqB;AAChD,UAAM,kBAAiB,yDAAoB,aAAY,CAAC;AACxD,QAAI,CAAC,sBAAsB,CAAC,mBAAmB,YAAY;AACvD,YAAM,IAAI;AAAA,QACN,gBAAgB,QAAQ;AAAA,MAC5B;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,KAAK,mBAAmB,UAAU,EAC3D,OAAO,CAAC,WAAmB,WAAW,IAAI,EAC1C;AAAA,MACG,YACI,mBAAmB,WAAY,MAAM,EAAE,WAAW;AAAA,IAC1D,EACC;AAAA,MAAI,CAAC,WACF,qBAAqB;AAAA,QACjB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACI,mBAAmB,WAAY,MAAM,EAAE;AAAA,QAC3C,QAAQ,mBAAmB,WAAY,MAAM,EAAE;AAAA,QAC/C,MAAO,mBAAmB,cAC1B,mBAAmB,WAAW,MAAM,KACpC,OAAO,mBAAmB,WAAW,MAAM,EAAE,SACzC,WACE,mBAAmB,WAAY,MAAM,EAAE,OACvC;AAAA,QACN;AAAA,MACJ,CAAC;AAAA,IACL;AACJ,UAAM,eAAe,IAAI;AAAA,MACrB,eAAe;AAAA,MACf;AAAA,MACA;AAAA,IACJ;AACA,aAAS,aAAa,WAAW,CAAC;AAClC,QAAI,CAAC,UAAW;AAEhB,UAAM,iBAAiB,aAAa,kBAAkB;AAEtD,UAAM,aAAa,CAAC,QAAQ,EACvB;AAAA,MACG,MAAM;AAAA,QACF,IAAI;AAAA,UACA,MAAM,KAAK,eAAe,SAAS,cAAc,CAAC,EAC7C,IAAI,WAAS,MAAM,CAAC,CAAC,EACrB,OAAO,eAAa,cAAc,MAAM;AAAA,QACjD;AAAA,MACJ;AAAA,IACJ,EACC,KAAK;AAGV,YAAQ;AAAA,MACJ;AAAA;AAAA,WAED,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA,mBAEjB,mCAAW,gCAAY,QAAQ,CAAC,CAAC;AAAA;AAAA,EAE9C,cAAc;AAAA;AAAA;AAAA,IAGR;AAAA,EACJ,GAAG,CAAC,UAAU,WAAW,OAAO,QAAQ,SAAS,CAAC;AAElD,MAAI,UAAW,YAAO,yBAAC,WAAQ;AAC/B,MAAI,MAAO,YAAO,0BAAC,OAAE;AAAA;AAAA,IAAQ,MAAM;AAAA,KAAQ;AAE3C,aAAO,yBAAC,cAAY,GAAG,MAAO,iBAAM;AACxC;;;AE9HA,IAAAC,SAAuB;AAWvB,IAAAC,qBAAwC;AA2B5B,IAAAC,sBAAA;AAvBL,IAAM,cAAc,CAAC,UAAgD;AACxE,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP,IAAI;AACJ,aACI;AAAA,IAAC;AAAA;AAAA,MACG;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA,uCAAC,mBAAiB,GAAG,MAAM;AAAA;AAAA,EAC/B;AAER;AAEO,IAAM,kBAAkB,CAC3B,UAGC;AACD,QAAM,EAAE,MAAM,QAAQ,OAAO,UAAU,IAAI,aAAa;AACxD,QAAM,WAAW,mBAAmB;AACpC,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAoB,IAAI;AACxD,MAAI,CAAC,UAAU;AACX,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACxE;AACA,QAAM,EAAE,YAAY,MAAwC,GAAG,KAAK,IAChE;AAEJ,EAAM,iBAAU,MAAM;AAzD1B;AA0DQ,QAAI,aAAa,OAAO;AACpB;AAAA,IACJ;AACA,UAAM,sBAAqB,YAAO,gBAAP,mBAAqB;AAChD,UAAM,kBAAiB,yDAAoB,aAAY,CAAC;AACxD,QAAI,CAAC,sBAAsB,CAAC,mBAAmB,YAAY;AACvD,YAAM,IAAI;AAAA,QACN,gBAAgB,QAAQ;AAAA,MAC5B;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,KAAK,mBAAmB,UAAU,EAC3D,OAAO,CAAC,WAAmB,WAAW,IAAI,EAC1C;AAAA,MACG,YACI,mBAAmB,WAAY,MAAM,EAAE,WAAW;AAAA,IAC1D,EACC;AAAA,MAAI,CAAC,WACF,qBAAqB;AAAA,QACjB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACI,mBAAmB,WAAY,MAAM,EAAE;AAAA,QAC3C,QAAQ,mBAAmB,WAAY,MAAM,EAAE;AAAA,QAC/C,MAAO,mBAAmB,cAC1B,mBAAmB,WAAW,MAAM,KACpC,OAAO,mBAAmB,WAAW,MAAM,EAAE,SACzC,WACE,mBAAmB,WAAY,MAAM,EAAE,OACvC;AAAA,QACN;AAAA,MACJ,CAAC;AAAA,IACL;AACJ,UAAM,eAAe,IAAI;AAAA,MACrB,eAAe;AAAA,MACf;AAAA,MACA;AAAA,IACJ;AACA,aAAS,aAAa,WAAW,CAAC;AAClC,QAAI,CAAC,UAAW;AAEhB,UAAM,iBAAiB,aAAa,kBAAkB;AAEtD,UAAM,aAAa,CAAC,MAAM,EACrB;AAAA,MACG,MAAM;AAAA,QACF,IAAI;AAAA,UACA,MAAM,KAAK,eAAe,SAAS,cAAc,CAAC,EAC7C,IAAI,WAAS,MAAM,CAAC,CAAC,EACrB,OAAO,eAAa,cAAc,MAAM;AAAA,QACjD;AAAA,MACJ;AAAA,IACJ,EACC,KAAK;AAGV,YAAQ;AAAA,MACJ;AAAA;AAAA,WAED,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA,mBAEjB,mCAAW,gCAAY,QAAQ,CAAC,CAAC;AAAA;AAAA,EAE9C,cAAc;AAAA;AAAA;AAAA,IAGR;AAAA,EACJ,GAAG,CAAC,UAAU,WAAW,OAAO,QAAQ,SAAS,CAAC;AAElD,MAAI,UAAW,YAAO,yBAAC,WAAQ;AAC/B,MAAI,MAAO,YAAO,0BAAC,OAAE;AAAA;AAAA,IAAQ,MAAM;AAAA,KAAQ;AAE3C,aAAO,yBAAC,YAAU,GAAG,MAAO,iBAAM;AACtC;;;AClIA,IAAAC,SAAuB;AAavB,IAAAC,qBAAwC;AAiC5B,IAAAC,sBAAA;AA7BL,IAAM,cAAc,CAAC,UAA+C;AACvE,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP,IAAI;AACJ,aACI;AAAA,IAAC;AAAA;AAAA,MACG;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA,uCAAC,mBAAiB,GAAG,MAAM;AAAA;AAAA,EAC/B;AAER;AAEO,IAAM,kBAAkB,CAC3B,UAGC;AACD,QAAM,EAAE,MAAM,QAAQ,OAAO,UAAU,IAAI,aAAa;AACxD,QAAM,WAAW,mBAAmB;AACpC,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAoB,IAAI;AACxD,QAAM,CAAC,SAAS,UAAU,IAAU,gBAElC,MAAS;AACX,MAAI,CAAC,UAAU;AACX,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACxE;AACA,QAAM,EAAE,YAAY,MAAwC,GAAG,KAAK,IAChE;AAEJ,EAAM,iBAAU,MAAM;AApE1B;AAqEQ,QAAI,aAAa,OAAO;AACpB;AAAA,IACJ;AACA,UAAM,sBAAqB,YAAO,gBAAP,mBAAqB;AAChD,QAAI,CAAC,sBAAsB,CAAC,mBAAmB,YAAY;AACvD,YAAM,IAAI;AAAA,QACN,gBAAgB,QAAQ;AAAA,MAC5B;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,KAAK,mBAAmB,UAAU,EAC3D;AAAA,MACG,YACI,mBAAmB,WAAY,MAAM,EAAE,WAAW;AAAA,IAC1D,EACC;AAAA,MAAI,CAAC,WACF,qBAAqB;AAAA,QACjB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACI,mBAAmB,WAAY,MAAM,EAAE;AAAA,QAC3C,QAAQ,mBAAmB,WAAY,MAAM,EAAE;AAAA,QAC/C,MAAO,mBAAmB,cAC1B,mBAAmB,WAAW,MAAM,KACpC,OAAO,mBAAmB,WAAW,MAAM,EAAE,SACzC,WACE,mBAAmB,WAAY,MAAM,EAAE,OACvC;AAAA,MACV,CAAC;AAAA,IACL;AACJ,UAAM,gBAAgB,IAAI;AAAA,MACtB,eAAe;AAAA,MACf;AAAA,MACA;AAAA,IACJ;AACA,aAAS,cAAc,WAAW,CAAC;AAEnC,UAAM,eACF,YACK,MAAO,IAAI,QAAQ,EAAE,EAAE,IAAK,eADjC,mBAC6C;AAAA,MAAO,SAC5C,IAAI,MAAM,EAAE,SAAS,WAAW;AAAA,MAEnC,IAAI,SAAO,IAAI,MAAM,EAAE,MAAM,GAAG,EAAE,IAAI,OAAM,CAAC;AACtD,UAAM,2BAA2B,WAC5B;AAAA,MACG,YACI,mBAAmB,WAAY,MAAM,EAAE,WAAW;AAAA,IAC1D,EACC,IAAI,YAAU;AACX,YAAM,QAAQ,mBAAmB,WAAY,MAAM;AACnD,aAAO,qBAAqB;AAAA,QACxB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa,MAAM;AAAA,QACnB,QAAQ,MAAM;AAAA,QACd,MAAM,MAAM;AAAA,MAChB,CAAC;AAAA,IACL,CAAC;AACL,QACI,WAAW;AAAA,MACP,YACI,mBAAmB,WAAY,MAAM,EAAE,WAAW;AAAA,IAC1D,GACF;AACE,YAAM,uBAAuB,WAAW;AAAA,QACpC,YACI,mBAAmB,WAAY,MAAM,EAAE,WAAW;AAAA,MAC1D;AACA,YAAM,QAAQ,mBAAmB,WAAY,oBAAqB;AAClE,+BAAyB;AAAA,QACrB,qBAAqB;AAAA,UACjB,MAAM,GAAG,oBAAqB;AAAA,UAC9B,OAAO;AAAA,YACH,QAAQ;AAAA,cACJ,WAAW;AAAA,cACX,gBAAgB,CAAAC,WACZ,iCAAiCA,OAAM,MAAM;AAAA,YACrD;AAAA,UACJ;AAAA,UACA,aAAa,MAAM;AAAA,UACnB,QAAQ;AAAA,UACR,OAAO;AAAA,YACH,UAAU;AAAA,YACV,OAAO,WAAU,QAAQ,GAAG,KAAK,OAAO;AAAA,YACxC,QAAQ,WACJ,QAAQ,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,IAAI;AAAA,UACvD;AAAA,UACA,MAAM,MAAM;AAAA,QAChB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,QAAI,yBAAyB,SAAS,GAAG;AACrC,YAAM,iBAAiB,yBAClB,IAAI,mBAAiB,cAAc,WAAW,CAAC,EAC/C,OAAO,QAAM,MAAM,IAAI;AAC5B,iBAAW,cAAc;AAAA,IAC7B;AAEA,QAAI,CAAC,UAAW;AAEhB,UAAM,sBAAsB,cAAc,kBAAkB;AAE5D,UAAM,uBACF,yBAAyB,SAAS,IAC5B;AAAA,EAChB,yBACG,IAAI,mBAAiB,SAAS,cAAc,kBAAkB,CAAC,EAC/D,KAAK,KAAK,CAAC;AAAA;AAAA,IAGE;AAEV,UAAM,kBAAkB,MAAM;AAAA,MAC1B,oBAAoB,SAAS,cAAc;AAAA,IAC/C,EACK,IAAI,WAAS,MAAM,CAAC,CAAC,EACrB,OAAO,eAAa,cAAc,MAAM;AAC7C,UAAM,mBAAmB,MAAM;AAAA,MAC3B,qBAAqB,SAAS,cAAc;AAAA,IAChD,EACK,IAAI,WAAS,MAAM,CAAC,CAAC,EACrB,OAAO,eAAa,cAAc,MAAM;AAC7C,UAAM,aAAa,MAAM;AAAA,MACrB,oBAAI,IAAI,CAAC,QAAQ,GAAG,iBAAiB,GAAG,gBAAgB,CAAC;AAAA,IAC7D,EAAE,KAAK;AAGP,YAAQ;AAAA,MACJ;AAAA;AAAA,WAED,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA,EAE9B,oBAAoB;AAAA,mBACP,mCAAW,gCAAY,QAAQ,CAAC,CAAC;AAAA,WACrC,uBAAuB,uBAAuB,EAAE;AAAA,EACzD,mBAAmB;AAAA;AAAA;AAAA,IAGb;AAAA,EACJ,GAAG,CAAC,UAAU,WAAW,OAAO,QAAQ,SAAS,CAAC;AAElD,MAAI,UAAW,YAAO,yBAAC,WAAQ;AAC/B,MAAI,MAAO,YAAO,0BAAC,OAAE;AAAA;AAAA,IAAQ,MAAM;AAAA,KAAQ;AAE3C,aACI,yBAAC,YAAS,SAAmB,GAAG,MAC3B,iBACL;AAER;;;ACzNA,IAAAC,gBAAwB;;;ACAxB,IAAAC,SAAuB;AAWvB,IAAAC,qBAAwC;AAc5B,IAAAC,sBAAA;AAVL,IAAM,cAAc,CAAC,UAA+C;AACvE,QAAM,EAAE,IAAI,uBAAuB,cAAc,UAAU,GAAG,KAAK,IAC/D;AACJ,aACI;AAAA,IAAC;AAAA;AAAA,MACG;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA,uCAAC,mBAAiB,GAAG,MAAM;AAAA;AAAA,EAC/B;AAER;AAEO,IAAM,kBAAkB,CAC3B,UAGC;AACD,QAAM,EAAE,MAAM,QAAQ,OAAO,UAAU,IAAI,aAAa;AACxD,QAAM,WAAW,mBAAmB;AACpC,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAoB,IAAI;AACxD,MAAI,CAAC,UAAU;AACX,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACxE;AACA,QAAM,EAAE,YAAY,MAAwC,GAAG,KAAK,IAChE;AAEJ,EAAM,iBAAU,MAAM;AA5C1B;AA6CQ,QAAI,aAAa,OAAO;AACpB;AAAA,IACJ;AACA,UAAM,sBAAqB,YAAO,gBAAP,mBAAqB;AAChD,QAAI,CAAC,sBAAsB,CAAC,mBAAmB,YAAY;AACvD,YAAM,IAAI;AAAA,QACN,gBAAgB,QAAQ;AAAA,MAC5B;AAAA,IACJ;AACA,UAAM,iBAAiB,OAAO,KAAK,mBAAmB,UAAU,EAC3D;AAAA,MACG,YACI,mBAAmB,WAAY,MAAM,EAAE,WAAW;AAAA,IAC1D,EACC;AAAA,MAAI,CAAC,WACF,qBAAqB;AAAA,QACjB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aACI,mBAAmB,WAAY,MAAM,EAAE;AAAA,QAC3C,QAAQ,mBAAmB,WAAY,MAAM,EAAE;AAAA,QAC/C,MAAO,mBAAmB,cAC1B,mBAAmB,WAAW,MAAM,KACpC,OAAO,mBAAmB,WAAW,MAAM,EAAE,SACzC,WACE,mBAAmB,WAAY,MAAM,EAAE,OACvC;AAAA,MACV,CAAC;AAAA,IACL;AACJ,UAAM,iBAAiB,IAAI;AAAA,MACvB,eAAe;AAAA,MACf;AAAA,MACA;AAAA,IACJ;AACA,aAAS,eAAe,WAAW,CAAC;AACpC,QAAI,CAAC,UAAW;AAEhB,UAAM,iBAAiB,eAAe,kBAAkB;AAExD,UAAM,aAAa,CAAC,MAAM,EACrB;AAAA,MACG,MAAM;AAAA,QACF,IAAI;AAAA,UACA,MAAM,KAAK,eAAe,SAAS,cAAc,CAAC,EAC7C,IAAI,WAAS,MAAM,CAAC,CAAC,EACrB,OAAO,eAAa,cAAc,MAAM;AAAA,QACjD;AAAA,MACJ;AAAA,IACJ,EACC,KAAK;AAGV,YAAQ;AAAA,MACJ;AAAA;AAAA,WAED,WAAW,KAAK,IAAI,CAAC;AAAA;AAAA,mBAEjB,mCAAW,gCAAY,QAAQ,CAAC,CAAC;AAAA;AAAA,EAE9C,cAAc;AAAA;AAAA;AAAA,IAGR;AAAA,EACJ,GAAG,CAAC,UAAU,WAAW,OAAO,QAAQ,SAAS,CAAC;AAElD,MAAI,UAAW,YAAO,yBAAC,WAAQ;AAC/B,MAAI,MAAO,YAAO,0BAAC,OAAE;AAAA;AAAA,IAAQ,MAAM;AAAA,KAAQ;AAE3C,aAAO,yBAAC,YAAU,GAAG,MAAO,iBAAM;AACtC;;;ADzGO,IAAM,iBAAiB,MAAM;AAChC,QAAM,EAAE,MAAM,QAAQ,OAAO,UAAU,IAAI,aAAa;AACxD,aAAO,uBAAyB,MAAM;AAClC,QAAI,aAAa,OAAO;AACpB,aAAO,CAAC;AAAA,IACZ;AACA,UAAM,gBAAgB,OAAO,KAAK,OAAO,WAAY;AACrD,WAAO,cAAc,IAAI,UAAQ;AAC7B,YAAM,gBAAgB,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,CAAC;AACnD,aAAO;AAAA,QACH;AAAA,QACA,MAAM,cAAc,MAAM,cAAc;AAAA,QACxC,MAAM,cAAc,MAAM,cAAc;AAAA,QACxC,MAAM,cAAc,QAAQ,cAAc;AAAA,QAC1C,QAAQ,cAAc,OAAO,gBAAgB;AAAA,MACjD;AAAA,IACJ,CAAC;AAAA,EACL,GAAG,CAAC,QAAQ,WAAW,KAAK,CAAC;AACjC;;;AEiBgB,IAAAC,sBAAA;AAnCT,IAAM,qBAAqB,MAAM;AACpC,QAAM,SAAS,UAAU;AACzB,QAAM,YAAY,aAAa;AAC/B,QAAM,CAAC,aAAa,IAAI,iBAAiB;AAAA,IACrC,SAAS,WAAS;AACd;AAAA,QACI,OAAO,UAAU,WACX,QACA,OAAO,UAAU,eAAe,CAAC,MAAM,UACvC,0BACA,MAAM;AAAA,QACZ;AAAA,UACI,MAAM;AAAA,UACN,aAAa;AAAA,YACT,GACI,OAAO,UAAU,WACX,QACA,SAAS,MAAM,UACf,MAAM,UACN;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AAED,QAAM,SAAS,CAAC,WAAqB;AACjC,WAAO,cAAc;AAAA,MACjB,OAAO,OAAO;AAAA,IAClB,CAAC;AAAA,EACL;AAEA,aACI,0BAACC,OAAA,EAAK,UAAU,QACZ;AAAA,kCAAC,SAAI,WAAW,yBAAyB,WACrC;AAAA,oCAAC,iBAAM,SAAS,GACZ;AAAA,qCAAC,sBAAW,SAAQ,MAAK,WAAU,UAC9B;AAAA,UACG;AAAA,UACA,EAAE,GAAG,mBAAmB;AAAA,QAC5B,GACJ;AAAA,YAEA;AAAA,UAAC;AAAA;AAAA,YACG,SAAQ;AAAA,YACR,OAAM;AAAA,YACN,WAAU;AAAA,YAET;AAAA,cACG;AAAA,cACA;AAAA,gBACI,GAAG;AAAA,cACP;AAAA,YACJ;AAAA;AAAA,QACJ;AAAA,SACJ;AAAA,UAEA,yBAAC,SAAI,WAAW,yBAAyB,OACrC;AAAA,QAAC;AAAA;AAAA,UACG,QAAO;AAAA,UACP,OAAO,UAAU,iBAAiB;AAAA,YAC9B,GAAG;AAAA,UACP,CAAC;AAAA,UACD,cAAa;AAAA,UACb,WAAS;AAAA,UACT,UAAU,SAAS;AAAA;AAAA,MACvB,GACJ;AAAA,OACJ;AAAA,QACA,0BAAC,uBAAY,IAAI,EAAE,eAAe,UAAU,KAAK,EAAE,GAC/C;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,SAAQ;AAAA,UACR,MAAK;AAAA,UACL,WAAW,yBAAyB;AAAA,UACpC,OAAO,UAAU,4BAA4B;AAAA,YACzC,GAAG;AAAA,UACP,CAAC;AAAA,UACD,UAAM,yDAAE;AAAA;AAAA,MACZ;AAAA,UACA,yBAAC,QAAK,IAAG,UAAS,SAAQ,SACrB,oBAAU,kCAAkC;AAAA,QACzC,GAAG;AAAA,MACP,CAAC,GACL;AAAA,OACJ;AAAA,KACJ;AAER;AAMA,IAAMC,UAAS;AAEf,IAAM,2BAA2B;AAAA,EAC7B,WAAW,GAAGA,OAAM;AAAA,EACpB,OAAO,GAAGA,OAAM;AAAA,EAChB,QAAQ,GAAGA,OAAM;AACrB;AAEA,IAAMD,QAAO,eAAO,MAAM;AAAA,EACtB,MAAMC;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AACjD,CAAC,EAAE,CAAC,EAAE,MAAM,OAAO;AAAA,EACf,CAAC,MAAM,yBAAyB,SAAS,EAAE,GAAG;AAAA,IAC1C,SAAS;AAAA,EACb;AAAA,EACA,CAAC,MAAM,yBAAyB,KAAK,EAAE,GAAG;AAAA,IACtC,WAAW;AAAA,EACf;AAAA,EACA,CAAC,MAAM,yBAAyB,MAAM,EAAE,GAAG;AAAA,IACvC,OAAO;AAAA,EACX;AACJ,EAAE;;;AClGqB,IAAAC,sBAAA;AADhB,IAAM,qBAAqB,CAAC,UAAmC;AAClE,QAAM,EAAE,eAAW,yBAAC,sBAAmB,EAAG,IAAI;AAE9C,aAAO,yBAAC,cAAY,UAAS;AACjC;AAEA,mBAAmB,OAAO;;;ACcd,IAAAC,sBAAA;AAjCL,IAAM,YAAY,CAAC;AAAA,EACtB;AAAA,EACA,GAAG;AACP,MAAsB;AAClB,QAAM,QAAQ,iBAAS;AACvB,QAAM,SAAS,UAAU;AACzB,QAAM,YAAY,aAAa;AAE/B,QAAM,SAAS,CAAC,WAAqB;AACjC,WAAO,MAAM,MAAM,EAAE,MAAM,WAAS;AAChC;AAAA,QACI,OAAO,UAAU,WACX,QACA,OAAO,UAAU,eAAe,CAAC,MAAM,UACvC,0BACA,MAAM;AAAA,QACZ;AAAA,UACI,MAAM;AAAA,UACN,aAAa;AAAA,YACT,GACI,OAAO,UAAU,WACX,QACA,SAAS,MAAM,UACf,MAAM,UACN;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,aACI,0BAACC,OAAA,EAAK,UAAU,QAAS,GAAG,OACxB;AAAA,kCAAC,SAAI,WAAWC,0BAAyB,WACrC;AAAA,mCAAC,SAAI,WAAWA,0BAAyB,OACrC;AAAA,QAAC;AAAA;AAAA,UACG,WAAS;AAAA,UACT,QAAO;AAAA,UACP,MAAK;AAAA,UACL,OAAO,UAAU,0BAA0B;AAAA,YACvC,GAAG;AAAA,UACP,CAAC;AAAA,UACD,WAAS;AAAA,UACT,UAAU,SAAS;AAAA;AAAA,MACvB,GACJ;AAAA,UACA,yBAAC,SACG;AAAA,QAAC;AAAA;AAAA,UACG,QAAO;AAAA,UACP,OAAO,UAAU,oBAAoB;AAAA,YACjC,GAAG;AAAA,UACP,CAAC;AAAA,UACD,cAAa;AAAA,UACb,WAAS;AAAA,UACT,UAAU,SAAS;AAAA;AAAA,MACvB,GACJ;AAAA,OACJ;AAAA,QACA,0BAAC,uBAAY,IAAI,EAAE,eAAe,UAAU,KAAK,EAAE,GAC/C;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,SAAQ;AAAA,UACR,MAAK;AAAA,UACL,WAAWA,0BAAyB;AAAA,UACpC,OAAO,UAAU,iBAAiB;AAAA,UAClC,UAAM,yDAAE;AAAA;AAAA,MACZ;AAAA,MACC,CAAC,4BACE,yBAAC,QAAK,IAAI,mBAAmB,MAAM,SAAQ,SACtC,oBAAU,oCAAoC;AAAA,QAC3C,GAAG;AAAA,MACP,CAAC,GACL,IACA;AAAA,OACR;AAAA,KACJ;AAER;AAYA,IAAMC,UAAS;AAEf,IAAMD,4BAA2B;AAAA,EAC7B,WAAW,GAAGC,OAAM;AAAA,EACpB,OAAO,GAAGA,OAAM;AAAA,EAChB,QAAQ,GAAGA,OAAM;AACrB;AAEA,IAAMF,QAAO,eAAO,MAAM;AAAA,EACtB,MAAME;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AACjD,CAAC,EAAE,CAAC,EAAE,MAAM,OAAO;AAAA,EACf,CAAC,MAAMD,0BAAyB,SAAS,EAAE,GAAG;AAAA,IAC1C,SAAS;AAAA,EACb;AAAA,EACA,CAAC,MAAMA,0BAAyB,KAAK,EAAE,GAAG;AAAA,IACtC,WAAW;AAAA,EACf;AAAA,EACA,CAAC,MAAMA,0BAAyB,MAAM,EAAE,GAAG;AAAA,IACvC,OAAO;AAAA,EACX;AACJ,EAAE;;;AClHE,IAAAE,sBAAA;AADG,IAAM,aAAa,CAAC,cACvB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA;AAAA;AACJ;AAGG,IAAM,eAAe,CAAC,cACzB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA,mCAAC,UAAK,MAAK,WAAU,GAAE,6CAA4C;AAAA,UACnE;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA;AAAA;AACJ;AAGG,IAAM,cAAc,CAAC,cACxB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA,MAAC;AAAA;AAAA,QACG,MAAK;AAAA,QACL,GAAE;AAAA;AAAA,IACN;AAAA;AACJ;AAGG,IAAM,YAAY,CAAC,cACtB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,MAAK;AAAA,IACL,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEN;AAAA;AAAA,UACD,yBAAC,UAAK,GAAE,kiCAAiiC;AAAA;AAAA;AAC7iC;AAGG,IAAM,aAAa,CAAC,cACvB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,MAAK;AAAA,IACL,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEN;AAAA;AAAA,UACD,yBAAC,UAAK,GAAE,i0BAAg0B;AAAA;AAAA;AAC50B;AAGG,IAAM,aAAa,CAAC,cACvB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA,mCAAC,UAAK,MAAK,WAAU,GAAE,uBAAsB;AAAA,UAC7C,yBAAC,UAAK,MAAK,WAAU,GAAE,uBAAsB;AAAA,UAC7C,yBAAC,UAAK,MAAK,WAAU,GAAE,sBAAqB;AAAA,UAC5C,yBAAC,UAAK,MAAK,WAAU,GAAE,uBAAsB;AAAA,UAC7C,yBAAC,UAAK,MAAK,WAAU,GAAE,sBAAqB;AAAA,UAC5C,yBAAC,UAAK,MAAK,WAAU,GAAE,qBAAoB;AAAA,UAC3C,yBAAC,UAAK,MAAK,WAAU,GAAE,qBAAoB;AAAA;AAAA;AAC/C;AAEG,IAAM,gBAAgB,CAAC,cAC1B;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IAER;AAAA,mCAAC,UACG;AAAA,QAAC;AAAA;AAAA,UACG,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,eAAc;AAAA,UAEd;AAAA,yCAAC,UAAK,QAAO,QAAO,WAAU,WAAU;AAAA,gBACxC,yBAAC,UAAK,QAAO,KAAI,WAAU,WAAU;AAAA;AAAA;AAAA,MACzC,GACJ;AAAA,UACA,yBAAC,WAAM,4BAAc;AAAA,UACrB,yBAAC,OAAE,IAAG,WAAU,aAAU,WACtB,wCAAC,OAAE,IAAG,QAAO,WAAU,sBACnB;AAAA;AAAA,UAAC;AAAA;AAAA,YACG,GAAE;AAAA,YACF,MAAK;AAAA;AAAA,QACT;AAAA,YACA;AAAA,UAAC;AAAA;AAAA,YACG,GAAE;AAAA,YACF,MAAK;AAAA;AAAA,QACT;AAAA,SACJ,GACJ;AAAA;AAAA;AACJ;AAGG,IAAM,cAAc,CAAC,cACxB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA,MAAC;AAAA;AAAA,QACG,MAAK;AAAA,QACL,GAAE;AAAA;AAAA,IACN;AAAA;AACJ;AAGG,IAAM,YAAY,CAAC,cACtB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,mBAAkB;AAAA,UAClB,eAAc;AAAA,UAEd;AAAA,yCAAC,UAAK,QAAO,KAAI,WAAU,WAAU;AAAA,gBACrC,yBAAC,UAAK,QAAO,KAAI,WAAU,WAAU;AAAA;AAAA;AAAA,MACzC;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,mBAAkB;AAAA,UAClB,eAAc;AAAA,UAEd;AAAA,yCAAC,UAAK,QAAO,KAAI,aAAY,MAAK;AAAA,gBAClC,yBAAC,UAAK,QAAO,QAAO,aAAY,MAAK;AAAA,gBACrC,yBAAC,UAAK,QAAO,QAAO,aAAY,MAAK;AAAA,gBACrC,yBAAC,UAAK,QAAO,QAAO,aAAY,OAAM;AAAA,gBACtC,yBAAC,UAAK,QAAO,KAAI,aAAY,KAAI;AAAA;AAAA;AAAA,MACrC;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,IAAG;AAAA,UACH,mBAAkB;AAAA,UAClB,eAAc;AAAA,UAEd;AAAA,yCAAC,UAAK,QAAO,KAAI,WAAU,WAAU;AAAA,gBACrC,yBAAC,UAAK,QAAO,KAAI,WAAU,WAAU;AAAA;AAAA;AAAA,MACzC;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA;AAAA;AACJ;AAGG,IAAM,eAAe,CAAC,cACzB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,OAAM;AAAA,IAEN;AAAA,MAAC;AAAA;AAAA,QACG,GAAE;AAAA,QACF,MAAK;AAAA;AAAA,IACT;AAAA;AACJ;AAGG,IAAM,eAAe,CAAC,cACzB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA;AAAA;AACJ;AAGG,IAAM,aAAa,CAAC,cACvB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IACP,UAAS;AAAA,IACT,UAAS;AAAA,IAET;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,UAAS;AAAA,UACT,GAAE;AAAA,UACF,UAAS;AAAA;AAAA,MACb;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,UAAS;AAAA,UACT,GAAE;AAAA,UACF,UAAS;AAAA;AAAA,MACb;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,UAAS;AAAA,UACT,GAAE;AAAA,UACF,UAAS;AAAA;AAAA,MACb;AAAA;AAAA;AACJ;AAGG,IAAM,YAAY,CAAC,cACtB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACN;AAAA;AAAA;AACJ;AAGG,IAAM,cAAc,CAAC,cACxB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,OAAM;AAAA,IAEN;AAAA,MAAC;AAAA;AAAA,QACG,GAAE;AAAA,QACF,MAAK;AAAA;AAAA,IACT;AAAA;AACJ;AAGG,IAAM,aAAa,CAAC,cACvB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,OAAM;AAAA,IAEN;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,GAAE;AAAA,UACF,MAAK;AAAA;AAAA,MACT;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,GAAE;AAAA,UACF,MAAK;AAAA;AAAA,MACT;AAAA,UACA,yBAAC,UAAK,GAAE,6BAA4B,MAAK,WAAU;AAAA,UACnD,yBAAC,UAAK,GAAE,6BAA4B,MAAK,WAAU;AAAA;AAAA;AACvD;AAGG,IAAM,aAAa,CAAC,cACvB;AAAA,EAAC;AAAA;AAAA,IACI,GAAG;AAAA,IACJ,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,OAAM;AAAA,IAEN;AAAA;AAAA,QAAC;AAAA;AAAA,UACG,GAAE;AAAA,UACF,MAAK;AAAA;AAAA,MACT;AAAA,UACA;AAAA,QAAC;AAAA;AAAA,UACG,GAAE;AAAA,UACF,MAAK;AAAA;AAAA,MACT;AAAA;AAAA;AACJ;;;ACvVI,IAAAC,uBAAA;AAvBD,IAAM,mBAAmB,CAAC;AAAA,EAC7B;AAAA,EACA,UAAU;AAAA,EACV,GAAG;AACP,MAA6B;AACzB,QAAM,QAAQ,iBAAS;AACvB,QAAM,SAAS,UAAU;AAEzB,QAAM,cAAc,MAAM;AACtB,UAAM,EAAE,SAAS,GAAG,cAAc,OAAO,SAAS,SAAS,CAAC,EAAE;AAAA,MAC1D,WAAS;AAKL,YAAI,OAAO;AACP,iBAAQ,MAAgB,SAAS,EAAE,MAAM,QAAQ,CAAC;AAAA,QACtD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,aACI;AAAA,IAAC;AAAA;AAAA,MACG,SAAS;AAAA,MACT,SAAQ;AAAA,MACR,MAAK;AAAA,MACL,OAAM;AAAA,MACL,GAAG;AAAA;AAAA,EACR;AAER;AAOO,IAAM,cAAc,CAAC,UAAmD;AAC3E,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI,0BAAC,oBAAiB,eAAW,0BAAC,aAAU,GAAI,UAAS,SAAS,GAAG,OAC5D,iBACL;AAER;AAEO,IAAM,cAAc,CAAC,UAAmD;AAC3E,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI,0BAAC,oBAAiB,eAAW,0BAAC,aAAU,GAAI,UAAS,SAAS,GAAG,OAC5D,iBACL;AAER;AAEO,IAAM,kBAAkB,CAC3B,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,iBAAc;AAAA,MAC1B,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,gBAAgB,CACzB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,eAAY;AAAA,MACxB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,iBAAiB,CAC1B,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,gBAAa;AAAA,MACzB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,eAAe,CACxB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,cAAW;AAAA,MACvB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,eAAe,CACxB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,cAAW;AAAA,MACvB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,eAAe,CACxB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,cAAW;AAAA,MACvB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,iBAAiB,CAC1B,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,gBAAa;AAAA,MACzB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,iBAAiB,CAC1B,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,gBAAa;AAAA,MACzB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,eAAe,CACxB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,cAAW;AAAA,MACvB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,cAAc,CAAC,UAAmD;AAC3E,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI,0BAAC,oBAAiB,eAAW,0BAAC,aAAU,GAAI,UAAS,SAAS,GAAG,OAC5D,iBACL;AAER;AAEO,IAAM,gBAAgB,CACzB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,eAAY;AAAA,MACxB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,eAAe,CACxB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,cAAW;AAAA,MACvB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,gBAAgB,CACzB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,eAAY;AAAA,MACxB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;AAEO,IAAM,eAAe,CACxB,UACC;AACD,QAAM,YAAY,aAAa;AAC/B,QAAM,QAAQ,UAAU,iCAAiC;AAAA,IACrD,UAAU;AAAA,EACd,CAAC;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,eAAW,0BAAC,cAAW;AAAA,MACvB,UAAS;AAAA,MACR,GAAG;AAAA,MAEH;AAAA;AAAA,EACL;AAER;;;ACrQwB,IAAAC,uBAAA;AAbjB,IAAM,YAAY,CAAC,UAA0B;AAChD,QAAM;AAAA,IACF;AAAA,IACA,uBAAuB;AAAA,IACvB,wBAAwB;AAAA,IACxB,YAAY,CAAC;AAAA,EACjB,IAAI;AAEJ,aACI,0BAAC,cACI,0BACG,4DACK;AAAA,2BAAuB,WACpB;AAAA,MAAC;AAAA;AAAA,QACG;AAAA;AAAA,IACJ;AAAA,IAEH,wBAAwB,UAAU,WAAW,IAAI,WAC9C,0BAAC,mBAAQ;AAAA,IAEZ,aAAa,UAAU,SAAS,QAC7B,2DACI,yCAAC,iBAAM,KAAK,GAAG,SAAS,GACnB;AAAA,gBAAU,SAAS,OAAO,QACvB,0BAAC,eAAY,IACb;AAAA,MACH,UAAU,SAAS,OAAO,QACvB,0BAAC,eAAY,IACb;AAAA,MACH,UAAU,SAAS,WAAW,QAC3B,0BAAC,mBAAgB,IACjB;AAAA,MACH,UAAU,SAAS,SAAS,QACzB,0BAAC,iBAAc,IACf;AAAA,MACH,UAAU,SAAS,UAAU,QAC1B,0BAAC,kBAAe,IAChB;AAAA,MACH,UAAU,SAAS,QAAQ,QACxB,0BAAC,gBAAa,IACd;AAAA,MACH,UAAU,SAAS,QAAQ,QACxB,0BAAC,gBAAa,IACd;AAAA,MACH,UAAU,SAAS,QAAQ,QACxB,0BAAC,gBAAa,IACd;AAAA,MACH,UAAU,SAAS,UAAU,QAC1B,0BAAC,kBAAe,IAChB;AAAA,MACH,UAAU,SAAS,UAAU,QAC1B,0BAAC,kBAAe,IAChB;AAAA,MACH,UAAU,SAAS,QAAQ,QACxB,0BAAC,gBAAa,IACd;AAAA,MACH,UAAU,SAAS,OAAO,QACvB,0BAAC,eAAY,IACb;AAAA,MACH,UAAU,SAAS,SAAS,QACzB,0BAAC,iBAAc,IACf;AAAA,MACH,UAAU,SAAS,QAAQ,QACxB,0BAAC,gBAAa,IACd;AAAA,MACH,UAAU,SAAS,SAAS,QACzB,0BAAC,iBAAc,IACf;AAAA,MACH,UAAU,SAAS,QAAQ,QACxB,0BAAC,gBAAa,IACd;AAAA,OACR,GACJ,IACA;AAAA,KACR,GAER;AAER;;;AC3FgB,IAAAC,uBAAA;AAjDT,IAAM,kBAAkB,MAAM;AACjC,QAAM,eAAe,uBAAuB;AAC5C,QAAM,gBAAgB,uBAAuB;AAAA,IACzC,eAAe;AAAA,EACnB,CAAC;AAED,QAAM,SAAS,UAAU;AACzB,QAAM,YAAY,aAAa;AAC/B,QAAM,CAAC,WAAW,IAAI,eAAe;AAAA,IACjC,SAAS,WAAS;AACd;AAAA,QACI,OAAO,UAAU,WACX,QACA,OAAO,UAAU,eAAe,CAAC,MAAM,UACvC,0BACA,MAAM;AAAA,QACZ;AAAA,UACI,MAAM;AAAA,UACN,aAAa;AAAA,YACT,GACI,OAAO,UAAU,WACX,QACA,SAAS,MAAM,UACf,MAAM,UACN;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AAED,QAAM,WAAW,CAAC,WAAqB;AACnC,QAAI,OAAO,aAAa,OAAO,iBAAiB;AAC5C,aAAO;AAAA,QACH,UAAU;AAAA,QACV,iBAAiB;AAAA,MACrB;AAAA,IACJ;AACA,WAAO,CAAC;AAAA,EACZ;AAEA,MAAI,CAAC,gBAAgB,CAAC,eAAe;AACjC,QAAI,MAAwC;AACxC,cAAQ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,eACI,0BAAC,SAAI,WAAWC,0BAAyB,WACrC,wCAAC,SAAK,oBAAU,iCAAiC,GAAE,GACvD;AAAA,EAER;AAEA,QAAM,SAAS,CAAC,WAAqB;AACjC,WAAO,YAAY;AAAA,MACf;AAAA,MACA;AAAA,MACA,UAAU,OAAO;AAAA,IACrB,CAAC;AAAA,EACL;AAEA,aACI,2BAACC,OAAA,EAAK,UAAU,QAAQ,UACpB;AAAA,mCAAC,SAAI,WAAWD,0BAAyB,WACrC;AAAA,oCAAC,sBAAW,SAAQ,MAAK,WAAU,UAAS,cAAY,MACnD,oBAAU,yCAAyC;AAAA,QAChD,GAAG;AAAA,MACP,CAAC,GACL;AAAA,UAEA,0BAAC,SAAI,WAAWA,0BAAyB,OACrC;AAAA,QAAC;AAAA;AAAA,UACG,QAAO;AAAA,UACP,OAAO,UAAU,oBAAoB;AAAA,YACjC,GAAG;AAAA,UACP,CAAC;AAAA,UACD,cAAa;AAAA,UACb,WAAS;AAAA,UACT,UAAU,SAAS;AAAA;AAAA,MACvB,GACJ;AAAA,UACA,0BAAC,SACG;AAAA,QAAC;AAAA;AAAA,UACG,QAAO;AAAA,UACP,OAAO,UAAU,4BAA4B;AAAA,YACzC,GAAG;AAAA,UACP,CAAC;AAAA,UACD,WAAS;AAAA,UACT,UAAU,SAAS;AAAA;AAAA,MACvB,GACJ;AAAA,OACJ;AAAA,QACA,0BAAC,uBACG;AAAA,MAAC;AAAA;AAAA,QACG,SAAQ;AAAA,QACR,MAAK;AAAA,QACL,WAAWA,0BAAyB;AAAA,QACpC,OAAO,UAAU,gBAAgB;AAAA;AAAA,IACrC,GACJ;AAAA,KACJ;AAER;AAOA,IAAME,UAAS;AAEf,IAAMF,4BAA2B;AAAA,EAC7B,WAAW,GAAGE,OAAM;AAAA,EACpB,OAAO,GAAGA,OAAM;AAAA,EAChB,QAAQ,GAAGA,OAAM;AACrB;AAEA,IAAMD,QAAO,eAAO,MAAM;AAAA,EACtB,MAAMC;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AACjD,CAAC,EAAE,CAAC,EAAE,MAAM,OAAO;AAAA,EACf,CAAC,MAAMF,0BAAyB,SAAS,EAAE,GAAG;AAAA,IAC1C,SAAS;AAAA,EACb;AAAA,EACA,CAAC,MAAMA,0BAAyB,KAAK,EAAE,GAAG;AAAA,IACtC,WAAW;AAAA,EACf;AAAA,EACA,CAAC,MAAMA,0BAAyB,MAAM,EAAE,GAAG;AAAA,IACvC,OAAO;AAAA,EACX;AACJ,EAAE;;;ACnHqB,IAAAG,uBAAA;AADhB,IAAM,kBAAkB,CAAC,UAAgC;AAC5D,QAAM,EAAE,eAAW,0BAAC,mBAAgB,EAAG,IAAI;AAE3C,aAAO,0BAAC,cAAY,UAAS;AACjC;AAEA,gBAAgB,OAAO;;;AC/BhB,IAAM,4BAA4B;AAAA,EACrC,eAAe;AAAA,IACX,MAAM;AAAA,MACF,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,gBACI;AAAA,MACJ,gBAAgB;AAAA,MAChB,eAAe;AAAA,IACnB;AAAA,IACA,gBAAgB;AAAA,MACZ,iBAAiB;AAAA,MACjB,yBAAyB;AAAA,IAC7B;AAAA,IACA,cAAc;AAAA,MACV,cAAc;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,MACR,mBAAmB;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACnBO,IAAM,sBAAsB,YAAqB,MAAM;AAC1D,SAAO,kBAAkBC,cAAiB,yBAAyB;AACvE,GAAG,IAAI;;;ACPP,IAAAC,SAAuB;AAqEP,IAAAC,uBAAA;AAhDT,IAAM,eAAe,CACxB,UACC;AACD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAAC;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP,IAAI;AAEJ,QAAM,wBACF,eAAe,SAAS,aAAa,aAAa,MAAM,IAAI;AAChE,QAAM,sBACF,eAAe,UAAU,wBACnB,qBAAqB;AAAA,IACjB;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,EACpB,CAAC,IACD;AACV,QAAM,sBACF,eAAe,UAAU,wBACnB,qBAAqB,uBAAuB,CAAC,CAAC,IAC9C;AAEV,aACI,0BAAC,iBACG;AAAA,IAAC;AAAA;AAAA,MACG,cAAc,gBAAgB;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,cAAc,gBAAgB;AAAA,MAC9B,cAAcA;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA,wCAAC,kBAAgB,GAAG,MAAM;AAAA;AAAA,EAC9B,GACJ;AAER;AAEA,IAAM,iBAAiB,CAAC,UAAwB;AAC5C,QAAM,sBAAsB,eAAe;AAC3C,QAAM,EAAE,UAAU,GAAG,KAAK,IAAI;AAE9B,QAAM,CAAC,UAAU,WAAW,IAAU,gBAEpC,MAAM,OAAO;AACf,EAAM,iBAAU,MAAM;AAClB,QAAI,CAAC,YAAY,oBAAoB,SAAS,GAAG;AAC7C,cAAQ;AAAA,QACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WA+BL,oBACE;AAAA,UACG,SAAO;AAAA,8BACO,IAAI,IAAI,IAClB,IAAI,OAAO,wBAAwB,EACvC,GAAG,IAAI,OAAO,wBAAwB,EAAE,GACpC,IAAI,SAAS,4BAA4B,EAC7C,GAAG,IAAI,OAAO,wBAAwB,EAAE;AAAA,QAC5C,EACC,KAAK,EAAE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQT;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,qBAAqB,QAAQ,CAAC;AAElC,EAAM,iBAAU,MAAM;AAElB,QAAI,CAAC,YAAY,oBAAoB,SAAS,GAAG;AAC7C,kBAAY,MAAS;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,qBAAqB,QAAQ,CAAC;AAElC,QAAM,mBAAmB,oBAAoB,IAAI,4BAC7C,0BAAC,YAAwC,GAAG,sBAA7B,mBAAmB,IAA8B,CACnE;AAED,aACI;AAAA,IAAC;AAAA;AAAA,MACG,OAAO;AAAA,MACP,UAAU;AAAA,MACV,WAAW;AAAA,MACV,GAAG;AAAA,MAEH;AAAA,oBAAY;AAAA,YACb,2BAAC,gBAAa,UAAQ,MAClB;AAAA;AAAA,YAAC;AAAA;AAAA,cACG,MAAM,gBAAgB;AAAA,cACtB,aAAS,0BAAC,mBAAgB;AAAA;AAAA,UAC9B;AAAA,cACA;AAAA,YAAC;AAAA;AAAA,cACG,MAAM,mBAAmB;AAAA,cACzB,aAAS,0BAAC,sBAAmB;AAAA;AAAA,UACjC;AAAA,WACJ;AAAA;AAAA;AAAA,EACJ;AAER;", "names": ["compactQueue", "arrayToObject", "merge", "encode", "compact", "isRegExp", "<PERSON><PERSON><PERSON><PERSON>", "combine", "maybeMap", "isNonNullishPrimitive", "stringify", "value", "normalizeStringifyOptions", "normalizeParseOptions", "_a", "id", "__assign", "qs", "_a", "data", "changes", "isEqual", "import_react", "import_react", "LockIcon", "React", "import_inflection", "import_jsx_runtime", "React", "import_inflection", "import_jsx_runtime", "React", "import_inflection", "import_jsx_runtime", "props", "import_react", "React", "import_inflection", "import_jsx_runtime", "import_jsx_runtime", "Root", "PREFIX", "import_jsx_runtime", "import_jsx_runtime", "Root", "SupabaseLoginFormClasses", "PREFIX", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "SupabaseLoginFormClasses", "Root", "PREFIX", "import_jsx_runtime", "src_default", "React", "import_jsx_runtime", "defaultTheme"]}