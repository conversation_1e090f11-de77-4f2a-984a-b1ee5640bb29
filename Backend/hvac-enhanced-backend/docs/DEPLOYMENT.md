# 🚀 HVAC Enhanced Backend - Deployment Guide

## 📋 Prerequisites

### System Requirements
- Node.js 18+ 
- Docker & Docker Compose
- Git
- 4GB+ RAM
- 20GB+ disk space

### Required Services
- Supabase account (or PostgreSQL)
- Trigger.dev account
- OpenAI API key (optional)
- Email SMTP credentials

## 🔧 Environment Setup

### 1. <PERSON><PERSON> and Setup
```bash
cd /home/<USER>/HVAC/Foundations/Backend/hvac-enhanced-backend

# Copy environment file
cp .env.example .env

# Edit configuration
nano .env
```

### 2. Configure Environment Variables
```bash
# Server Configuration
PORT=5000
NODE_ENV=production

# Database URLs
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
MONGODB_URI=*************************************************
WEAVIATE_URL=http://localhost:8080
REDIS_URL=redis://localhost:6379

# Trigger.dev Configuration
TRIGGER_API_KEY=tr_dev_your_api_key
TRIGGER_PROJECT_ID=hvac-crm

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## 🐳 Docker Deployment

### Quick Start
```bash
# Start all services
./start.sh

# Or manually:
docker-compose up -d
npm run build
npm start
```

### Production Deployment
```bash
# Build production image
docker build -t hvac-enhanced-backend .

# Run with production compose
docker-compose -f docker-compose.prod.yml up -d
```

## ⚡ Trigger.dev Setup

### 1. Create Trigger.dev Account
- Visit https://trigger.dev
- Create new project: `hvac-crm`
- Copy API key to `.env`

### 2. Deploy Workflows
```bash
# Login to Trigger.dev
npx @trigger.dev/cli login

# Deploy workflows
npm run trigger:deploy
```

## 📧 Email Templates Setup

### 1. Build Templates
```bash
# Development preview
npm run email:dev

# Build for production
npm run email:build
```

### 2. Configure SMTP
```bash
# Gmail setup (recommended)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password  # Use App Password, not regular password
```

## 🗄️ Database Setup

### Supabase Setup
1. Create Supabase project
2. Run SQL migrations (if any)
3. Configure RLS policies
4. Copy connection details to `.env`

### Local Database Setup
```bash
# Start local databases
docker-compose up -d mongodb redis weaviate

# Verify connections
curl http://localhost:8080/v1/meta  # Weaviate
docker exec -it hvac-backend-mongodb-1 mongosh  # MongoDB
docker exec -it hvac-backend-redis-1 redis-cli ping  # Redis
```

## 🔍 Health Checks

### Verify Deployment
```bash
# Check application health
curl http://localhost:5000/api/health

# Expected response:
{
  "status": "healthy",
  "services": {
    "supabase": "healthy",
    "weaviate": "healthy", 
    "redis": "healthy",
    "triggerDev": "healthy"
  }
}
```

### Monitor Services
```bash
# View logs
docker-compose logs -f hvac-backend

# Check service status
docker-compose ps

# Monitor workflows
# Visit Trigger.dev dashboard
```

## 🔒 Security Configuration

### SSL/TLS Setup
```bash
# For production, use reverse proxy (nginx)
server {
    listen 443 ssl;
    server_name api.fulmark.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Firewall Rules
```bash
# Allow only necessary ports
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 5000   # Block direct access to app
```

## 📊 Monitoring & Logging

### Log Files
```bash
# Application logs
tail -f logs/hvac-backend.log

# Docker logs
docker-compose logs -f

# System logs
journalctl -u docker -f
```

### Performance Monitoring
- CPU/Memory usage via `htop`
- Disk usage via `df -h`
- Network via `netstat -tulpn`
- Application metrics via health endpoint

## 🔄 Updates & Maintenance

### Update Application
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Database Backups
```bash
# MongoDB backup
docker exec hvac-backend-mongodb-1 mongodump --out /backup

# Weaviate backup
curl -X POST http://localhost:8080/v1/backups/filesystem
```

## 🚨 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port
lsof -i :5000

# Kill process
kill -9 <PID>
```

#### Database Connection Failed
```bash
# Check service status
docker-compose ps

# Restart services
docker-compose restart mongodb redis weaviate
```

#### Email Sending Failed
```bash
# Test SMTP connection
telnet smtp.gmail.com 587

# Check Gmail App Password
# Enable 2FA and generate App Password
```

#### Trigger.dev Workflows Not Running
```bash
# Check API key
echo $TRIGGER_API_KEY

# Redeploy workflows
npm run trigger:deploy

# Check dashboard for errors
```

## 📞 Support

### Logs to Collect
- Application logs: `logs/hvac-backend.log`
- Docker logs: `docker-compose logs`
- System info: `docker-compose ps`
- Environment: `env | grep -E "(NODE_|TRIGGER_|SUPABASE_)"`

### Contact Information
- Technical Support: <EMAIL>
- Documentation: /docs/
- Issue Tracker: GitHub Issues

---

**🔥 HVAC Enhanced Backend - Production Ready! ⚡📧**