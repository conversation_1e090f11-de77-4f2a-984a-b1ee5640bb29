{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/email-templates/*": ["./email-templates/*"], "@/workflows/*": ["./workflows/*"], "@/services/*": ["./services/*"], "@/api/*": ["./api/*"], "@/config/*": ["./config/*"], "@/types/*": ["./types/*"], "@/utils/*": ["./utils/*"]}, "types": ["node", "vitest/globals"], "lib": ["ES2022"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}