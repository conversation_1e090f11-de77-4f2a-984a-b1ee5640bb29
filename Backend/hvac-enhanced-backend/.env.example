# Server Configuration
PORT=5000
NODE_ENV=development
API_VERSION=v1

# Database Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

MONGODB_URI=mongodb://localhost:27017/hvac_crm
WEAVIATE_URL=http://localhost:8080
REDIS_URL=redis://localhost:6379

# Trigger.dev Configuration
TRIGGER_API_KEY=your_trigger_api_key
TRIGGER_PROJECT_ID=hvac-crm
TRIGGER_ENVIRONMENT=development

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# React Email Configuration
REACT_EMAIL_PREVIEW_URL=http://localhost:3000/email-preview
EMAIL_TEMPLATE_CDN_URL=https://cdn.fulmark.com/email-assets

# AI Configuration
BIELIK_API_URL=http://localhost:11434
GEMMA_API_URL=http://localhost:11434
LM_STUDIO_URL=http://localhost:1234

# Security
JWT_SECRET=your_jwt_secret_key
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/hvac-backend.log

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:5173