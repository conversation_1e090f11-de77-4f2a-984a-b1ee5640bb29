{"name": "hvac-enhanced-backend", "version": "1.0.0", "description": "Enhanced HVAC CRM Backend with React Email + Trigger.dev Integration", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "email:dev": "email dev", "email:build": "email build", "trigger:dev": "trigger.dev dev", "trigger:deploy": "trigger.dev deploy"}, "dependencies": {"@trigger.dev/sdk": "^3.0.0", "@trigger.dev/express": "^3.0.0", "react-email": "^2.1.0", "@react-email/components": "^0.0.15", "@react-email/render": "^0.0.12", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.4.5", "zod": "^3.22.4", "winston": "^3.11.0", "@supabase/supabase-js": "^2.39.0", "mongodb": "^6.3.0", "weaviate-ts-client": "^1.5.0", "redis": "^4.6.12", "nodemailer": "^6.9.8", "multer": "^1.4.5-lts.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "rate-limiter-flexible": "^4.0.1"}, "devDependencies": {"@types/node": "^20.10.6", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "typescript": "^5.3.3", "tsx": "^4.7.0", "tsc-alias": "^1.8.8", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "vitest": "^1.1.3", "@vitest/coverage-v8": "^1.1.3", "supertest": "^6.3.4", "@types/supertest": "^6.0.2"}, "keywords": ["hvac", "crm", "react-email", "trigger.dev", "typescript", "express"], "author": "Fulmark HVAC Team", "license": "MIT"}