# 🔥 HVAC Enhanced Backend

Enhanced HVAC CRM Backend with React Email + Trigger.dev Integration

## 🚀 Features

### ✅ Implemented
- **React Email Integration**: Professional email templating system
- **Trigger.dev Workflows**: Enterprise-grade background job processing
- **Multi-Database Support**: Supabase, MongoDB, Weaviate, Redis
- **Authentication & Security**: JWT, rate limiting, CORS
- **Health Monitoring**: Comprehensive health checks
- **Logging**: Winston-based structured logging

### 🔄 In Progress
- Email template library expansion
- Advanced workflow automation
- AI processing pipelines
- Real-time monitoring dashboard

## 📁 Project Structure

```
src/
├── api/
│   ├── middleware/          # Authentication, rate limiting, error handling
│   └── routes/             # API endpoints
├── config/                 # Database and service configurations
├── email-templates/        # React Email templates
│   ├── components/         # Reusable email components
│   ├── layouts/           # Email layouts
│   └── styles/            # Email styling
├── workflows/             # Trigger.dev workflows
│   ├── email-processing/  # Email analysis and processing
│   ├── service-automation/# Service order automation
│   └── ai-processing/     # AI workflow automation
├── services/              # Business logic services
├── types/                 # TypeScript type definitions
└── utils/                 # Utility functions
```

## 🛠️ Installation

```bash
# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Edit .env with your configuration
nano .env

# Start development server
npm run dev
```

## 🔧 Environment Variables

```bash
# Server
PORT=5000
NODE_ENV=development

# Databases
SUPABASE_URL=your_supabase_url
MONGODB_URI=mongodb://localhost:27017/hvac_crm
WEAVIATE_URL=http://localhost:8080
REDIS_URL=redis://localhost:6379

# Trigger.dev
TRIGGER_API_KEY=your_trigger_api_key
TRIGGER_PROJECT_ID=hvac-crm

# Email
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## 📧 Email Templates

### Available Templates
- **Service Notification**: Appointment confirmations
- **Invoice Template**: Professional invoices
- **Maintenance Reminder**: Scheduled maintenance alerts
- **Quote Template**: Service estimates
- **Welcome Email**: Customer onboarding
- **Emergency Alert**: Urgent service notifications

### Usage Example
```typescript
import { render } from '@react-email/render';
import { ServiceNotification } from '@/email-templates';

const emailHtml = render(
  <ServiceNotification 
    customerName="Jan Kowalski"
    serviceDate="2024-01-15"
    technicianName="Tomasz Nowak"
    serviceType="Serwis klimatyzacji"
  />
);
```

## ⚡ Workflows

### Email Processing Workflow
- AI-powered email analysis
- Customer information extraction
- Attachment processing
- Automated responses

### Service Order Workflow
- Intelligent technician assignment
- Route optimization
- Customer notifications
- Resource preparation

## 🔍 API Endpoints

### Health Check
```
GET /api/health
```

### Email Templates
```
POST /api/email/send
GET  /api/email/templates
POST /api/email/templates/preview
```

### Workflows
```
GET  /api/workflow/status
POST /api/workflow/trigger
GET  /api/workflow/history
```

## 🚀 Deployment

```bash
# Build for production
npm run build

# Start production server
npm start

# Deploy workflows to Trigger.dev
npm run trigger:deploy
```

## 📊 Monitoring

- Health checks at `/api/health`
- Workflow monitoring via Trigger.dev dashboard
- Structured logging with Winston
- Performance metrics tracking

## 🔒 Security

- JWT authentication
- Rate limiting (100 requests/15 minutes)
- CORS protection
- Helmet security headers
- Input validation with Zod

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage
```

## 📝 Development

```bash
# Start development server with hot reload
npm run dev

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Preview email templates
npm run email:dev
```

## 🎯 Next Steps

1. **Phase 1**: Complete React Email template library
2. **Phase 2**: Implement advanced Trigger.dev workflows
3. **Phase 3**: Add Effect-TS for enhanced error handling
4. **Phase 4**: Integrate React Email Editor for marketing

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

MIT License - see LICENSE file for details

---

**🔥 Kosmiczna Kuźnia Backend - Ready for Production! ⚡📧**