#!/bin/bash

echo "🔥 Starting HVAC Enhanced Backend..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Build the project
echo "🔨 Building project..."
npm run build

# Start services with Docker Compose
echo "🐳 Starting Docker services..."
docker-compose up -d redis mongodb weaviate

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Start the application
echo "🚀 Starting HVAC Enhanced Backend..."
npm run dev

echo "✅ HVAC Enhanced Backend is running!"
echo "📧 Email templates: http://localhost:5000/api/email"
echo "⚡ Workflows: http://localhost:5000/api/workflow"
echo "🔍 Health check: http://localhost:5000/api/health"