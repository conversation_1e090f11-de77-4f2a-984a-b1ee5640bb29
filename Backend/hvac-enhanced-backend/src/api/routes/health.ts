import { Router } from 'express';
import { supabase, weaviateClient, redisClient } from '@/config/database';
import { logger } from '@/utils/logger';

const router = Router();

router.get('/', async (req, res) => {
  try {
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        supabase: 'unknown',
        weaviate: 'unknown',
        redis: 'unknown',
        triggerDev: 'unknown',
      },
    };

    // Check Supabase
    try {
      const { data, error } = await supabase.from('customers').select('count').limit(1);
      healthCheck.services.supabase = error ? 'unhealthy' : 'healthy';
    } catch (error) {
      healthCheck.services.supabase = 'unhealthy';
    }

    // Check Weaviate
    try {
      await weaviateClient.misc.liveChecker().do();
      healthCheck.services.weaviate = 'healthy';
    } catch (error) {
      healthCheck.services.weaviate = 'unhealthy';
    }

    // Check Redis
    try {
      await redisClient.ping();
      healthCheck.services.redis = 'healthy';
    } catch (error) {
      healthCheck.services.redis = 'unhealthy';
    }

    // Check if any service is unhealthy
    const isUnhealthy = Object.values(healthCheck.services).includes('unhealthy');
    
    if (isUnhealthy) {
      healthCheck.status = 'degraded';
      logger.warn('⚠️ Health check shows degraded services', healthCheck.services);
    }

    res.status(isUnhealthy ? 503 : 200).json(healthCheck);
  } catch (error) {
    logger.error('❌ Health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
    });
  }
});

export default router;