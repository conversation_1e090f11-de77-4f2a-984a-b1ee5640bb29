/**
 * 🌟 COSMIC WELCOME EMAIL TEMPLATE
 * Najwspanialszy email powitalny dla nowych klientów HVAC
 */

import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Row,
  Column,
  Heading,
  Text,
  Button,
  Img,
  Hr,
  Link,
} from '@react-email/components';

interface WelcomeEmailProps {
  customerName: string;
  customerEmail: string;
  customerType: 'individual' | 'business';
  assignedConsultant?: {
    name: string;
    phone: string;
    email: string;
    photo?: string;
  };
  nextSteps?: string[];
  cosmicTheme?: boolean;
}

export const WelcomeEmail = ({
  customerName,
  customerEmail,
  customerType = 'individual',
  assignedConsultant,
  nextSteps = [],
  cosmicTheme = true,
}: WelcomeEmailProps) => {
  const primaryColor = cosmicTheme ? '#1890ff' : '#0066cc';
  const accentColor = cosmicTheme ? '#fa8c16' : '#ff6600';
  const backgroundColor = cosmicTheme ? '#f0f8ff' : '#ffffff';

  return (
    <Html>
      <Head>
        <title>Witamy w Fulmark HVAC - Twój partner w klimatyzacji</title>
      </Head>
      <Body style={{ backgroundColor, fontFamily: 'Arial, sans-serif' }}>
        <Container style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
          
          {/* Header */}
          <Section style={{ 
            backgroundColor: primaryColor, 
            padding: '30px 20px', 
            borderRadius: '10px 10px 0 0',
            textAlign: 'center'
          }}>
            <Img 
              src="https://fulmark.com/logo-white.png" 
              alt="Fulmark HVAC" 
              width="200" 
              height="60"
              style={{ margin: '0 auto' }}
            />
            <Heading style={{ 
              color: '#ffffff', 
              fontSize: '28px', 
              margin: '20px 0 10px 0',
              fontWeight: 'bold'
            }}>
              Witamy w rodzinie Fulmark! 🌟
            </Heading>
            <Text style={{ 
              color: '#ffffff', 
              fontSize: '16px', 
              margin: '0',
              opacity: 0.9
            }}>
              Profesjonalne usługi klimatyzacyjne na najwyższym poziomie
            </Text>
          </Section>

          {/* Main Content */}
          <Section style={{ 
            backgroundColor: '#ffffff', 
            padding: '40px 30px',
            borderLeft: `1px solid #e0e0e0`,
            borderRight: `1px solid #e0e0e0`
          }}>
            <Heading style={{ 
              color: '#333333', 
              fontSize: '24px', 
              margin: '0 0 20px 0' 
            }}>
              Dzień dobry {customerName}! 👋
            </Heading>

            <Text style={{ 
              color: '#666666', 
              fontSize: '16px', 
              lineHeight: '1.6',
              margin: '0 0 20px 0'
            }}>
              Dziękujemy za zaufanie i wybór Fulmark HVAC jako Twojego partnera w dziedzinie 
              klimatyzacji i wentylacji. Jesteśmy dumni, że możemy dołączyć do grona naszych 
              {customerType === 'business' ? ' partnerów biznesowych' : ' zadowolonych klientów'}.
            </Text>

            <Text style={{ 
              color: '#666666', 
              fontSize: '16px', 
              lineHeight: '1.6',
              margin: '0 0 30px 0'
            }}>
              Nasz zespół ekspertów jest gotowy, aby zapewnić Ci najwyższą jakość usług, 
              od konsultacji i projektowania, przez instalację, aż po serwis i konserwację.
            </Text>

            {/* Assigned Consultant */}
            {assignedConsultant && (
              <Section style={{ 
                backgroundColor: '#f8f9fa', 
                padding: '25px', 
                borderRadius: '8px',
                border: `2px solid ${accentColor}`,
                margin: '0 0 30px 0'
              }}>
                <Heading style={{ 
                  color: '#333333', 
                  fontSize: '18px', 
                  margin: '0 0 15px 0' 
                }}>
                  Twój dedykowany konsultant 🎯
                </Heading>
                
                <Row>
                  {assignedConsultant.photo && (
                    <Column style={{ width: '80px', verticalAlign: 'top' }}>
                      <Img 
                        src={assignedConsultant.photo} 
                        alt={assignedConsultant.name}
                        width="60"
                        height="60"
                        style={{ borderRadius: '50%' }}
                      />
                    </Column>
                  )}
                  <Column style={{ verticalAlign: 'top' }}>
                    <Text style={{ 
                      color: '#333333', 
                      fontSize: '16px', 
                      fontWeight: 'bold',
                      margin: '0 0 5px 0'
                    }}>
                      {assignedConsultant.name}
                    </Text>
                    <Text style={{ 
                      color: '#666666', 
                      fontSize: '14px',
                      margin: '0 0 5px 0'
                    }}>
                      📞 {assignedConsultant.phone}
                    </Text>
                    <Text style={{ 
                      color: '#666666', 
                      fontSize: '14px',
                      margin: '0'
                    }}>
                      ✉️ {assignedConsultant.email}
                    </Text>
                  </Column>
                </Row>
              </Section>
            )}

            {/* Next Steps */}
            {nextSteps.length > 0 && (
              <Section style={{ margin: '0 0 30px 0' }}>
                <Heading style={{ 
                  color: '#333333', 
                  fontSize: '18px', 
                  margin: '0 0 15px 0' 
                }}>
                  Następne kroki 📋
                </Heading>
                
                {nextSteps.map((step, index) => (
                  <Text key={index} style={{ 
                    color: '#666666', 
                    fontSize: '14px',
                    margin: '0 0 8px 0',
                    paddingLeft: '20px',
                    position: 'relative'
                  }}>
                    <span style={{ 
                      position: 'absolute', 
                      left: '0', 
                      color: accentColor,
                      fontWeight: 'bold'
                    }}>
                      {index + 1}.
                    </span>
                    {step}
                  </Text>
                ))}
              </Section>
            )}

            {/* CTA Buttons */}
            <Section style={{ textAlign: 'center', margin: '30px 0' }}>
              <Button
                href={`https://fulmark.com/portal?email=${customerEmail}`}
                style={{
                  backgroundColor: primaryColor,
                  color: '#ffffff',
                  padding: '15px 30px',
                  borderRadius: '8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  fontSize: '16px',
                  margin: '0 10px 10px 0',
                  display: 'inline-block'
                }}
              >
                🏠 Portal Klienta
              </Button>
              
              <Button
                href="https://fulmark.com/contact"
                style={{
                  backgroundColor: accentColor,
                  color: '#ffffff',
                  padding: '15px 30px',
                  borderRadius: '8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  fontSize: '16px',
                  margin: '0 10px 10px 0',
                  display: 'inline-block'
                }}
              >
                📞 Skontaktuj się
              </Button>
            </Section>
          </Section>

          {/* Footer */}
          <Section style={{ 
            backgroundColor: '#f8f9fa', 
            padding: '30px 20px',
            borderRadius: '0 0 10px 10px',
            textAlign: 'center',
            borderLeft: `1px solid #e0e0e0`,
            borderRight: `1px solid #e0e0e0`,
            borderBottom: `1px solid #e0e0e0`
          }}>
            <Text style={{ 
              color: '#666666', 
              fontSize: '14px',
              margin: '0 0 15px 0'
            }}>
              Fulmark HVAC - Twój partner w klimatyzacji
            </Text>
            
            <Text style={{ 
              color: '#999999', 
              fontSize: '12px',
              margin: '0 0 10px 0'
            }}>
              ul. Przykładowa 123, 00-001 Warszawa<br/>
              Tel: +48 123 456 789 | Email: <EMAIL>
            </Text>
            
            <Hr style={{ margin: '20px 0', borderColor: '#e0e0e0' }} />
            
            <Text style={{ 
              color: '#999999', 
              fontSize: '11px',
              margin: '0'
            }}>
              Ten email został wysłany do {customerEmail}.<br/>
              <Link href="https://fulmark.com/unsubscribe" style={{ color: '#999999' }}>
                Wypisz się z listy
              </Link> | 
              <Link href="https://fulmark.com/privacy" style={{ color: '#999999' }}>
                Polityka prywatności
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};