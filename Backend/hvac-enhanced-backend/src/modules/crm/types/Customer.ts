/**
 * 🌟 COSMIC CRM CUSTOMER TYPES
 * Najwspanialsze definicje typów dla zarządzania klientami HVAC
 */

export interface CosmicCustomer {
  id: string;
  
  // Basic Information
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    alternativePhone?: string;
    dateOfBirth?: string;
    preferredLanguage: 'pl' | 'en';
  };
  
  // Address Information
  address: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
    accessInstructions?: string;
  };
  
  // Business Information (for B2B customers)
  businessInfo?: {
    companyName: string;
    nip: string;
    regon?: string;
    industry: string;
    employeeCount?: number;
    annualRevenue?: number;
  };
  
  // Customer Segmentation
  segmentation: {
    type: 'individual' | 'business' | 'vip' | 'enterprise';
    category: 'residential' | 'commercial' | 'industrial';
    priority: 'low' | 'medium' | 'high' | 'critical';
    lifetimeValue: number;
    acquisitionChannel: string;
  };
  
  // AI-Powered Analytics
  aiAnalytics: {
    sentimentScore: number; // -1 to 1
    satisfactionLevel: 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
    churnProbability: number; // 0 to 1
    upsellPotential: number; // 0 to 1
    communicationPreferences: {
      preferredChannel: 'email' | 'phone' | 'sms' | 'whatsapp';
      preferredTime: 'morning' | 'afternoon' | 'evening';
      frequency: 'low' | 'medium' | 'high';
    };
  };
}  // Interaction History
  interactions: CustomerInteraction[];
  
  // Equipment & Services
  equipment: CustomerEquipment[];
  serviceHistory: ServiceRecord[];
  
  // Financial Information
  financial: {
    creditRating?: 'excellent' | 'good' | 'fair' | 'poor';
    paymentTerms: number; // days
    totalSpent: number;
    averageOrderValue: number;
    outstandingBalance: number;
    lastPaymentDate?: string;
  };
  
  // Metadata
  metadata: {
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    lastContactDate?: string;
    nextFollowUpDate?: string;
    tags: string[];
    notes: CustomerNote[];
    source: 'website' | 'referral' | 'cold_call' | 'marketing' | 'social_media';
  };
}

export interface CustomerInteraction {
  id: string;
  type: 'email' | 'phone' | 'meeting' | 'service_visit' | 'complaint' | 'inquiry';
  direction: 'inbound' | 'outbound';
  subject: string;
  content: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  outcome: string;
  followUpRequired: boolean;
  followUpDate?: string;
  attachments?: string[];
  duration?: number; // minutes
  createdAt: string;
  createdBy: string;
}

export interface CustomerEquipment {
  id: string;
  brand: string;
  model: string;
  serialNumber: string;
  installationDate: string;
  warrantyExpiry?: string;
  location: string; // room/area description
  specifications: Record<string, any>;
  healthScore: number; // 0 to 100
  lastServiceDate?: string;
  nextServiceDue?: string;
}