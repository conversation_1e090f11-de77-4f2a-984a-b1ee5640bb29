/**
 * 🌟 COSMIC CUSTOMER ONBOARDING WORKFLOW
 * Najwspanialszy workflow onboardingu klientów z AI automation
 */

import { TriggerClient, eventTrigger } from "@trigger.dev/sdk";
import { z } from "zod";
import { render } from '@react-email/render';
import { logger } from '@/utils/logger';
import { WelcomeEmail } from '../email-templates/WelcomeEmail';
import { CosmicCRMService } from '../services/CosmicCRMService';

const client = new TriggerClient({
  id: "hvac-crm",
  apiKey: process.env.TRIGGER_API_KEY!,
});

// Customer onboarding schema
const customerOnboardingSchema = z.object({
  customerId: z.string(),
  customerType: z.enum(['individual', 'business']),
  priority: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  source: z.string().optional(),
  assignConsultant: z.boolean().optional(),
});

export const customerOnboardingWorkflow = client.defineJob({
  id: "customer-onboarding",
  name: "Cosmic Customer Onboarding Workflow",
  version: "1.0.0",
  trigger: eventTrigger({
    name: "customer.created",
    schema: customerOnboardingSchema,
  }),
  run: async (payload, io, ctx) => {
    logger.info(`🌟 Starting cosmic onboarding for customer: ${payload.customerId}`);

    // Step 1: Fetch customer details
    const customer = await io.runTask("fetch-customer", async () => {
      logger.info(`📋 Fetching customer details`);
      
      // Simulate customer fetch - replace with actual CRM service call
      return {
        id: payload.customerId,
        personalInfo: {
          firstName: 'Jan',
          lastName: 'Kowalski',
          email: '<EMAIL>',
          phone: '+**************',
          preferredLanguage: 'pl',
        },
        segmentation: {
          type: payload.customerType,
          category: 'residential',
          priority: payload.priority || 'medium',
          lifetimeValue: 0,
          acquisitionChannel: payload.source || 'website',
        },
        address: {
          street: 'ul. Testowa 123',
          city: 'Warszawa',
          postalCode: '00-001',
          country: 'Polska',
        },
      };
    });

    // Step 2: AI-powered customer analysis
    const customerAnalysis = await io.runTask("ai-customer-analysis", async () => {
      logger.info(`🤖 Performing AI analysis of customer profile`);
      
      // Simulate AI analysis with Bielik V3
      const analysis = {
        customerProfile: {
          communicationStyle: 'professional',
          decisionMaking: 'analytical',
          pricesSensitivity: 'medium',
          servicePreferences: ['quality', 'reliability', 'warranty'],
        },
        recommendedApproach: {
          communicationChannel: 'email',
          followUpTiming: '24-48 hours',
          consultationStyle: 'detailed',
          focusAreas: ['energy efficiency', 'cost savings', 'comfort'],
        },
        riskAssessment: {
          churnRisk: 0.15,
          paymentRisk: 0.1,
          satisfactionPotential: 0.85,
        },
        upsellOpportunities: [
          'Premium maintenance plan',
          'Smart thermostat integration',
          'Air quality monitoring',
        ],
      };
      
      return analysis;
    });

    // Step 3: Assign dedicated consultant (for high-value customers)
    let assignedConsultant = null;
    if (payload.assignConsultant || customer.segmentation.priority === 'high' || customer.segmentation.priority === 'critical') {
      assignedConsultant = await io.runTask("assign-consultant", async () => {
        logger.info(`👨‍💼 Assigning dedicated consultant`);
        
        // AI-powered consultant matching
        const consultant = {
          id: 'consultant_123',
          name: 'Tomasz Nowak',
          email: '<EMAIL>',
          phone: '+48 987 654 321',
          photo: 'https://fulmark.com/team/tomasz-nowak.jpg',
          specializations: ['residential', 'energy_efficiency'],
          languages: ['pl', 'en'],
          rating: 4.9,
          availability: 'immediate',
        };
        
        // Update customer record with assigned consultant
        logger.info(`✅ Consultant assigned: ${consultant.name}`);
        return consultant;
      });
    }

    // Step 4: Generate personalized welcome email
    const welcomeEmail = await io.runTask("generate-welcome-email", async () => {
      logger.info(`📧 Generating personalized welcome email`);
      
      const nextSteps = [
        'Skontaktujemy się w ciągu 24 godzin w celu umówienia bezpłatnej konsultacji',
        'Przygotujemy spersonalizowaną ofertę dostosowaną do Twoich potrzeb',
        'Zaplanujemy wizytę techniczną w dogodnym dla Ciebie terminie',
      ];

      if (customer.segmentation.type === 'business') {
        nextSteps.push('Omówimy możliwości finansowania i warunki współpracy B2B');
      }

      const emailHtml = render(
        WelcomeEmail({
          customerName: customer.personalInfo.firstName,
          customerEmail: customer.personalInfo.email,
          customerType: customer.segmentation.type,
          assignedConsultant,
          nextSteps,
          cosmicTheme: true,
        })
      );
      
      return {
        to: customer.personalInfo.email,
        subject: `Witamy w Fulmark HVAC, ${customer.personalInfo.firstName}! 🌟`,
        html: emailHtml,
        template: 'welcome_email',
      };
    });

    // Step 5: Send welcome email
    const emailSent = await io.runTask("send-welcome-email", async () => {
      logger.info(`📤 Sending welcome email to ${customer.personalInfo.email}`);
      
      // Simulate email sending - replace with actual email service
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      logger.info(`✅ Welcome email sent successfully`);
      return {
        messageId: `msg_${Date.now()}`,
        status: 'sent',
        sentAt: new Date().toISOString(),
      };
    });

    // Step 6: Schedule follow-up tasks
    const followUpTasks = await io.runTask("schedule-follow-up", async () => {
      logger.info(`📅 Scheduling follow-up tasks`);
      
      const tasks = [
        {
          type: 'consultation_call',
          scheduledFor: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
          assignedTo: assignedConsultant?.id || 'sales_team',
          priority: customer.segmentation.priority,
          description: 'Initial consultation call to understand customer needs',
        },
        {
          type: 'follow_up_email',
          scheduledFor: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days
          assignedTo: 'marketing_automation',
          priority: 'medium',
          description: 'Follow-up email with additional resources and tips',
        },
      ];

      if (customer.segmentation.priority === 'high' || customer.segmentation.priority === 'critical') {
        tasks.push({
          type: 'priority_review',
          scheduledFor: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours
          assignedTo: 'sales_manager',
          priority: 'high',
          description: 'Priority customer review and immediate action plan',
        });
      }
      
      // Schedule tasks in external system
      for (const task of tasks) {
        logger.info(`📋 Scheduled task: ${task.type} for ${task.scheduledFor}`);
      }
      
      return tasks;
    });

    // Step 7: Update customer analytics
    await io.runTask("update-customer-analytics", async () => {
      logger.info(`📊 Updating customer analytics and segmentation`);
      
      // Update customer profile with onboarding data
      const updates = {
        onboardingCompleted: true,
        onboardingDate: new Date().toISOString(),
        assignedConsultantId: assignedConsultant?.id,
        aiAnalysis: customerAnalysis,
        nextFollowUpDate: followUpTasks[0].scheduledFor,
        tags: ['onboarded', 'new_customer', customer.segmentation.type],
      };
      
      logger.info(`✅ Customer analytics updated`);
      return updates;
    });

    // Step 8: Trigger additional workflows based on customer type
    if (customer.segmentation.type === 'business') {
      await io.runTask("trigger-b2b-workflow", async () => {
        logger.info(`🏢 Triggering B2B-specific workflow`);
        
        // Trigger B2B onboarding workflow
        await client.sendEvent({
          name: 'customer.b2b.onboarding',
          payload: {
            customerId: payload.customerId,
            consultantId: assignedConsultant?.id,
          },
        });
        
        return { triggered: true };
      });
    }

    logger.info(`🎉 Cosmic onboarding completed for customer: ${payload.customerId}`);

    return {
      success: true,
      customerId: payload.customerId,
      onboardingCompleted: true,
      emailSent: emailSent.status === 'sent',
      consultantAssigned: !!assignedConsultant,
      followUpTasksScheduled: followUpTasks.length,
      customerAnalysis: customerAnalysis.customerProfile,
      nextSteps: followUpTasks.map(task => ({
        type: task.type,
        scheduledFor: task.scheduledFor,
      })),
      processingTime: Date.now() - ctx.run.startedAt.getTime(),
    };
  },
});

export default customerOnboardingWorkflow;