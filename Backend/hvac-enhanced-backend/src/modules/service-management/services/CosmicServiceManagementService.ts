/**
 * 🔧 COSMIC SERVICE MANAGEMENT SERVICE
 * Najwspanialszy serwis zarządzania usługami HVAC z AI optimization
 */

import { TriggerClient } from '@trigger.dev/sdk';
import { supabase, weaviateClient } from '@/config/database';
import { logger } from '@/utils/logger';
import { CosmicServiceOrder } from '../types/ServiceOrder';

export class CosmicServiceManagementService {
  private triggerClient: TriggerClient;

  constructor(triggerClient: TriggerClient) {
    this.triggerClient = triggerClient;
  }

  /**
   * 🎯 Create new service order with AI-powered optimization
   */
  async createServiceOrder(orderData: Partial<CosmicServiceOrder>): Promise<CosmicServiceOrder> {
    try {
      logger.info('🔧 Creating new cosmic service order');

      // AI-powered order analysis and optimization
      const aiInsights = await this.analyzeServiceOrderWithAI(orderData);

      // Intelligent technician assignment
      const assignment = await this.optimizeTechnicianAssignment(orderData, aiInsights);

      // Smart scheduling optimization
      const optimizedScheduling = await this.optimizeScheduling(orderData, assignment);

      // Create service order with enhanced data
      const serviceOrder: CosmicServiceOrder = {
        id: this.generateCosmicOrderId(),
        orderInfo: {
          orderNumber: this.generateOrderNumber(),
          ...orderData.orderInfo!,
        },
        customer: orderData.customer!,
        location: orderData.location!,
        equipment: orderData.equipment || [],
        scheduling: optimizedScheduling,
        assignment,
        aiInsights,
        financial: {
          estimatedCost: aiInsights.estimatedCost.total,
          paymentStatus: 'pending',
        },
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'system',
          lastModifiedBy: 'system',
          version: 1,
          tags: this.generateSmartTags(orderData, aiInsights),
          attachments: [],
          communicationHistory: [],
        },
      } as CosmicServiceOrder;

      // Save to Supabase
      const { data, error } = await supabase
        .from('cosmic_service_orders')
        .insert(serviceOrder)
        .select()
        .single();

      if (error) throw error;

      // Index in Weaviate for intelligent search
      await this.indexServiceOrderInWeaviate(serviceOrder);

      // Trigger service order workflow
      await this.triggerClient.sendEvent({
        name: 'service.order.created',
        payload: {
          serviceOrderId: serviceOrder.id,
          priority: serviceOrder.orderInfo.priority,
          type: serviceOrder.orderInfo.type,
        },
      });

      logger.info(`✅ Cosmic service order created: ${serviceOrder.id}`);
      return serviceOrder;
    } catch (error) {
      logger.error('❌ Failed to create cosmic service order:', error);
      throw error;
    }
  }

  /**
   * 🤖 AI-powered service order analysis
   */
  private async analyzeServiceOrderWithAI(orderData: Partial<CosmicServiceOrder>) {
    logger.info('🤖 Analyzing service order with AI');

    // Simulate AI analysis - replace with actual Bielik V3/Gemma3 calls
    const complexityScore = this.calculateComplexityScore(orderData);
    
    return {
      complexityScore,
      estimatedCost: {
        labor: complexityScore * 150, // PLN per complexity point
        parts: complexityScore * 100,
        total: complexityScore * 250,
        confidence: 0.85,
      },
      riskFactors: [
        {
          type: 'technical' as const,
          description: 'Potential compatibility issues with older equipment',
          severity: 'medium' as const,
          probability: 0.3,
          mitigation: 'Verify equipment specifications before visit',
          impact: 'May require additional parts or extended service time',
        },
      ],
      recommendations: [
        'Schedule during optimal weather conditions',
        'Bring backup parts for common failure points',
        'Allow extra time for potential complications',
      ],
      similarCases: ['order_123', 'order_456'],
      predictedIssues: [
        {
          description: 'Refrigerant leak detection',
          probability: 0.25,
          estimatedCost: 300,
          preventionSteps: ['Pressure test before service', 'Visual inspection'],
          urgency: 'medium' as const,
        },
      ],
      optimizationSuggestions: [
        'Combine with nearby service calls for efficiency',
        'Schedule during technician\'s optimal route',
        'Prepare customer communication templates',
      ],
    };
  }

  /**
   * 👨‍🔧 Intelligent technician assignment
   */
  private async optimizeTechnicianAssignment(orderData: Partial<CosmicServiceOrder>, aiInsights: any) {
    logger.info('👨‍🔧 Optimizing technician assignment');

    // AI-powered technician matching
    const skillsRequired = this.extractRequiredSkills(orderData);
    const availableTechnicians = await this.getAvailableTechnicians(orderData.scheduling?.scheduledDate);
    
    // Simulate AI optimization
    const bestMatch = {
      primaryTechnicianId: 'tech_001',
      skillsRequired,
      assignmentMethod: 'ai_optimized' as const,
      assignedAt: new Date().toISOString(),
      assignedBy: 'ai_system',
    };

    return bestMatch;
  }

  /**
   * 📅 Smart scheduling optimization
   */
  private async optimizeScheduling(orderData: Partial<CosmicServiceOrder>, assignment: any) {
    logger.info('📅 Optimizing service scheduling');

    // AI-powered scheduling optimization
    const currentScheduling = orderData.scheduling!;
    
    // Optimize based on:
    // - Technician availability and location
    // - Customer preferences
    // - Route optimization
    // - Weather conditions
    // - Equipment availability
    
    return {
      ...currentScheduling,
      scheduledDate: currentScheduling.scheduledDate || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      estimatedDuration: Math.max(currentScheduling.estimatedDuration, 60), // minimum 1 hour
      timeWindow: currentScheduling.timeWindow || {
        start: '09:00',
        end: '17:00',
      },
      flexibility: currentScheduling.flexibility || 'flexible',
    };
  }

  /**
   * 🏷️ Generate smart tags based on AI analysis
   */
  private generateSmartTags(orderData: Partial<CosmicServiceOrder>, aiInsights: any): string[] {
    const tags = [];
    
    // Add type-based tags
    if (orderData.orderInfo?.type) {
      tags.push(orderData.orderInfo.type);
    }
    
    // Add priority tags
    if (orderData.orderInfo?.priority) {
      tags.push(`priority_${orderData.orderInfo.priority}`);
    }
    
    // Add complexity tags
    if (aiInsights.complexityScore > 7) {
      tags.push('high_complexity');
    } else if (aiInsights.complexityScore > 4) {
      tags.push('medium_complexity');
    } else {
      tags.push('low_complexity');
    }
    
    // Add equipment tags
    if (orderData.equipment) {
      orderData.equipment.forEach(eq => {
        tags.push(`equipment_${eq.type}`);
        tags.push(`brand_${eq.brand.toLowerCase()}`);
      });
    }
    
    // Add location tags
    if (orderData.location?.buildingType) {
      tags.push(`building_${orderData.location.buildingType}`);
    }
    
    return tags;
  }

  /**
   * 📇 Index service order in Weaviate
   */
  private async indexServiceOrderInWeaviate(serviceOrder: CosmicServiceOrder) {
    try {
      await weaviateClient.data
        .creator()
        .withClassName('HVACServiceOrder')
        .withId(serviceOrder.id)
        .withProperties({
          orderNumber: serviceOrder.orderInfo.orderNumber,
          type: serviceOrder.orderInfo.type,
          priority: serviceOrder.orderInfo.priority,
          description: serviceOrder.orderInfo.description,
          customerName: serviceOrder.customer.contactPerson,
          address: serviceOrder.location.address,
          equipment: serviceOrder.equipment.map(eq => `${eq.brand} ${eq.model}`).join(', '),
          tags: serviceOrder.metadata.tags.join(', '),
        })
        .do();

      logger.info(`📇 Service order indexed in Weaviate: ${serviceOrder.id}`);
    } catch (error) {
      logger.warn('Failed to index service order in Weaviate:', error);
    }
  }

  private generateCosmicOrderId(): string {
    return `cosmic_order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateOrderNumber(): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `FH-${year}${month}${day}-${random}`;
  }

  private calculateComplexityScore(orderData: Partial<CosmicServiceOrder>): number {
    let score = 1;
    
    // Base complexity by type
    const typeComplexity = {
      'consultation': 1,
      'inspection': 2,
      'maintenance': 3,
      'repair': 5,
      'installation': 7,
      'emergency': 8,
    };
    
    if (orderData.orderInfo?.type) {
      score = typeComplexity[orderData.orderInfo.type] || 3;
    }
    
    // Adjust for equipment count
    if (orderData.equipment) {
      score += Math.min(orderData.equipment.length * 0.5, 2);
    }
    
    // Adjust for building type
    if (orderData.location?.buildingType === 'industrial') {
      score += 2;
    } else if (orderData.location?.buildingType === 'commercial') {
      score += 1;
    }
    
    return Math.min(Math.max(score, 1), 10);
  }

  private extractRequiredSkills(orderData: Partial<CosmicServiceOrder>): string[] {
    const skills = ['hvac_basic'];
    
    if (orderData.orderInfo?.type === 'installation') {
      skills.push('installation', 'electrical');
    }
    
    if (orderData.orderInfo?.type === 'repair') {
      skills.push('diagnostics', 'troubleshooting');
    }
    
    if (orderData.equipment) {
      orderData.equipment.forEach(eq => {
        skills.push(`${eq.type}_specialist`);
        skills.push(`${eq.brand.toLowerCase()}_certified`);
      });
    }
    
    return [...new Set(skills)];
  }

  private async getAvailableTechnicians(scheduledDate?: string) {
    // Simulate technician availability check
    return [
      { id: 'tech_001', name: 'Jan Kowalski', skills: ['hvac_basic', 'installation'], rating: 4.8 },
      { id: 'tech_002', name: 'Anna Nowak', skills: ['hvac_basic', 'repair'], rating: 4.9 },
    ];
  }
}