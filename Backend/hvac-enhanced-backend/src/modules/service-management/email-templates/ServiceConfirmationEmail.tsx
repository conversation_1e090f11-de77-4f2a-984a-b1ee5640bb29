/**
 * 🔧 COSMIC SERVICE CONFIRMATION EMAIL
 * Najwspanialszy email potwierdzenia wizyty serwisowej
 */

import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Row,
  Column,
  Heading,
  Text,
  Button,
  Img,
  Hr,
  Link,
} from '@react-email/components';

interface ServiceConfirmationEmailProps {
  customerName: string;
  orderNumber: string;
  serviceType: string;
  scheduledDate: string;
  timeWindow: {
    start: string;
    end: string;
  };
  technician: {
    name: string;
    phone: string;
    photo?: string;
    specializations: string[];
  };
  address: string;
  estimatedDuration: number;
  estimatedCost?: number;
  equipment: Array<{
    brand: string;
    model: string;
    location: string;
  }>;
  preparationSteps?: string[];
  emergencyContact: string;
  cosmicTheme?: boolean;
}

export const ServiceConfirmationEmail = ({
  customerName,
  orderNumber,
  serviceType,
  scheduledDate,
  timeWindow,
  technician,
  address,
  estimatedDuration,
  estimatedCost,
  equipment,
  preparationSteps = [],
  emergencyContact,
  cosmicTheme = true,
}: ServiceConfirmationEmailProps) => {
  const primaryColor = cosmicTheme ? '#1890ff' : '#0066cc';
  const accentColor = cosmicTheme ? '#fa8c16' : '#ff6600';
  const successColor = '#52c41a';
  const backgroundColor = cosmicTheme ? '#f0f8ff' : '#ffffff';

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getServiceTypeIcon = (type: string) => {
    const icons = {
      'installation': '🔧',
      'maintenance': '⚙️',
      'repair': '🛠️',
      'inspection': '🔍',
      'emergency': '🚨',
      'consultation': '💬',
    };
    return icons[type as keyof typeof icons] || '🔧';
  };

  return (
    <Html>
      <Head>
        <title>Potwierdzenie wizyty serwisowej - Fulmark HVAC</title>
      </Head>
      <Body style={{ backgroundColor, fontFamily: 'Arial, sans-serif' }}>
        <Container style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
          
          {/* Header */}
          <Section style={{ 
            backgroundColor: primaryColor, 
            padding: '30px 20px', 
            borderRadius: '10px 10px 0 0',
            textAlign: 'center'
          }}>
            <Img 
              src="https://fulmark.com/logo-white.png" 
              alt="Fulmark HVAC" 
              width="180" 
              height="50"
              style={{ margin: '0 auto' }}
            />
            <Heading style={{ 
              color: '#ffffff', 
              fontSize: '26px', 
              margin: '20px 0 10px 0',
              fontWeight: 'bold'
            }}>
              Wizyta potwierdzona! {getServiceTypeIcon(serviceType)}
            </Heading>
            <Text style={{ 
              color: '#ffffff', 
              fontSize: '16px', 
              margin: '0',
              opacity: 0.9
            }}>
              Zlecenie #{orderNumber}
            </Text>
          </Section>

          {/* Main Content */}
          <Section style={{ 
            backgroundColor: '#ffffff', 
            padding: '30px',
            borderLeft: `1px solid #e0e0e0`,
            borderRight: `1px solid #e0e0e0`
          }}>
            <Heading style={{ 
              color: '#333333', 
              fontSize: '22px', 
              margin: '0 0 20px 0' 
            }}>
              Dzień dobry {customerName}! 👋
            </Heading>

            <Text style={{ 
              color: '#666666', 
              fontSize: '16px', 
              lineHeight: '1.6',
              margin: '0 0 25px 0'
            }}>
              Potwierdzamy umówioną wizytę serwisową. Nasz technik będzie u Państwa 
              w ustalonym terminie, aby wykonać {serviceType.toLowerCase()}.
            </Text>

            {/* Service Details */}
            <Section style={{ 
              backgroundColor: '#f8f9fa', 
              padding: '25px', 
              borderRadius: '8px',
              border: `2px solid ${successColor}`,
              margin: '0 0 25px 0'
            }}>
              <Heading style={{ 
                color: '#333333', 
                fontSize: '18px', 
                margin: '0 0 20px 0',
                display: 'flex',
                alignItems: 'center'
              }}>
                📅 Szczegóły wizyty
              </Heading>
              
              <Row style={{ marginBottom: '15px' }}>
                <Column style={{ width: '30%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Data:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {formatDate(scheduledDate)}
                  </Text>
                </Column>
              </Row>

              <Row style={{ marginBottom: '15px' }}>
                <Column style={{ width: '30%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Godzina:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {timeWindow.start} - {timeWindow.end}
                  </Text>
                </Column>
              </Row>

              <Row style={{ marginBottom: '15px' }}>
                <Column style={{ width: '30%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Czas trwania:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    około {Math.round(estimatedDuration / 60)} godzin
                  </Text>
                </Column>
              </Row>

              <Row style={{ marginBottom: '15px' }}>
                <Column style={{ width: '30%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Adres:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {address}
                  </Text>
                </Column>
              </Row>

              {estimatedCost && (
                <Row>
                  <Column style={{ width: '30%' }}>
                    <Text style={{ 
                      color: '#666666', 
                      fontSize: '14px', 
                      fontWeight: 'bold',
                      margin: '0'
                    }}>
                      Szacowany koszt:
                    </Text>
                  </Column>
                  <Column>
                    <Text style={{ 
                      color: '#333333', 
                      fontSize: '14px',
                      margin: '0'
                    }}>
                      {estimatedCost.toLocaleString('pl-PL')} PLN
                    </Text>
                  </Column>
                </Row>
              )}
            </Section>

            {/* Technician Info */}
            <Section style={{ 
              backgroundColor: '#f0f8ff', 
              padding: '25px', 
              borderRadius: '8px',
              border: `2px solid ${primaryColor}`,
              margin: '0 0 25px 0'
            }}>
              <Heading style={{ 
                color: '#333333', 
                fontSize: '18px', 
                margin: '0 0 15px 0' 
              }}>
                👨‍🔧 Twój technik
              </Heading>
              
              <Row>
                {technician.photo && (
                  <Column style={{ width: '80px', verticalAlign: 'top' }}>
                    <Img 
                      src={technician.photo} 
                      alt={technician.name}
                      width="60"
                      height="60"
                      style={{ borderRadius: '50%' }}
                    />
                  </Column>
                )}
                <Column style={{ verticalAlign: 'top' }}>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '16px', 
                    fontWeight: 'bold',
                    margin: '0 0 5px 0'
                  }}>
                    {technician.name}
                  </Text>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px',
                    margin: '0 0 5px 0'
                  }}>
                    📞 {technician.phone}
                  </Text>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '12px',
                    margin: '0'
                  }}>
                    Specjalizacje: {technician.specializations.join(', ')}
                  </Text>
                </Column>
              </Row>
            </Section>

            {/* Equipment List */}
            {equipment.length > 0 && (
              <Section style={{ margin: '0 0 25px 0' }}>
                <Heading style={{ 
                  color: '#333333', 
                  fontSize: '18px', 
                  margin: '0 0 15px 0' 
                }}>
                  🏠 Urządzenia do obsługi
                </Heading>
                
                {equipment.map((item, index) => (
                  <Text key={index} style={{ 
                    color: '#666666', 
                    fontSize: '14px',
                    margin: '0 0 8px 0',
                    paddingLeft: '20px',
                    position: 'relative'
                  }}>
                    <span style={{ 
                      position: 'absolute', 
                      left: '0', 
                      color: accentColor 
                    }}>
                      •
                    </span>
                    {item.brand} {item.model} ({item.location})
                  </Text>
                ))}
              </Section>
            )}

            {/* Preparation Steps */}
            {preparationSteps.length > 0 && (
              <Section style={{ 
                backgroundColor: '#fff7e6', 
                padding: '20px', 
                borderRadius: '8px',
                border: `2px solid ${accentColor}`,
                margin: '0 0 25px 0'
              }}>
                <Heading style={{ 
                  color: '#333333', 
                  fontSize: '16px', 
                  margin: '0 0 15px 0' 
                }}>
                  📋 Przygotowanie do wizyty
                </Heading>
                
                {preparationSteps.map((step, index) => (
                  <Text key={index} style={{ 
                    color: '#666666', 
                    fontSize: '14px',
                    margin: '0 0 8px 0',
                    paddingLeft: '20px',
                    position: 'relative'
                  }}>
                    <span style={{ 
                      position: 'absolute', 
                      left: '0', 
                      color: accentColor,
                      fontWeight: 'bold'
                    }}>
                      {index + 1}.
                    </span>
                    {step}
                  </Text>
                ))}
              </Section>
            )}

            {/* Action Buttons */}
            <Section style={{ textAlign: 'center', margin: '25px 0' }}>
              <Button
                href={`https://fulmark.com/service/reschedule?order=${orderNumber}`}
                style={{
                  backgroundColor: primaryColor,
                  color: '#ffffff',
                  padding: '12px 25px',
                  borderRadius: '6px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  fontSize: '14px',
                  margin: '0 10px 10px 0',
                  display: 'inline-block'
                }}
              >
                📅 Zmień termin
              </Button>
              
              <Button
                href={`tel:${emergencyContact}`}
                style={{
                  backgroundColor: accentColor,
                  color: '#ffffff',
                  padding: '12px 25px',
                  borderRadius: '6px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  fontSize: '14px',
                  margin: '0 10px 10px 0',
                  display: 'inline-block'
                }}
              >
                📞 Kontakt
              </Button>
            </Section>

            {/* Important Note */}
            <Section style={{ 
              backgroundColor: '#f6ffed', 
              padding: '15px', 
              borderRadius: '6px',
              border: `1px solid ${successColor}`,
              margin: '25px 0'
            }}>
              <Text style={{ 
                color: '#333333', 
                fontSize: '14px',
                margin: '0',
                textAlign: 'center'
              }}>
                💡 <strong>Ważne:</strong> Technik skontaktuje się 30 minut przed przyjazdem
              </Text>
            </Section>
          </Section>

          {/* Footer */}
          <Section style={{ 
            backgroundColor: '#f8f9fa', 
            padding: '25px 20px',
            borderRadius: '0 0 10px 10px',
            textAlign: 'center',
            borderLeft: `1px solid #e0e0e0`,
            borderRight: `1px solid #e0e0e0`,
            borderBottom: `1px solid #e0e0e0`
          }}>
            <Text style={{ 
              color: '#666666', 
              fontSize: '14px',
              margin: '0 0 10px 0'
            }}>
              Fulmark HVAC - Profesjonalne usługi klimatyzacyjne
            </Text>
            
            <Text style={{ 
              color: '#999999', 
              fontSize: '12px',
              margin: '0 0 15px 0'
            }}>
              Kontakt awaryjny: {emergencyContact}<br/>
              Email: <EMAIL>
            </Text>
            
            <Hr style={{ margin: '15px 0', borderColor: '#e0e0e0' }} />
            
            <Text style={{ 
              color: '#999999', 
              fontSize: '11px',
              margin: '0'
            }}>
              <Link href="https://fulmark.com/service-terms" style={{ color: '#999999' }}>
                Warunki serwisu
              </Link> | 
              <Link href="https://fulmark.com/privacy" style={{ color: '#999999' }}>
                Polityka prywatności
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};