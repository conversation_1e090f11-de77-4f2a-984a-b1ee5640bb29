/**
 * 🔧 COSMIC SERVICE ORDER WORKFLOW
 * Najwspanialszy workflow zarządzania zleceniami serwisowymi z AI optimization
 */

import { TriggerClient, eventTrigger } from "@trigger.dev/sdk";
import { z } from "zod";
import { render } from '@react-email/render';
import { logger } from '@/utils/logger';
import { ServiceConfirmationEmail } from '../email-templates/ServiceConfirmationEmail';

const client = new TriggerClient({
  id: "hvac-crm",
  apiKey: process.env.TRIGGER_API_KEY!,
});

// Service order workflow schema
const serviceOrderSchema = z.object({
  serviceOrderId: z.string(),
  priority: z.enum(['low', 'medium', 'high', 'critical', 'emergency']),
  type: z.enum(['installation', 'maintenance', 'repair', 'inspection', 'emergency', 'consultation']),
  customerId: z.string().optional(),
  technicianId: z.string().optional(),
});

export const serviceOrderWorkflow = client.defineJob({
  id: "service-order-automation",
  name: "Cosmic Service Order Automation",
  version: "1.0.0",
  trigger: eventTrigger({
    name: "service.order.created",
    schema: serviceOrderSchema,
  }),
  run: async (payload, io, ctx) => {
    logger.info(`🔧 Starting cosmic service order workflow: ${payload.serviceOrderId}`);

    // Step 1: Fetch complete service order details
    const serviceOrder = await io.runTask("fetch-service-order", async () => {
      logger.info(`📋 Fetching service order details`);
      
      // Simulate service order fetch
      return {
        id: payload.serviceOrderId,
        orderInfo: {
          orderNumber: 'FH-20241215-001',
          type: payload.type,
          priority: payload.priority,
          status: 'pending',
          title: 'Serwis klimatyzacji Daikin',
          description: 'Przegląd i konserwacja jednostki klimatyzacyjnej',
        },
        customer: {
          customerId: payload.customerId || 'cust_123',
          contactPerson: 'Jan Kowalski',
          phone: '+48 123 456 789',
          email: '<EMAIL>',
        },
        location: {
          address: 'ul. Testowa 123, 00-001 Warszawa',
          coordinates: { lat: 52.2297, lng: 21.0122 },
          buildingType: 'residential',
          accessInstructions: 'Domofon nr 15, kod 1234',
        },
        equipment: [
          {
            id: 'eq_001',
            type: 'air_conditioner',
            brand: 'Daikin',
            model: 'FTXM35R',
            serialNumber: 'DK123456789',
            location: 'Salon',
            condition: 'good',
          },
        ],
        scheduling: {
          requestedDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          estimatedDuration: 120, // 2 hours
          timeWindow: { start: '09:00', end: '17:00' },
          flexibility: 'flexible',
        },
      };
    });

    // Step 2: AI-powered route optimization and technician assignment
    const optimizedAssignment = await io.runTask("optimize-assignment", async () => {
      logger.info(`🤖 AI-powered technician assignment and route optimization`);
      
      // Simulate AI optimization
      const availableTechnicians = [
        {
          id: 'tech_001',
          name: 'Tomasz Nowak',
          phone: '+48 987 654 321',
          photo: 'https://fulmark.com/team/tomasz-nowak.jpg',
          specializations: ['Daikin', 'Klimatyzacja', 'Serwis'],
          currentLocation: { lat: 52.2200, lng: 21.0100 },
          availability: 'available',
          rating: 4.9,
          completedJobs: 156,
        },
        {
          id: 'tech_002',
          name: 'Anna Kowalczyk',
          phone: '+48 987 654 322',
          specializations: ['LG', 'Mitsubishi', 'Instalacja'],
          currentLocation: { lat: 52.2400, lng: 21.0200 },
          availability: 'busy',
          rating: 4.8,
          completedJobs: 142,
        },
      ];

      // AI selection based on:
      // - Skills match
      // - Location proximity
      // - Availability
      // - Customer preferences
      // - Route optimization
      
      const selectedTechnician = availableTechnicians[0]; // Best match
      
      // Calculate optimal schedule
      const optimizedSchedule = {
        scheduledDate: serviceOrder.scheduling.requestedDate,
        timeWindow: { start: '10:00', end: '12:00' },
        estimatedArrival: '10:15',
        travelTime: 25, // minutes
        routeOptimized: true,
      };

      return {
        technician: selectedTechnician,
        schedule: optimizedSchedule,
        confidence: 0.92,
        alternativeTechnicians: availableTechnicians.slice(1),
      };
    });

    // Step 3: Smart inventory and parts preparation
    const partsPreparation = await io.runTask("prepare-parts", async () => {
      logger.info(`📦 Smart parts and inventory preparation`);
      
      // AI-powered parts prediction based on:
      // - Equipment type and age
      // - Service history
      // - Common failure patterns
      // - Seasonal factors
      
      const predictedParts = [
        {
          partId: 'filter_001',
          name: 'Filtr powietrza Daikin FTXM35R',
          probability: 0.85,
          quantity: 1,
          inStock: true,
          cost: 45.00,
        },
        {
          partId: 'refrigerant_001',
          name: 'Czynnik chłodniczy R32',
          probability: 0.35,
          quantity: 0.5, // kg
          inStock: true,
          cost: 120.00,
        },
      ];

      // Reserve parts for the job
      const reservedParts = predictedParts.filter(part => part.probability > 0.5);
      
      return {
        predictedParts,
        reservedParts,
        totalEstimatedCost: reservedParts.reduce((sum, part) => sum + part.cost, 0),
        preparationCompleted: true,
      };
    });

    // Step 4: Generate and send service confirmation email
    const confirmationEmail = await io.runTask("send-confirmation-email", async () => {
      logger.info(`📧 Generating and sending service confirmation email`);
      
      const preparationSteps = [
        'Zapewnij dostęp do urządzenia klimatyzacyjnego',
        'Przygotuj dokumentację gwarancyjną (jeśli dostępna)',
        'Wyłącz urządzenie 30 minut przed wizytą',
        'Zapewnij miejsce parkingowe dla technika',
      ];

      const emailHtml = render(
        ServiceConfirmationEmail({
          customerName: serviceOrder.customer.contactPerson,
          orderNumber: serviceOrder.orderInfo.orderNumber,
          serviceType: serviceOrder.orderInfo.type,
          scheduledDate: optimizedAssignment.schedule.scheduledDate,
          timeWindow: optimizedAssignment.schedule.timeWindow,
          technician: {
            name: optimizedAssignment.technician.name,
            phone: optimizedAssignment.technician.phone,
            photo: optimizedAssignment.technician.photo,
            specializations: optimizedAssignment.technician.specializations,
          },
          address: serviceOrder.location.address,
          estimatedDuration: serviceOrder.scheduling.estimatedDuration,
          estimatedCost: 250 + partsPreparation.totalEstimatedCost,
          equipment: serviceOrder.equipment.map(eq => ({
            brand: eq.brand,
            model: eq.model,
            location: eq.location,
          })),
          preparationSteps,
          emergencyContact: '+48 800 123 456',
          cosmicTheme: true,
        })
      );
      
      // Send email
      logger.info(`📤 Sending confirmation email to ${serviceOrder.customer.email}`);
      
      return {
        to: serviceOrder.customer.email,
        subject: `Potwierdzenie wizyty serwisowej - ${serviceOrder.orderInfo.orderNumber}`,
        html: emailHtml,
        sent: true,
        sentAt: new Date().toISOString(),
      };
    });

    // Step 5: Update technician schedule and mobile app
    const technicianNotification = await io.runTask("notify-technician", async () => {
      logger.info(`👨‍🔧 Updating technician schedule and sending mobile notification`);
      
      // Update technician's mobile app
      const notification = {
        technicianId: optimizedAssignment.technician.id,
        type: 'new_assignment',
        title: 'Nowe zlecenie serwisowe',
        message: `${serviceOrder.orderInfo.title} - ${optimizedAssignment.schedule.scheduledDate}`,
        data: {
          serviceOrderId: payload.serviceOrderId,
          customerName: serviceOrder.customer.contactPerson,
          address: serviceOrder.location.address,
          scheduledTime: optimizedAssignment.schedule.timeWindow.start,
          estimatedDuration: serviceOrder.scheduling.estimatedDuration,
          priority: payload.priority,
        },
        scheduledFor: optimizedAssignment.schedule.scheduledDate,
      };
      
      // Send push notification to mobile app
      logger.info(`📱 Push notification sent to technician: ${optimizedAssignment.technician.name}`);
      
      return notification;
    });

    // Step 6: Schedule automated follow-ups and reminders
    const scheduledTasks = await io.runTask("schedule-follow-ups", async () => {
      logger.info(`📅 Scheduling automated follow-ups and reminders`);
      
      const tasks = [
        {
          type: 'customer_reminder',
          scheduledFor: new Date(new Date(optimizedAssignment.schedule.scheduledDate).getTime() - 24 * 60 * 60 * 1000).toISOString(),
          description: '24h reminder email to customer',
          action: 'send_reminder_email',
        },
        {
          type: 'technician_preparation',
          scheduledFor: new Date(new Date(optimizedAssignment.schedule.scheduledDate).getTime() - 2 * 60 * 60 * 1000).toISOString(),
          description: '2h preparation reminder to technician',
          action: 'send_preparation_notification',
        },
        {
          type: 'arrival_notification',
          scheduledFor: new Date(new Date(optimizedAssignment.schedule.scheduledDate).getTime() - 30 * 60 * 1000).toISOString(),
          description: '30min arrival notification to customer',
          action: 'send_arrival_sms',
        },
        {
          type: 'feedback_request',
          scheduledFor: new Date(new Date(optimizedAssignment.schedule.scheduledDate).getTime() + 4 * 60 * 60 * 1000).toISOString(),
          description: 'Post-service feedback request',
          action: 'send_feedback_email',
        },
      ];

      // Schedule each task
      for (const task of tasks) {
        await client.sendEvent({
          name: 'service.task.scheduled',
          payload: {
            serviceOrderId: payload.serviceOrderId,
            taskType: task.type,
            scheduledFor: task.scheduledFor,
            action: task.action,
          },
        });
      }
      
      return tasks;
    });

    // Step 7: Update service order status and analytics
    await io.runTask("update-service-order", async () => {
      logger.info(`📊 Updating service order status and analytics`);
      
      const updates = {
        status: 'scheduled',
        assignedTechnicianId: optimizedAssignment.technician.id,
        scheduledDate: optimizedAssignment.schedule.scheduledDate,
        estimatedCost: 250 + partsPreparation.totalEstimatedCost,
        partsReserved: partsPreparation.reservedParts.length,
        confirmationSent: confirmationEmail.sent,
        lastUpdated: new Date().toISOString(),
      };
      
      // Update analytics
      const analytics = {
        assignmentTime: Date.now() - ctx.run.startedAt.getTime(),
        aiConfidence: optimizedAssignment.confidence,
        routeOptimized: optimizedAssignment.schedule.routeOptimized,
        partsPreparationScore: partsPreparation.predictedParts.length,
      };
      
      logger.info(`✅ Service order updated with analytics`);
      return { updates, analytics };
    });

    // Step 8: Trigger additional workflows based on priority
    if (payload.priority === 'emergency' || payload.priority === 'critical') {
      await io.runTask("trigger-priority-workflow", async () => {
        logger.info(`🚨 Triggering priority workflow for ${payload.priority} service`);
        
        await client.sendEvent({
          name: 'service.priority.escalation',
          payload: {
            serviceOrderId: payload.serviceOrderId,
            priority: payload.priority,
            technicianId: optimizedAssignment.technician.id,
            estimatedArrival: optimizedAssignment.schedule.estimatedArrival,
          },
        });
        
        return { escalated: true };
      });
    }

    logger.info(`🎉 Cosmic service order workflow completed: ${payload.serviceOrderId}`);

    return {
      success: true,
      serviceOrderId: payload.serviceOrderId,
      status: 'scheduled',
      assignedTechnician: {
        id: optimizedAssignment.technician.id,
        name: optimizedAssignment.technician.name,
        phone: optimizedAssignment.technician.phone,
      },
      scheduledDate: optimizedAssignment.schedule.scheduledDate,
      timeWindow: optimizedAssignment.schedule.timeWindow,
      estimatedCost: 250 + partsPreparation.totalEstimatedCost,
      confirmationSent: confirmationEmail.sent,
      partsReserved: partsPreparation.reservedParts.length,
      followUpTasksScheduled: scheduledTasks.length,
      aiOptimization: {
        confidence: optimizedAssignment.confidence,
        routeOptimized: optimizedAssignment.schedule.routeOptimized,
        partsPreparationScore: partsPreparation.predictedParts.length,
      },
      processingTime: Date.now() - ctx.run.startedAt.getTime(),
    };
  },
});

export default serviceOrderWorkflow;