/**
 * 🔧 COSMIC SERVICE MANAGEMENT TYPES
 * Najwspanialsze definicje typów dla zarządzania serwisem HVAC
 */

export interface CosmicServiceOrder {
  id: string;
  
  // Basic Information
  orderInfo: {
    orderNumber: string;
    type: 'installation' | 'maintenance' | 'repair' | 'inspection' | 'emergency' | 'consultation';
    priority: 'low' | 'medium' | 'high' | 'critical' | 'emergency';
    status: 'pending' | 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';
    title: string;
    description: string;
    customerNotes?: string;
    internalNotes?: string;
  };
  
  // Customer Information
  customer: {
    customerId: string;
    contactPerson: string;
    phone: string;
    email: string;
    alternativeContact?: string;
  };
  
  // Location Information
  location: {
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    accessInstructions?: string;
    parkingInfo?: string;
    buildingType: 'residential' | 'commercial' | 'industrial';
    floorPlan?: string;
    safetyRequirements?: string[];
  };
  
  // Equipment Information
  equipment: ServiceEquipment[];
  
  // Scheduling
  scheduling: {
    requestedDate?: string;
    scheduledDate?: string;
    estimatedDuration: number; // minutes
    timeWindow: {
      start: string; // HH:MM
      end: string; // HH:MM
    };
    flexibility: 'strict' | 'flexible' | 'very_flexible';
    recurringSchedule?: RecurringSchedule;
  };
}