/**
 * 💰 COSMIC QUOTES & WARRANTIES TYPES
 * Najwspanialsze definicje typów dla ofert i gwarancji HVAC z AI pricing
 */

export interface CosmicQuote {
  id: string;
  
  // Basic Quote Information
  quoteInfo: {
    quoteNumber: string;
    title: string;
    description: string;
    type: QuoteType;
    status: QuoteStatus;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    validUntil: string;
    currency: 'PLN' | 'EUR' | 'USD';
    language: 'pl' | 'en';
  };
  
  // Customer Information
  customer: {
    customerId: string;
    contactPerson: string;
    email: string;
    phone: string;
    company?: string;
    nip?: string;
    address: {
      street: string;
      city: string;
      postalCode: string;
      country: string;
    };
  };
  
  // Project Details
  project: {
    name: string;
    description: string;
    location: {
      address: string;
      buildingType: 'residential' | 'commercial' | 'industrial';
      area: number; // m²
      floors?: number;
      rooms?: number;
      occupancy?: number;
    };
    requirements: ProjectRequirement[];
    constraints: ProjectConstraint[];
    timeline: {
      startDate?: string;
      endDate?: string;
      flexibility: 'strict' | 'flexible' | 'very_flexible';
    };
  };
  
  // Quote Items & Pricing
  items: QuoteItem[];
  pricing: {
    subtotal: number;
    discounts: QuoteDiscount[];
    taxes: QuoteTax[];
    total: number;
    paymentTerms: PaymentTerms;
    financingOptions?: FinancingOption[];
  };
  
  // AI-Powered Pricing Intelligence
  aiPricing: {
    competitiveAnalysis: CompetitiveAnalysis;
    marketPosition: 'budget' | 'mid_range' | 'premium' | 'luxury';
    priceOptimization: PriceOptimization;
    winProbability: number; // 0-1
    recommendedAdjustments: PricingAdjustment[];
    seasonalFactors: SeasonalFactor[];
    riskAssessment: PricingRisk[];
  };
  
  // Technical Specifications
  technical: {
    systemDesign: SystemDesign;
    equipmentList: TechnicalEquipment[];
    installations: InstallationSpec[];
    compliance: ComplianceRequirement[];
    warranties: WarrantySpec[];
    maintenance: MaintenancePackage[];
  };
  
  // Documents & Attachments
  documents: {
    proposal: DocumentReference;
    technicalDrawings: DocumentReference[];
    specifications: DocumentReference[];
    certifications: DocumentReference[];
    photos: string[];
    presentations: DocumentReference[];
  };
  
  // Workflow & Approvals
  workflow: {
    currentStage: QuoteStage;
    approvals: QuoteApproval[];
    revisions: QuoteRevision[];
    communications: QuoteCommunication[];
    followUps: FollowUpTask[];
  };
  
  // Analytics & Insights
  analytics: {
    viewCount: number;
    downloadCount: number;
    timeSpent: number; // seconds
    customerEngagement: EngagementMetric[];
    conversionProbability: number; // 0-1
    competitorComparison: CompetitorComparison[];
  };
  
  // Metadata
  metadata: {
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    lastModifiedBy: string;
    version: number;
    tags: string[];
    notes: QuoteNote[];
    source: 'website' | 'referral' | 'cold_call' | 'marketing' | 'existing_customer';
  };
}

export type QuoteType = 
  | 'installation'
  | 'maintenance_contract'
  | 'repair'
  | 'upgrade'
  | 'consultation'
  | 'emergency'
  | 'retrofit'
  | 'design_build';

export type QuoteStatus = 
  | 'draft'
  | 'pending_review'
  | 'sent'
  | 'viewed'
  | 'under_negotiation'
  | 'accepted'
  | 'rejected'
  | 'expired'
  | 'cancelled';

export type QuoteStage = 
  | 'initial_inquiry'
  | 'site_survey'
  | 'design_phase'
  | 'pricing_phase'
  | 'review_phase'
  | 'presentation'
  | 'negotiation'
  | 'final_approval'
  | 'contract_signing';

export interface QuoteItem {
  id: string;
  type: 'equipment' | 'labor' | 'material' | 'service' | 'permit' | 'other';
  category: string;
  name: string;
  description: string;
  specifications?: Record<string, any>;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  supplier?: string;
  leadTime?: number; // days
  warranty?: ItemWarranty;
  alternatives?: AlternativeItem[];
  isOptional: boolean;
  markup: number; // percentage
  cost: number; // internal cost
}

export interface ProjectRequirement {
  id: string;
  category: 'performance' | 'efficiency' | 'compliance' | 'aesthetic' | 'budget' | 'timeline';
  description: string;
  priority: 'must_have' | 'should_have' | 'nice_to_have';
  measurable: boolean;
  target?: number;
  unit?: string;
  compliance?: string[];
}

export interface CompetitiveAnalysis {
  marketPrice: {
    low: number;
    average: number;
    high: number;
  };
  competitorPrices: CompetitorPrice[];
  ourPosition: 'below_market' | 'at_market' | 'above_market';
  valueProposition: string[];
  differentiators: string[];
  threats: string[];
  opportunities: string[];
}

export interface PriceOptimization {
  recommendedPrice: number;
  confidence: number; // 0-1
  factors: OptimizationFactor[];
  scenarios: PricingScenario[];
  elasticity: number; // price elasticity
  breakeven: number;
  targetMargin: number;
}

export interface SystemDesign {
  id: string;
  type: 'split_system' | 'central_air' | 'vrf' | 'chiller' | 'heat_pump' | 'hybrid';
  capacity: {
    cooling: number; // kW
    heating: number; // kW
  };
  efficiency: {
    seer: number;
    scop: number;
    energyClass: string;
  };
  zones: DesignZone[];
  ductwork: DuctworkDesign;
  controls: ControlSystem;
  integration: SystemIntegration[];
}

// Warranty Types
export interface CosmicWarranty {
  id: string;
  
  // Basic Warranty Information
  warrantyInfo: {
    warrantyNumber: string;
    type: WarrantyType;
    status: WarrantyStatus;
    title: string;
    description: string;
  };
  
  // Coverage Details
  coverage: {
    equipmentId: string;
    customerId: string;
    quoteId?: string;
    serviceOrderId?: string;
    startDate: string;
    endDate: string;
    duration: number; // months
    coverageType: CoverageType[];
    limitations: WarrantyLimitation[];
    exclusions: WarrantyExclusion[];
  };
  
  // Terms & Conditions
  terms: {
    provider: 'manufacturer' | 'installer' | 'third_party' | 'extended';
    transferable: boolean;
    renewable: boolean;
    prorated: boolean;
    laborIncluded: boolean;
    partsIncluded: boolean;
    travelIncluded: boolean;
    responseTime: number; // hours
    maintenanceRequired: boolean;
    registrationRequired: boolean;
  };
  
  // Claims & Service History
  claims: WarrantyClaim[];
  serviceHistory: WarrantyService[];
  
  // Financial Information
  financial: {
    cost: number;
    value: number;
    deductible?: number;
    maxClaim?: number;
    totalClaims: number;
    remainingValue: number;
  };
  
  // AI Analytics
  aiAnalytics: {
    claimProbability: number; // 0-1
    riskScore: number; // 0-100
    recommendedActions: string[];
    costBenefit: number;
    renewalRecommendation: boolean;
  };
  
  // Metadata
  metadata: {
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    lastModifiedBy: string;
    version: number;
    tags: string[];
    documents: string[];
    notes: WarrantyNote[];
  };
}

export type WarrantyType = 
  | 'manufacturer'
  | 'installation'
  | 'extended'
  | 'service_contract'
  | 'performance'
  | 'energy_efficiency';

export type WarrantyStatus = 
  | 'active'
  | 'expired'
  | 'voided'
  | 'suspended'
  | 'transferred'
  | 'claimed';

export type CoverageType = 
  | 'parts'
  | 'labor'
  | 'travel'
  | 'emergency'
  | 'preventive_maintenance'
  | 'performance_guarantee'
  | 'energy_efficiency';

export interface WarrantyClaim {
  id: string;
  claimNumber: string;
  type: 'parts' | 'labor' | 'performance' | 'emergency';
  status: 'submitted' | 'under_review' | 'approved' | 'denied' | 'completed';
  submittedDate: string;
  description: string;
  cost: number;
  resolution?: string;
  completedDate?: string;
  satisfaction?: number; // 1-5
}