/**
 * 🔧 COSMIC MAINTENANCE NOTIFICATION EMAIL
 * Najwspanialszy email powiadomienia o konserwacji z AI insights
 */

import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Row,
  Column,
  Heading,
  Text,
  Button,
  Img,
  Hr,
  Link,
} from '@react-email/components';

interface MaintenanceNotificationEmailProps {
  customerName: string;
  equipmentName: string;
  equipmentType: string;
  brand: string;
  model: string;
  location: string;
  maintenanceType: 'preventive' | 'predictive' | 'corrective' | 'emergency';
  scheduledDate: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration: number;
  estimatedCost: number;
  healthScore: number;
  predictedIssues?: Array<{
    component: string;
    description: string;
    probability: number;
  }>;
  recommendations: string[];
  benefits: string[];
  contactInfo: {
    phone: string;
    email: string;
  };
  cosmicTheme?: boolean;
}

export const MaintenanceNotificationEmail = ({
  customerName,
  equipmentName,
  equipmentType,
  brand,
  model,
  location,
  maintenanceType,
  scheduledDate,
  urgency,
  estimatedDuration,
  estimatedCost,
  healthScore,
  predictedIssues = [],
  recommendations,
  benefits,
  contactInfo,
  cosmicTheme = true,
}: MaintenanceNotificationEmailProps) => {
  const primaryColor = cosmicTheme ? '#1890ff' : '#0066cc';
  const accentColor = cosmicTheme ? '#fa8c16' : '#ff6600';
  const backgroundColor = cosmicTheme ? '#f0f8ff' : '#ffffff';
  
  // Dynamic colors based on urgency
  const urgencyColors = {
    low: '#52c41a',
    medium: '#faad14',
    high: '#fa8c16',
    critical: '#ff4d4f',
  };
  
  const urgencyColor = urgencyColors[urgency];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getMaintenanceIcon = (type: string) => {
    const icons = {
      'preventive': '🔧',
      'predictive': '🔮',
      'corrective': '🛠️',
      'emergency': '🚨',
    };
    return icons[type as keyof typeof icons] || '🔧';
  };

  const getUrgencyLabel = (urgency: string) => {
    const labels = {
      'low': 'Niski priorytet',
      'medium': 'Średni priorytet',
      'high': 'Wysoki priorytet',
      'critical': 'Krytyczny',
    };
    return labels[urgency as keyof typeof labels] || 'Średni priorytet';
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    if (score >= 40) return '#fa8c16';
    return '#ff4d4f';
  };

  return (
    <Html>
      <Head>
        <title>Powiadomienie o konserwacji - Fulmark HVAC</title>
      </Head>
      <Body style={{ backgroundColor, fontFamily: 'Arial, sans-serif' }}>
        <Container style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
          
          {/* Header */}
          <Section style={{ 
            backgroundColor: primaryColor, 
            padding: '30px 20px', 
            borderRadius: '10px 10px 0 0',
            textAlign: 'center'
          }}>
            <Img 
              src="https://fulmark.com/logo-white.png" 
              alt="Fulmark HVAC" 
              width="180" 
              height="50"
              style={{ margin: '0 auto' }}
            />
            <Heading style={{ 
              color: '#ffffff', 
              fontSize: '24px', 
              margin: '20px 0 10px 0',
              fontWeight: 'bold'
            }}>
              Konserwacja urządzenia {getMaintenanceIcon(maintenanceType)}
            </Heading>
            <Text style={{ 
              color: '#ffffff', 
              fontSize: '16px', 
              margin: '0',
              opacity: 0.9
            }}>
              AI-powered maintenance notification
            </Text>
          </Section>

          {/* Urgency Banner */}
          <Section style={{ 
            backgroundColor: urgencyColor, 
            padding: '15px 20px',
            textAlign: 'center'
          }}>
            <Text style={{ 
              color: '#ffffff', 
              fontSize: '16px', 
              fontWeight: 'bold',
              margin: '0'
            }}>
              {getUrgencyLabel(urgency)} • Zaplanowano na {formatDate(scheduledDate)}
            </Text>
          </Section>

          {/* Main Content */}
          <Section style={{ 
            backgroundColor: '#ffffff', 
            padding: '30px',
            borderLeft: `1px solid #e0e0e0`,
            borderRight: `1px solid #e0e0e0`
          }}>
            <Heading style={{ 
              color: '#333333', 
              fontSize: '22px', 
              margin: '0 0 20px 0' 
            }}>
              Dzień dobry {customerName}! 👋
            </Heading>

            <Text style={{ 
              color: '#666666', 
              fontSize: '16px', 
              lineHeight: '1.6',
              margin: '0 0 25px 0'
            }}>
              Nasz system AI wykrył, że Twoje urządzenie wymaga konserwacji. 
              Przygotowaliśmy szczegółowe informacje i rekomendacje.
            </Text>

            {/* Equipment Details */}
            <Section style={{ 
              backgroundColor: '#f8f9fa', 
              padding: '25px', 
              borderRadius: '8px',
              border: `2px solid ${primaryColor}`,
              margin: '0 0 25px 0'
            }}>
              <Heading style={{ 
                color: '#333333', 
                fontSize: '18px', 
                margin: '0 0 20px 0' 
              }}>
                🏠 Szczegóły urządzenia
              </Heading>
              
              <Row style={{ marginBottom: '12px' }}>
                <Column style={{ width: '35%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Nazwa:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {equipmentName}
                  </Text>
                </Column>
              </Row>

              <Row style={{ marginBottom: '12px' }}>
                <Column style={{ width: '35%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Typ:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {equipmentType}
                  </Text>
                </Column>
              </Row>

              <Row style={{ marginBottom: '12px' }}>
                <Column style={{ width: '35%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Marka/Model:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {brand} {model}
                  </Text>
                </Column>
              </Row>

              <Row style={{ marginBottom: '12px' }}>
                <Column style={{ width: '35%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Lokalizacja:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {location}
                  </Text>
                </Column>
              </Row>

              <Row>
                <Column style={{ width: '35%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Stan zdrowia:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: getHealthScoreColor(healthScore), 
                    fontSize: '14px',
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    {healthScore}% 
                    {healthScore >= 80 ? '🟢' : healthScore >= 60 ? '🟡' : '🔴'}
                  </Text>
                </Column>
              </Row>
            </Section>

            {/* Maintenance Details */}
            <Section style={{ 
              backgroundColor: '#fff7e6', 
              padding: '25px', 
              borderRadius: '8px',
              border: `2px solid ${accentColor}`,
              margin: '0 0 25px 0'
            }}>
              <Heading style={{ 
                color: '#333333', 
                fontSize: '18px', 
                margin: '0 0 20px 0' 
              }}>
                🔧 Szczegóły konserwacji
              </Heading>
              
              <Row style={{ marginBottom: '12px' }}>
                <Column style={{ width: '35%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Typ konserwacji:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {maintenanceType === 'preventive' ? 'Prewencyjna' :
                     maintenanceType === 'predictive' ? 'Predykcyjna (AI)' :
                     maintenanceType === 'corrective' ? 'Naprawcza' : 'Awaryjna'}
                  </Text>
                </Column>
              </Row>

              <Row style={{ marginBottom: '12px' }}>
                <Column style={{ width: '35%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Czas trwania:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    około {Math.round(estimatedDuration / 60)} godzin
                  </Text>
                </Column>
              </Row>

              <Row>
                <Column style={{ width: '35%' }}>
                  <Text style={{ 
                    color: '#666666', 
                    fontSize: '14px', 
                    fontWeight: 'bold',
                    margin: '0'
                  }}>
                    Szacowany koszt:
                  </Text>
                </Column>
                <Column>
                  <Text style={{ 
                    color: '#333333', 
                    fontSize: '14px',
                    margin: '0'
                  }}>
                    {estimatedCost.toLocaleString('pl-PL')} PLN
                  </Text>
                </Column>
              </Row>
            </Section>

            {/* AI Predictions */}
            {predictedIssues.length > 0 && (
              <Section style={{ 
                backgroundColor: '#f6ffed', 
                padding: '20px', 
                borderRadius: '8px',
                border: `2px solid #52c41a`,
                margin: '0 0 25px 0'
              }}>
                <Heading style={{ 
                  color: '#333333', 
                  fontSize: '16px', 
                  margin: '0 0 15px 0' 
                }}>
                  🔮 Predykcje AI
                </Heading>
                
                {predictedIssues.map((issue, index) => (
                  <Text key={index} style={{ 
                    color: '#666666', 
                    fontSize: '14px',
                    margin: '0 0 10px 0',
                    paddingLeft: '20px',
                    position: 'relative'
                  }}>
                    <span style={{ 
                      position: 'absolute', 
                      left: '0', 
                      color: '#52c41a',
                      fontWeight: 'bold'
                    }}>
                      •
                    </span>
                    <strong>{issue.component}:</strong> {issue.description} 
                    <span style={{ color: '#999999' }}>
                      (prawdopodobieństwo: {Math.round(issue.probability * 100)}%)
                    </span>
                  </Text>
                ))}
              </Section>
            )}

            {/* Recommendations */}
            <Section style={{ margin: '0 0 25px 0' }}>
              <Heading style={{ 
                color: '#333333', 
                fontSize: '18px', 
                margin: '0 0 15px 0' 
              }}>
                💡 Rekomendacje
              </Heading>
              
              {recommendations.map((rec, index) => (
                <Text key={index} style={{ 
                  color: '#666666', 
                  fontSize: '14px',
                  margin: '0 0 8px 0',
                  paddingLeft: '20px',
                  position: 'relative'
                }}>
                  <span style={{ 
                    position: 'absolute', 
                    left: '0', 
                    color: accentColor,
                    fontWeight: 'bold'
                  }}>
                    {index + 1}.
                  </span>
                  {rec}
                </Text>
              ))}
            </Section>

            {/* Benefits */}
            <Section style={{ 
              backgroundColor: '#e6f7ff', 
              padding: '20px', 
              borderRadius: '8px',
              border: `1px solid ${primaryColor}`,
              margin: '0 0 25px 0'
            }}>
              <Heading style={{ 
                color: '#333333', 
                fontSize: '16px', 
                margin: '0 0 15px 0' 
              }}>
                ✨ Korzyści z konserwacji
              </Heading>
              
              {benefits.map((benefit, index) => (
                <Text key={index} style={{ 
                  color: '#666666', 
                  fontSize: '14px',
                  margin: '0 0 8px 0',
                  paddingLeft: '20px',
                  position: 'relative'
                }}>
                  <span style={{ 
                    position: 'absolute', 
                    left: '0', 
                    color: primaryColor 
                  }}>
                    ✓
                  </span>
                  {benefit}
                </Text>
              ))}
            </Section>

            {/* Action Buttons */}
            <Section style={{ textAlign: 'center', margin: '25px 0' }}>
              <Button
                href={`https://fulmark.com/maintenance/schedule?equipment=${equipmentName}`}
                style={{
                  backgroundColor: primaryColor,
                  color: '#ffffff',
                  padding: '15px 30px',
                  borderRadius: '8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  fontSize: '16px',
                  margin: '0 10px 10px 0',
                  display: 'inline-block'
                }}
              >
                📅 Umów konserwację
              </Button>
              
              <Button
                href={`tel:${contactInfo.phone}`}
                style={{
                  backgroundColor: accentColor,
                  color: '#ffffff',
                  padding: '15px 30px',
                  borderRadius: '8px',
                  textDecoration: 'none',
                  fontWeight: 'bold',
                  fontSize: '16px',
                  margin: '0 10px 10px 0',
                  display: 'inline-block'
                }}
              >
                📞 Zadzwoń teraz
              </Button>
            </Section>
          </Section>

          {/* Footer */}
          <Section style={{ 
            backgroundColor: '#f8f9fa', 
            padding: '25px 20px',
            borderRadius: '0 0 10px 10px',
            textAlign: 'center',
            borderLeft: `1px solid #e0e0e0`,
            borderRight: `1px solid #e0e0e0`,
            borderBottom: `1px solid #e0e0e0`
          }}>
            <Text style={{ 
              color: '#666666', 
              fontSize: '14px',
              margin: '0 0 10px 0'
            }}>
              Fulmark HVAC - Inteligentne zarządzanie urządzeniami
            </Text>
            
            <Text style={{ 
              color: '#999999', 
              fontSize: '12px',
              margin: '0 0 15px 0'
            }}>
              Kontakt: {contactInfo.phone} | {contactInfo.email}<br/>
              Monitoring 24/7 | AI-powered predictions
            </Text>
            
            <Hr style={{ margin: '15px 0', borderColor: '#e0e0e0' }} />
            
            <Text style={{ 
              color: '#999999', 
              fontSize: '11px',
              margin: '0'
            }}>
              <Link href="https://fulmark.com/equipment-portal" style={{ color: '#999999' }}>
                Portal urządzeń
              </Link> | 
              <Link href="https://fulmark.com/maintenance-terms" style={{ color: '#999999' }}>
                Warunki konserwacji
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};