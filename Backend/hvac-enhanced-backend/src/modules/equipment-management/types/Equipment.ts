/**
 * 🔧 COSMIC EQUIPMENT MANAGEMENT TYPES
 * Najwspanialsze definicje typów dla zarządzania urządzeniami HVAC z IoT integration
 */

export interface CosmicEquipment {
  id: string;
  
  // Basic Equipment Information
  basicInfo: {
    name: string;
    type: EquipmentType;
    category: EquipmentCategory;
    brand: string;
    model: string;
    serialNumber: string;
    manufacturingDate?: string;
    installationDate: string;
    lastServiceDate?: string;
    nextServiceDue?: string;
  };
  
  // Location & Installation Details
  location: {
    customerId: string;
    buildingId?: string;
    floor?: string;
    room: string;
    zone?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
    installationNotes?: string;
    accessInstructions?: string;
    environmentalConditions: EnvironmentalConditions;
  };
  
  // Technical Specifications
  specifications: {
    capacity: {
      cooling?: number; // kW
      heating?: number; // kW
      airflow?: number; // m³/h
      refrigerant?: string;
      powerConsumption?: number; // kW
    };
    dimensions: {
      width?: number; // cm
      height?: number; // cm
      depth?: number; // cm
      weight?: number; // kg
    };
    operatingConditions: {
      minTemperature?: number; // °C
      maxTemperature?: number; // °C
      minHumidity?: number; // %
      maxHumidity?: number; // %
      maxAltitude?: number; // m
    };
    efficiency: {
      seer?: number; // Seasonal Energy Efficiency Ratio
      scop?: number; // Seasonal Coefficient of Performance
      energyClass?: 'A+++' | 'A++' | 'A+' | 'A' | 'B' | 'C' | 'D';
    };
    additionalSpecs: Record<string, any>;
  };
  
  // IoT & Monitoring
  iotIntegration: {
    isConnected: boolean;
    deviceId?: string;
    lastDataReceived?: string;
    connectionType?: 'wifi' | 'ethernet' | 'cellular' | 'zigbee' | 'bluetooth';
    firmwareVersion?: string;
    sensors: EquipmentSensor[];
    alerts: EquipmentAlert[];
    dataRetentionDays: number;
  };
  
  // Real-time Status & Performance
  currentStatus: {
    isOnline: boolean;
    operationalStatus: 'running' | 'idle' | 'maintenance' | 'error' | 'offline';
    currentMode?: 'cooling' | 'heating' | 'fan' | 'auto' | 'dry';
    setTemperature?: number;
    currentTemperature?: number;
    powerConsumption?: number; // current kW
    efficiency?: number; // current efficiency %
    runtime: {
      totalHours: number;
      dailyHours: number;
      weeklyHours: number;
      monthlyHours: number;
    };
    lastStatusUpdate: string;
  };
  
  // AI-Powered Analytics
  aiAnalytics: {
    healthScore: number; // 0-100
    performanceScore: number; // 0-100
    efficiencyTrend: 'improving' | 'stable' | 'declining';
    predictedFailures: PredictedFailure[];
    maintenanceRecommendations: MaintenanceRecommendation[];
    optimizationSuggestions: OptimizationSuggestion[];
    anomalies: EquipmentAnomaly[];
    benchmarkComparison: BenchmarkData;
  };
  
  // Maintenance & Service History
  maintenance: {
    schedule: MaintenanceSchedule;
    history: MaintenanceRecord[];
    warrantyInfo: WarrantyInfo;
    serviceContracts: ServiceContract[];
    partsInventory: EquipmentPart[];
    manuals: DocumentReference[];
  };
  
  // Financial Information
  financial: {
    purchasePrice?: number;
    installationCost?: number;
    currentValue?: number;
    depreciationRate?: number;
    operatingCosts: {
      monthly: number;
      annual: number;
      lifetime: number;
    };
    energyCosts: {
      daily: number;
      monthly: number;
      annual: number;
    };
    maintenanceCosts: {
      preventive: number;
      corrective: number;
      emergency: number;
    };
  };
  
  // Compliance & Certifications
  compliance: {
    certifications: Certification[];
    inspections: InspectionRecord[];
    regulations: ComplianceRequirement[];
    environmentalImpact: EnvironmentalImpact;
  };
  
  // Metadata
  metadata: {
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    lastModifiedBy: string;
    version: number;
    tags: string[];
    notes: EquipmentNote[];
    attachments: string[];
    qrCode?: string;
    photos: string[];
  };
}// Supporting Types and Enums

export type EquipmentType = 
  | 'air_conditioner'
  | 'heat_pump'
  | 'ventilation_unit'
  | 'boiler'
  | 'chiller'
  | 'thermostat'
  | 'air_handler'
  | 'fan_coil'
  | 'vrf_system'
  | 'ductwork'
  | 'air_purifier'
  | 'humidifier'
  | 'dehumidifier';

export type EquipmentCategory = 
  | 'residential'
  | 'commercial'
  | 'industrial'
  | 'medical'
  | 'server_room'
  | 'clean_room';

export interface EnvironmentalConditions {
  averageTemperature: number;
  temperatureRange: { min: number; max: number };
  humidity: number;
  airQuality: 'excellent' | 'good' | 'moderate' | 'poor';
  dustLevel: 'low' | 'medium' | 'high';
  corrosiveEnvironment: boolean;
  vibrationLevel: 'low' | 'medium' | 'high';
}

export interface EquipmentSensor {
  id: string;
  type: 'temperature' | 'humidity' | 'pressure' | 'flow' | 'power' | 'vibration' | 'air_quality';
  name: string;
  unit: string;
  currentValue?: number;
  minValue?: number;
  maxValue?: number;
  alertThresholds: {
    warning: { min?: number; max?: number };
    critical: { min?: number; max?: number };
  };
  calibrationDate?: string;
  nextCalibrationDue?: string;
  isActive: boolean;
}

export interface EquipmentAlert {
  id: string;
  type: 'warning' | 'critical' | 'info';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  sensorId?: string;
  triggeredAt: string;
  acknowledgedAt?: string;
  resolvedAt?: string;
  acknowledgedBy?: string;
  resolution?: string;
  isActive: boolean;
}

export interface PredictedFailure {
  id: string;
  component: string;
  failureType: string;
  probability: number; // 0-1
  predictedDate: string;
  confidence: number; // 0-1
  impact: 'low' | 'medium' | 'high' | 'critical';
  estimatedCost: number;
  preventiveActions: string[];
  aiModel: string;
  lastUpdated: string;
}