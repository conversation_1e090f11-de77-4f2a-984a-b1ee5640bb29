import { TriggerClient, eventTrigger } from "@trigger.dev/sdk";
import { z } from "zod";
import { logger } from '@/utils/logger';

const client = new TriggerClient({
  id: "hvac-crm",
  apiKey: process.env.TRIGGER_API_KEY!,
});

// Email analysis workflow schema
const emailAnalysisSchema = z.object({
  emailId: z.string(),
  content: z.string(),
  sender: z.string(),
  subject: z.string(),
  attachments: z.array(z.string()).optional(),
  receivedAt: z.string(),
});

export const emailAnalysisWorkflow = client.defineJob({
  id: "email-analysis",
  name: "Email Analysis Workflow",
  version: "1.0.0",
  trigger: eventTrigger({
    name: "email.received",
    schema: emailAnalysisSchema,
  }),
  run: async (payload, io, ctx) => {
    logger.info(`🔄 Starting email analysis for: ${payload.emailId}`);

    // Step 1: AI Analysis with Bielik V3
    const aiAnalysis = await io.runTask("ai-analysis", async () => {
      logger.info(`🤖 Analyzing email content with AI`);
      
      // Simulate AI analysis - replace with actual Bielik V3 call
      const analysis = {
        sentiment: 'positive',
        category: 'service_request',
        priority: 'medium',
        extractedInfo: {
          customerName: 'Jan Kowalski',
          phoneNumber: '+48 123 456 789',
          address: 'ul. Testowa 123, Warszawa',
          serviceType: 'klimatyzacja',
          urgency: 'normal',
        },
        requiresResponse: true,
        confidence: 0.85,
      };
      
      return analysis;
    });

    // Step 2: Extract Customer Information
    const customerInfo = await io.runTask("extract-customer", async () => {
      logger.info(`👤 Extracting customer information`);
      
      return {
        email: payload.sender,
        name: aiAnalysis.extractedInfo.customerName,
        phone: aiAnalysis.extractedInfo.phoneNumber,
        address: aiAnalysis.extractedInfo.address,
        lastContact: payload.receivedAt,
      };
    });

    // Step 3: Process Attachments (if any)
    let attachmentAnalysis = null;
    if (payload.attachments?.length) {
      attachmentAnalysis = await io.runTask("process-attachments", async () => {
        logger.info(`📎 Processing ${payload.attachments!.length} attachments`);
        
        // Process each attachment
        const results = [];
        for (const attachment of payload.attachments!) {
          // Simulate attachment processing
          results.push({
            filename: attachment,
            type: 'image',
            analysis: 'HVAC equipment photo detected',
          });
        }
        
        return results;
      });
    }

    // Step 4: Update Customer Profile
    const customerUpdate = await io.runTask("update-customer", async () => {
      logger.info(`💾 Updating customer profile`);
      
      // Simulate database update
      return {
        customerId: 'cust_123',
        updated: true,
        fieldsUpdated: ['lastContact', 'communicationHistory'],
      };
    });

    // Step 5: Generate Automated Response (if needed)
    let responseGenerated = false;
    if (aiAnalysis.requiresResponse) {
      responseGenerated = await io.runTask("generate-response", async () => {
        logger.info(`📧 Generating automated response`);
        
        // Simulate email response generation
        const emailTemplate = {
          to: payload.sender,
          subject: `Re: ${payload.subject}`,
          template: 'service_acknowledgment',
          data: {
            customerName: aiAnalysis.extractedInfo.customerName,
            serviceType: aiAnalysis.extractedInfo.serviceType,
            estimatedResponseTime: '24 hours',
          },
        };
        
        // Send email (simulate)
        logger.info(`📤 Sending response email to ${payload.sender}`);
        return true;
      });
    }

    logger.info(`✅ Email analysis completed for: ${payload.emailId}`);

    return {
      success: true,
      emailId: payload.emailId,
      analysis: aiAnalysis,
      customerUpdated: customerUpdate.updated,
      responseGenerated,
      attachmentsProcessed: attachmentAnalysis?.length || 0,
      processingTime: Date.now() - new Date(payload.receivedAt).getTime(),
    };
  },
});

export default emailAnalysisWorkflow;