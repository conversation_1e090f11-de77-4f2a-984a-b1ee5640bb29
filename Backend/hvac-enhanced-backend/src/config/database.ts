import { createClient } from '@supabase/supabase-js';
import { MongoClient } from 'mongodb';
import weaviate from 'weaviate-ts-client';
import { createClient as createRedisClient } from 'redis';
import { logger } from '@/utils/logger';

// Supabase configuration
export const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// MongoDB configuration
let mongoClient: MongoClient;

export const connectMongoDB = async () => {
  try {
    mongoClient = new MongoClient(process.env.MONGODB_URI!);
    await mongoClient.connect();
    logger.info('✅ Connected to MongoDB');
    return mongoClient.db('hvac_crm');
  } catch (error) {
    logger.error('❌ MongoDB connection failed:', error);
    throw error;
  }
};

// Weaviate configuration
export const weaviateClient = weaviate.client({
  scheme: 'http',
  host: process.env.WEAVIATE_URL?.replace('http://', '') || 'localhost:8080',
});

// Redis configuration
export const redisClient = createRedisClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
});

export const connectRedis = async () => {
  try {
    await redisClient.connect();
    logger.info('✅ Connected to Redis');
  } catch (error) {
    logger.error('❌ Redis connection failed:', error);
    throw error;
  }
};

// Initialize all database connections
export const initializeDatabases = async () => {
  await Promise.all([
    connectMongoDB(),
    connectRedis(),
  ]);
  
  logger.info('🗄️ All databases initialized');
};