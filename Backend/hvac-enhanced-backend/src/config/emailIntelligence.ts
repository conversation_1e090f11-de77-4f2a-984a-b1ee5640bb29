/**
 * Enhanced Email Intelligence Configuration
 * Integrated from existing email_intelligence system
 */

export interface EmailIntelligenceConfig {
  // AI Models Configuration
  openai: {
    apiKey: string;
    model: string;
    maxTokens: number;
  };
  
  // Whisper Configuration for transcription
  whisper: {
    model: string;
    language: string;
    temperature: number;
  };
  
  // Weaviate Vector Database
  weaviate: {
    url: string;
    apiKey?: string;
    classPrefix: string;
  };
  
  // Email Processing
  email: {
    imapHost: string;
    imapPort: number;
    imapUser: string;
    imapPassword: string;
    processingInterval: number;
  };
  
  // HVAC Specific Configuration
  hvac: {
    supportedBrands: string[];
    serviceTypes: string[];
    urgencyKeywords: string[];
    polishTerminology: Record<string, string>;
  };
}

export const emailIntelligenceConfig: EmailIntelligenceConfig = {
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    model: process.env.OPENAI_MODEL || 'gpt-4-turbo-preview',
    maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
  },
  
  whisper: {
    model: process.env.WHISPER_MODEL || 'medium',
    language: 'pl',
    temperature: 0.1,
  },
  
  weaviate: {
    url: process.env.WEAVIATE_URL || 'http://localhost:8080',
    apiKey: process.env.WEAVIATE_API_KEY,
    classPrefix: 'HVAC2025',
  },
  
  email: {
    imapHost: process.env.IMAP_HOST || 'imap.gmail.com',
    imapPort: parseInt(process.env.IMAP_PORT || '993'),
    imapUser: process.env.IMAP_USER || '',
    imapPassword: process.env.IMAP_PASSWORD || '',
    processingInterval: parseInt(process.env.EMAIL_PROCESSING_INTERVAL || '60000'), // 1 minute
  },
  
  hvac: {
    supportedBrands: [
      'Daikin', 'LG', 'Mitsubishi', 'Carrier', 'Toshiba', 
      'Panasonic', 'Samsung', 'Fujitsu', 'Gree', 'Haier'
    ],
    serviceTypes: [
      'instalacja', 'serwis', 'naprawa', 'konserwacja', 
      'przegląd', 'czyszczenie', 'dezynfekcja', 'modernizacja'
    ],
    urgencyKeywords: [
      'pilne', 'awaria', 'nie działa', 'zepsuty', 'nagły', 
      'natychmiast', 'dzisiaj', 'emergency', 'urgent'
    ],
    polishTerminology: {
      'klimatyzacja': 'air conditioning',
      'wentylacja': 'ventilation',
      'pompa ciepła': 'heat pump',
      'chłodzenie': 'cooling',
      'ogrzewanie': 'heating',
      'filtr': 'filter',
      'czynnik chłodniczy': 'refrigerant',
      'kompresor': 'compressor',
      'parownik': 'evaporator',
      'skraplacz': 'condenser',
    },
  },
};