import {
  Html,
  Head,
  Body,
  Container,
  Img,
  Text,
  Section,
  Row,
  Column,
} from '@react-email/components';

interface HVACHeaderProps {
  logoUrl?: string;
  companyName?: string;
  cosmicTheme?: boolean;
}

export const HVACHeader = ({ 
  logoUrl = "/logo.png", 
  companyName = "Fulmark HVAC",
  cosmicTheme = true 
}: HVACHeaderProps) => {
  const headerStyle = {
    backgroundColor: cosmicTheme ? '#1890ff' : '#ffffff',
    padding: '20px 0',
    borderBottom: cosmicTheme ? '3px solid #fa8c16' : '1px solid #e0e0e0',
  };

  const logoStyle = {
    width: '200px',
    height: 'auto',
    margin: '0 auto',
  };

  const companyNameStyle = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: cosmicTheme ? '#ffffff' : '#1890ff',
    textAlign: 'center' as const,
    margin: '10px 0 5px 0',
  };

  const taglineStyle = {
    fontSize: '14px',
    color: cosmicTheme ? '#ffffff' : '#666666',
    textAlign: 'center' as const,
    margin: '0',
  };

  return (
    <Section style={headerStyle}>
      <Container>
        <Row>
          <Column align="center">
            <Img src={logoUrl} alt={companyName} style={logoStyle} />
            <Text style={companyNameStyle}>{companyName}</Text>
            <Text style={taglineStyle}>
              Profesjonalne usługi klimatyzacyjne
            </Text>
          </Column>
        </Row>
      </Container>
    </Section>
  );
};