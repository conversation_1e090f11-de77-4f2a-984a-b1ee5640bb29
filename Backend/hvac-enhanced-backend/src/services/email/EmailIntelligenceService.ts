import { render } from '@react-email/render';
import { emailIntelligenceConfig } from '@/config/emailIntelligence';
import { logger } from '@/utils/logger';
import { TriggerClient } from '@trigger.dev/sdk';

export interface EmailAnalysisResult {
  sentiment: 'positive' | 'negative' | 'neutral';
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  extractedInfo: {
    customerName?: string;
    phoneNumber?: string;
    address?: string;
    serviceType?: string;
    urgency?: string;
    equipmentBrand?: string;
    equipmentModel?: string;
  };
  requiresResponse: boolean;
  confidence: number;
  suggestedActions: string[];
}

export interface EmailData {
  id: string;
  sender: string;
  subject: string;
  content: string;
  attachments?: string[];
  receivedAt: string;
}

export class EmailIntelligenceService {
  private triggerClient: TriggerClient;

  constructor(triggerClient: TriggerClient) {
    this.triggerClient = triggerClient;
  }

  /**
   * Process incoming email with AI analysis
   */
  async processEmail(emailData: EmailData): Promise<{ workflowId: string }> {
    try {
      logger.info(`📧 Processing email: ${emailData.id} from ${emailData.sender}`);

      // Trigger email analysis workflow
      const workflowRun = await this.triggerClient.sendEvent({
        name: 'email.received',
        payload: emailData,
      });

      logger.info(`⚡ Email analysis workflow triggered: ${workflowRun.id}`);

      return { workflowId: workflowRun.id };
    } catch (error) {
      logger.error('❌ Failed to process email:', error);
      throw error;
    }
  }

  /**
   * Analyze email content with AI
   */
  async analyzeEmailContent(content: string, subject: string): Promise<EmailAnalysisResult> {
    try {
      logger.info('🤖 Analyzing email content with AI');

      // Simulate AI analysis - replace with actual OpenAI/Bielik call
      const analysis: EmailAnalysisResult = {
        sentiment: this.detectSentiment(content),
        category: this.categorizeEmail(content, subject),
        priority: this.determinePriority(content, subject),
        extractedInfo: this.extractInformation(content),
        requiresResponse: this.shouldRespond(content, subject),
        confidence: 0.85,
        suggestedActions: this.generateSuggestedActions(content, subject),
      };

      logger.info(`✅ Email analysis completed with ${analysis.confidence} confidence`);
      return analysis;
    } catch (error) {
      logger.error('❌ Email analysis failed:', error);
      throw error;
    }
  }

  /**
   * Generate automated email response
   */
  async generateResponse(
    analysis: EmailAnalysisResult,
    originalEmail: EmailData
  ): Promise<{ html: string; subject: string; template: string }> {
    try {
      logger.info('📝 Generating automated email response');

      // Select appropriate email template based on analysis
      const templateData = {
        customerName: analysis.extractedInfo.customerName || 'Szanowny Kliencie',
        serviceType: analysis.extractedInfo.serviceType || 'usługi HVAC',
        priority: analysis.priority,
        estimatedResponseTime: this.getEstimatedResponseTime(analysis.priority),
      };

      // Generate response using React Email template
      const emailHtml = await this.renderEmailTemplate(analysis.category, templateData);

      return {
        html: emailHtml,
        subject: `Re: ${originalEmail.subject}`,
        template: analysis.category,
      };
    } catch (error) {
      logger.error('❌ Failed to generate email response:', error);
      throw error;
    }
  }

  private detectSentiment(content: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['dziękuję', 'świetnie', 'doskonale', 'polecam', 'zadowolony'];
    const negativeWords = ['problem', 'awaria', 'nie działa', 'zły', 'niezadowolony'];

    const lowerContent = content.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerContent.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerContent.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private categorizeEmail(content: string, subject: string): string {
    const text = (content + ' ' + subject).toLowerCase();

    if (text.includes('awaria') || text.includes('nie działa')) return 'emergency_service';
    if (text.includes('serwis') || text.includes('przegląd')) return 'service_request';
    if (text.includes('instalacja') || text.includes('montaż')) return 'installation_request';
    if (text.includes('oferta') || text.includes('wycena')) return 'quote_request';
    if (text.includes('faktura') || text.includes('płatność')) return 'billing_inquiry';

    return 'general_inquiry';
  }

  private determinePriority(content: string, subject: string): 'low' | 'medium' | 'high' | 'urgent' {
    const text = (content + ' ' + subject).toLowerCase();
    const urgentKeywords = emailIntelligenceConfig.hvac.urgencyKeywords;

    if (urgentKeywords.some(keyword => text.includes(keyword))) return 'urgent';
    if (text.includes('awaria') || text.includes('nie działa')) return 'high';
    if (text.includes('serwis') || text.includes('naprawa')) return 'medium';

    return 'low';
  }

  private extractInformation(content: string): EmailAnalysisResult['extractedInfo'] {
    // Simple regex patterns for Polish data extraction
    const phoneRegex = /(\+48\s?)?(\d{3}[\s-]?\d{3}[\s-]?\d{3})/g;
    const nameRegex = /(?:nazywam się|jestem|imię)\s+([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+(?:\s+[A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+)?)/i;

    const phoneMatch = content.match(phoneRegex);
    const nameMatch = content.match(nameRegex);

    return {
      phoneNumber: phoneMatch?.[0],
      customerName: nameMatch?.[1],
      serviceType: this.extractServiceType(content),
      equipmentBrand: this.extractEquipmentBrand(content),
    };
  }

  private extractServiceType(content: string): string | undefined {
    const serviceTypes = emailIntelligenceConfig.hvac.serviceTypes;
    const lowerContent = content.toLowerCase();

    return serviceTypes.find(type => lowerContent.includes(type));
  }

  private extractEquipmentBrand(content: string): string | undefined {
    const brands = emailIntelligenceConfig.hvac.supportedBrands;
    const lowerContent = content.toLowerCase();

    return brands.find(brand => lowerContent.includes(brand.toLowerCase()));
  }

  private shouldRespond(content: string, subject: string): boolean {
    const text = (content + ' ' + subject).toLowerCase();
    
    // Don't respond to automated emails
    if (text.includes('noreply') || text.includes('no-reply')) return false;
    
    // Respond to service requests and inquiries
    return text.includes('?') || 
           text.includes('proszę') || 
           text.includes('potrzebuję') ||
           text.includes('awaria');
  }

  private generateSuggestedActions(content: string, subject: string): string[] {
    const actions: string[] = [];
    const text = (content + ' ' + subject).toLowerCase();

    if (text.includes('awaria')) {
      actions.push('Przydziel technika na pilną wizytę');
      actions.push('Skontaktuj się telefonicznie w ciągu 2 godzin');
    }

    if (text.includes('oferta') || text.includes('wycena')) {
      actions.push('Przygotuj szczegółową ofertę');
      actions.push('Zaplanuj wizytę w celu wyceny');
    }

    if (text.includes('serwis')) {
      actions.push('Zaplanuj wizytę serwisową');
      actions.push('Sprawdź historię urządzenia');
    }

    return actions;
  }

  private getEstimatedResponseTime(priority: string): string {
    switch (priority) {
      case 'urgent': return '2 godziny';
      case 'high': return '4 godziny';
      case 'medium': return '24 godziny';
      default: return '48 godzin';
    }
  }

  private async renderEmailTemplate(category: string, data: any): Promise<string> {
    // This would use actual React Email templates
    // For now, return a simple HTML template
    return `
      <html>
        <body style="font-family: Arial, sans-serif;">
          <h2>Dziękujemy za kontakt - Fulmark HVAC</h2>
          <p>Szanowny/a ${data.customerName},</p>
          <p>Otrzymaliśmy Państwa wiadomość dotyczącą ${data.serviceType}.</p>
          <p>Skontaktujemy się w ciągu ${data.estimatedResponseTime}.</p>
          <p>Z poważaniem,<br>Zespół Fulmark HVAC</p>
        </body>
      </html>
    `;
  }
}