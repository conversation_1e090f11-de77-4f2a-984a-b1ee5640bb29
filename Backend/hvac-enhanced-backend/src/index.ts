import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { TriggerClient } from '@trigger.dev/sdk';
import { createExpressServer } from '@trigger.dev/express';

import { logger } from '@/utils/logger';
import { errorHandler } from '@/api/middleware/errorHandler';
import { rateLimiter } from '@/api/middleware/rateLimiter';
import { authMiddleware } from '@/api/middleware/auth';

// Import routes
import emailRoutes from '@/api/routes/email';
import workflowRoutes from '@/api/routes/workflow';
import healthRoutes from '@/api/routes/health';

// Import workflows
import '@/workflows/email-processing/EmailAnalysisWorkflow';
import '@/workflows/service-automation/ServiceOrderWorkflow';
import '@/workflows/ai-processing/BielikAnalysisWorkflow';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Initialize Trigger.dev client
const triggerClient = new TriggerClient({
  id: process.env.TRIGGER_PROJECT_ID || 'hvac-crm',
  apiKey: process.env.TRIGGER_API_KEY!,
  apiUrl: process.env.TRIGGER_API_URL,
});

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
}));

// Rate limiting
app.use(rateLimiter);

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Trigger.dev integration
const triggerApp = createExpressServer(triggerClient);
app.use('/api/trigger', triggerApp);

// API routes
app.use('/api/health', healthRoutes);
app.use('/api/email', authMiddleware, emailRoutes);
app.use('/api/workflow', authMiddleware, workflowRoutes);

// Error handling
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  logger.info(`🚀 HVAC Enhanced Backend running on port ${PORT}`);
  logger.info(`📧 Email templates available at /api/email`);
  logger.info(`⚡ Workflows available at /api/workflow`);
  logger.info(`🔧 Trigger.dev webhooks at /api/trigger`);
});

export default app;