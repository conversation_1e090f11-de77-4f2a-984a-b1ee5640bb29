version: '3.8'

services:
  hvac-backend:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
    env_file:
      - .env
    depends_on:
      - redis
      - mongodb
      - weaviate
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - hvac-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - hvac-network

  mongodb:
    image: mongo:7
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=hvac_crm
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped
    networks:
      - hvac-network

  weaviate:
    image: semitechnologies/weaviate:1.25.0
    ports:
      - "8080:8080"
    environment:
      - QUERY_DEFAULTS_LIMIT=25
      - AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true
      - PERSISTENCE_DATA_PATH=/var/lib/weaviate
      - DEFAULT_VECTORIZER_MODULE=none
      - ENABLE_MODULES=text2vec-openai,generative-openai
    volumes:
      - weaviate_data:/var/lib/weaviate
    restart: unless-stopped
    networks:
      - hvac-network

volumes:
  redis_data:
  mongodb_data:
  weaviate_data:

networks:
  hvac-network:
    driver: bridge